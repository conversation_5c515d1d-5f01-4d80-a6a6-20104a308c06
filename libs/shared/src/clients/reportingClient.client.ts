import { Inject, Injectable, Logger } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { AvailableClients } from '@tangopay/shared/grpc-clients';
import { unpackResponse } from './utils';
import { SCHEDULED_REPORT_SERVICE_NAME, ScheduledReportServiceClient } from 'apps/reporting/src/proto/reporting/scheduled.pb';
import { REPORT_DATA_SERVICE_NAME, ReportDataServiceClient } from 'apps/reporting/src/proto/reporting/data.pb';
import { REPORT_GENERATION_SERVICE_NAME, ReportGenerationServiceClient } from 'apps/reporting/src/proto/reporting/generate.pb';
import { REPORT_DETAILED_DATA_SERVICE_NAME, ReportDetailedDataServiceClient } from 'apps/reporting/src/proto/reporting/detailed-data.pb';
import { REPORT_BUDGET_SERVICE_NAME, ReportBudgetServiceClient } from 'apps/reporting/src/proto/reporting/budget.pb';
import { AddRecipientRequest, DeleteRecipientRequest, REPORT_MAILING_SERVICE_NAME, ReportMailingServiceClient } from 'apps/reporting/src/proto/reporting/mailing.pb';
import { Resolution } from 'apps/reporting/src/entities/CachedBusinessStats.entity';
import { REPORT_FORECASTING_SERVICE_NAME, ReportForecastingServiceClient } from 'apps/reporting/src/proto/reporting/forecasting.pb';
import { IdWithDateRequest } from 'apps/reporting/src/proto/reporting/shared.pb';
import { REPORT_DATA_HARVEST_SERVICE_NAME, ReportDataHarvestServiceClient } from 'apps/reporting/src/proto/reporting/harvest.pb';
import { REVIEWS_SERVICE_NAME, ReviewsServiceClient } from 'apps/reporting/src/proto/reporting/reviews.pb';

@Injectable()
export class ReportingClient {
  constructor(
    @Inject(AvailableClients.REPORTING_PACKAGE.packageName)
    private client: ClientGrpc,
  ) {}

  private service: ScheduledReportServiceClient;
  private dataService: ReportDataServiceClient;
  private generateService: ReportGenerationServiceClient;
  private detailedDataService: ReportDetailedDataServiceClient;
  private budgetService: ReportBudgetServiceClient;
  private mailingService: ReportMailingServiceClient;
  private forecastingService: ReportForecastingServiceClient;
  private harvestService: ReportDataHarvestServiceClient;
  private reviewService: ReviewsServiceClient;

  private readonly logger = new Logger(ReportingClient.name);

  onModuleInit() {
    this.service = this.client.getService<ScheduledReportServiceClient>(SCHEDULED_REPORT_SERVICE_NAME);
    this.dataService = this.client.getService<ReportDataServiceClient>(REPORT_DATA_SERVICE_NAME);
    this.generateService = this.client.getService<ReportGenerationServiceClient>(REPORT_GENERATION_SERVICE_NAME);
    this.detailedDataService = this.client.getService<ReportDetailedDataServiceClient>(REPORT_DETAILED_DATA_SERVICE_NAME);
    this.budgetService = this.client.getService<ReportBudgetServiceClient>(REPORT_BUDGET_SERVICE_NAME);
    this.mailingService = this.client.getService<ReportMailingServiceClient>(REPORT_MAILING_SERVICE_NAME);
    this.forecastingService = this.client.getService<ReportForecastingServiceClient>(REPORT_FORECASTING_SERVICE_NAME);
    this.harvestService = this.client.getService<ReportDataHarvestServiceClient>(REPORT_DATA_HARVEST_SERVICE_NAME);
    this.reviewService = this.client.getService<ReviewsServiceClient>(REVIEWS_SERVICE_NAME);
  }

  async triggerDailyScheduledReports(injectedLogger?: Logger) {
    return unpackResponse(this.service.triggerDailyScheduledReports(null), 'triggering daily reports', injectedLogger ?? this.logger);
  }
  async triggerMonthlyScheduledReports(injectedLogger?: Logger) {
    return unpackResponse(this.service.triggerMonthlyScheduledReports(null), 'triggering monthly schedule reports', injectedLogger ?? this.logger);
  }
  async triggerWeeklyScheduledReports(injectedLogger?: Logger) {
    return unpackResponse(this.service.triggerWeeklyScheduledReports(null), 'triggering weekly reports', injectedLogger ?? this.logger);
  }
  async getStatsForBusiness(businessId: string, startDate: string, endDate: string, injectedLogger?: Logger) {
    return unpackResponse(
      this.dataService.getStatsForBusiness({
        id: businessId,
        startDate,
        endDate,
      }),
      'Get stats for business',
      injectedLogger ?? this.logger,
    );
  }

  async getStatsForStaffMember(staffId: string, startDate: string, endDate: string, injectedLogger?: Logger) {
    return unpackResponse(
      this.dataService.getStatsForStaffMember({
        id: staffId,
        startDate,
        endDate,
      }),
      'Get stats for staff member',
      injectedLogger ?? this.logger,
    );
  }
  async getStatsForThirdParty(businessId: string, startDate: string, endDate: string, injectedLogger?: Logger) {
    return unpackResponse(
      this.dataService.getStatsForBusiness({
        id: businessId,
        startDate,
        endDate,
        query: 'thirdParty=true',
      }),
      'Get stats for business',
      injectedLogger ?? this.logger,
    );
  }

  async getDetailedStatsForBusiness(businessId: string, startDate: string, endDate: string, injectedLogger?: Logger) {
    return unpackResponse(
      this.detailedDataService.getDetailedStatsForBusiness({
        id: businessId,
        startDate,
        endDate,
      }),
      'Get detailed stats for business',
      injectedLogger ?? this.logger,
    );
  }

  async getIncrementalisedDetailedStatsForBusiness(
    businessId: string,
    startDate: string,
    endDate: string,
    cadence: 'quart-hourly' | 'hourly' | 'daily' | 'weekly' | 'monthly' = 'quart-hourly',
    averagePeriod: 'week' | 'period' = 'week',
    injectedLogger?: Logger,
  ) {
    return unpackResponse(
      this.detailedDataService.getIncrementalisedDetailedStatsForBusiness({
        id: businessId,
        startDate,
        endDate,
        query: `cadence=${cadence}&averagePeriod=${averagePeriod}`,
      }),
      'Get incrementalised detailed stats for business',
      injectedLogger ?? this.logger,
    );
  }

  async getIncrementalisedAverageStatsForBusiness(
    businessId: string,
    startDate: string,
    endDate: string,
    increments: Resolution,
    samples: number,
    injectedLogger?: Logger,
  ) {
    return unpackResponse(
      this.detailedDataService.getIncrementalisedAverageStatsForBusiness({
        id: businessId,
        startDate,
        endDate,
        query: `increments=${increments}&samples=${samples}`,
      }),
      'Get incrementalised average stats for business',
      injectedLogger ?? this.logger,
    );
  }

  async getDetailedStatsForStaffMember(staffId: string, startDate: string, endDate: string, injectedLogger?: Logger) {
    return unpackResponse(
      this.detailedDataService.getDetailedStatsForStaffMember({
        id: staffId,
        startDate,
        endDate,
      }),
      'Get detailed stats for staff member',
      injectedLogger ?? this.logger,
    );
  }

  async bustCache(businessId: string, start: Date, end: Date, injectedLogger?: Logger) {
    return unpackResponse(
      this.detailedDataService.bustCacheForBusiness({
        id: businessId,
        startDate: start.toISOString(),
        endDate: end.toISOString(),
      }),
      'Busting cache',
      injectedLogger ?? this.logger,
    );
  }

  async cleanAndBuildStatsForBusiness(businessId: string, start: Date, end: Date, injectedLogger?: Logger) {
    return unpackResponse(
      this.detailedDataService.cleanAndBuildStatsForBusiness({
        id: businessId,
        startDate: start.toISOString(),
        endDate: end.toISOString(),
      }),
      'Cleaning and building stats for business',
      injectedLogger ?? this.logger,
      true,
    );
  }

  async sendNotifications() {
    return unpackResponse(this.generateService.sendReportingNotifications({}), 'sending notifications', this.logger);
  }

  async getDailyProjections(businessId: string, startDate: string, endDate: string, injectedLogger?: Logger) {
    return unpackResponse(
      this.budgetService.getSalesProjection({
        id: businessId,
        startDate,
        endDate,
      }),
      'Get daily projections',
      injectedLogger ?? this.logger,
    );
  }

  async getCurrentBudget(businessId: string, injectedLogger?: Logger) {
    return unpackResponse(this.budgetService.getCurrentBudget({ id: businessId }), 'Get current budget', injectedLogger ?? this.logger);
  }

  async getRecipientsByEmail(email: string, injectedLogger?: Logger) {
    return unpackResponse(this.mailingService.getRecipientsByEmail({ email }), 'Get recipients by email', injectedLogger ?? this.logger);
  }

  async getLabourMarginReport(businessId: string, startDate: Date, endDate: Date, cadence: 'hourly' | 'daily' | 'weekly' | 'monthly', injectedLogger?: Logger) {
    return unpackResponse(
      this.generateService.labourMarginReport({
        id: businessId,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        cadence,
      }),
      'Get labour margin report',
      injectedLogger ?? this.logger,
    );
  }

  async getDailySalesReport(businessId: string, startDate: Date, endDate: Date, query: string, injectedLogger?: Logger) {
    return unpackResponse(
      this.generateService.dailySalesReport({
        id: businessId,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        query,
      }),
      'Get daily sales report',
      injectedLogger ?? this.logger,
    );
  }

  async getSalesMixReport(businessId: string, startDate: Date, endDate: Date, injectedLogger?: Logger) {
    return unpackResponse(
      this.generateService.salesMixReport({
        id: businessId,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      }),
      'Get sales mix report',
      injectedLogger ?? this.logger,
    );
  }

  async getRecipientsForBusiness(businessId: string, injectedLogger?: Logger) {
    return unpackResponse(this.mailingService.getRecipientsForBusiness({ id: businessId }), 'Get recipients for business', injectedLogger ?? this.logger);
  }

  async addRecipientToReport(recipient: AddRecipientRequest, injectedLogger?: Logger) {
    return unpackResponse(this.mailingService.addRecipient(recipient), 'Add recipient to report', injectedLogger ?? this.logger);
  }

  async deleteRecipientBySearch(request: DeleteRecipientRequest, injectedLogger?: Logger) {
    return unpackResponse(this.mailingService.deleteRecipientBySearch(request), 'Delete recipient by search', injectedLogger ?? this.logger);
  }

  async dispatchEmails(injectedLogger?: Logger) {
    return unpackResponse(this.mailingService.dispatchEmails({ temporaryRecipients: [] }), 'Dispatch emails', injectedLogger ?? this.logger);
  }

  async getBusinessForecast(request: IdWithDateRequest, injectedLogger?: Logger) {
    return unpackResponse(this.forecastingService.getBusinessForecast(request), 'Get business forecast', injectedLogger ?? this.logger);
  }

  async createNewJobsForSchedules(injectedLogger?: Logger) {
    return unpackResponse(this.harvestService.createNewJobsForSchedules({}), 'Create new jobs for schedules', injectedLogger ?? this.logger);
  }

  async refreshReviews(businessId: string, injectedLogger?: Logger) {
    return unpackResponse(this.reviewService.refreshReviews({ id: businessId }), 'Refresh reviews', injectedLogger ?? this.logger);
  }
}
