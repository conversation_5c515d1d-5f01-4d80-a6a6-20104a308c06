import { HttpStatus } from '@nestjs/common';
import { RpcException } from '@nestjs/microservices';

export default class TangoException extends RpcException {
  rawMessage: string;
  rawType: string;
  rawStatus: HttpStatus;
  constructor(message: string, type = 'ERROR', status = HttpStatus.INTERNAL_SERVER_ERROR) {
    super({
      error: {
        type,
        message,
      },
      status,
    });
    // for the old error format
    this.rawType = type;
    this.rawMessage = message;
    this.rawStatus = status;
  }
}

// GENERAL
export class NotImplementedException extends TangoException {
  constructor() {
    super(`Not implemented`, 'NOT_IMPLEMENTED', HttpStatus.NOT_IMPLEMENTED);
  }
}
export class FeatureDisabledException extends TangoException {
  constructor(feature: string) {
    super(`Feature ${feature} is not enabled`, 'FEATURE_DISABLED', HttpStatus.BAD_REQUEST);
  }
}
export class NoPublicAccessException extends TangoException {
  constructor(action: string) {
    super(`Must be authenticated to ${action}`, 'ACTION_NOT_ALLOWED', HttpStatus.FORBIDDEN);
  }
}

// BUSINESS
export class EnterpriseNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Enterprise ${id} not found`, 'ENTERPRISE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class EnterpriseNameInUseException extends TangoException {
  constructor(name: string) {
    super(`Enterprise name ${name} is already in use`, 'ENTERPRISE_NAME_IN_USE', HttpStatus.NOT_FOUND);
  }
}
export class ErrorSavingEnterpriseException extends TangoException {
  constructor() {
    super(`Error saving enterprise`, 'ERROR_SAVING_ENTERPRISE', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}

export class BusinessNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Business ${id} not found`, 'BUSINESS_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class BusinessNotFoundForTerminal extends TangoException {
  constructor(terminal: string) {
    super(`Business not found for terminal ${terminal}`, 'BUSINESS_NOT_FOUND_FOR_TERMINAL', HttpStatus.NOT_FOUND);
  }
}
export class DeliverectBusinessNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Business with Deliverect Location id ${id} not found`, 'BUSINESS_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class EnterpriseBusinessesNotFoundException extends TangoException {
  constructor(enterprise: string) {
    super(`Businesses for enterprise ${enterprise} not found`, 'BUSINESS_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class BusinessSettingsNotFoundException extends TangoException {
  constructor(businessId: string) {
    super(`Business settings for Business ${businessId} not found`, 'BUSINESS_SETTINGS_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class BusinessNotLegacyException extends TangoException {
  constructor(business: string) {
    super(`Business ${business} is not a legacy business`, 'BUSINESS_NOT_LEGACY', HttpStatus.BAD_REQUEST);
  }
}
export class BusinessLocationNotFoundException extends TangoException {
  constructor(name: string) {
    super(`Location for Business ${name} not found`, 'BUSINESS_LOCATION_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class ReviewSettingsNotFoundForLocationException extends TangoException {
  constructor(locationId: string) {
    super(`Ratings and review settings for ReviewTracker location id ${locationId} cannot be found`, 'REVIEW_SETTINGS_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class TaxNotFoundException extends TangoException {
  constructor(tax: string) {
    super(`Tax ${tax} not found`, 'TAX_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class ProcessingRateNotFoundException extends TangoException {
  constructor(business: string | null) {
    if (business) {
      super(`Processing rate not found for business ${business}`, 'PROCESSING_RATE_NOT_FOUND', HttpStatus.NOT_FOUND);
    } else {
      super(`Processing rate not found`, 'PROCESSING_RATE_NOT_FOUND', HttpStatus.NOT_FOUND);
    }
  }
}
export class ServiceFeeNotFoundException extends TangoException {
  constructor(fee: string) {
    super(`Service fee ${fee} not found`, 'SERVICE_FEE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class ServiceFeeRuleNotFoundException extends TangoException {
  constructor(rule: string) {
    super(`Service fee rule ${rule} not found`, 'SERVICE_FEE_RULE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class ServiceFeeIsOpenException extends TangoException {
  constructor(fee: string) {
    super(`Service fee ${fee} is an open fee`, 'SERVICE_FEE_OPEN', HttpStatus.BAD_REQUEST);
  }
}
export class ServiceFeeIsNotOpenException extends TangoException {
  constructor(fee: string) {
    super(`Service fee ${fee} is not an open fee`, 'SERVICE_FEE_NOT_OPEN', HttpStatus.BAD_REQUEST);
  }
}
export class OpenFeeWithRateException extends TangoException {
  constructor() {
    super(`Service fees cannot have set rates if they are open`, 'OPEN_FEE_WITH_RATE', HttpStatus.BAD_REQUEST);
  }
}
export class MissingRateException extends TangoException {
  constructor() {
    super(`Service fees which are not open must have a rate`, 'MISSING_RATE', HttpStatus.BAD_REQUEST);
  }
}
export class ContractNotFoundException extends TangoException {
  constructor(contract: string) {
    super(`Contract ${contract} not found`, 'CONTRACT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class ContractAlreadySignedException extends TangoException {
  constructor(contract: string) {
    super(`Contract ${contract} has already been signed`, 'CONTRACT_ALREADY_SIGNED', HttpStatus.CONFLICT);
  }
}
export class ContractNotSavedException extends TangoException {
  constructor(contract: string) {
    super(`Failed to save contract ${contract}`, 'CONTRACT_NOT_SAVED', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}
export class ContractNotCreatedException extends TangoException {
  constructor() {
    super(`Failed to create contract`, 'CONTRACT_NOT_CREATED', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}
export class ScheduleNotRetrievedException extends TangoException {
  constructor(business: string) {
    super(`Unable to retrieve order schedule for Business ${business}`, 'SCHEDULE_NOT_RETRIEVED', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}
export class BusinessNameInUseException extends TangoException {
  constructor(businessName: string) {
    super(`Business name ${businessName} is already in use`, 'BUSINESS_NAME_IN_USE', HttpStatus.CONFLICT);
  }
}
export class NoCountryForBusinessException extends TangoException {
  constructor(businessName: string) {
    super(`Business ${businessName} has no country`, 'BUSINESS_NO_COUNTRY', HttpStatus.BAD_REQUEST);
  }
}
export class ErrorSavingBusinessException extends TangoException {
  constructor() {
    super(`Failed to save business`, 'ERROR_SAVING_BUSINESS', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}

export class HelpGuideNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Help guide with id ${id} was not found`, 'HELP_GUIDE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class InvalidBusinessAddressException extends TangoException {
  constructor() {
    super(`Business address format is invalid`, 'INVALID_BUSINESS_ADDRESS', HttpStatus.BAD_REQUEST);
  }
}
export class GenerateLocationException extends TangoException {
  constructor() {
    super(`Error generating location`, 'ERROR_CONFIGURING_STRIPE', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}
export class LinkToLocationException extends TangoException {
  constructor() {
    super(`Error linking generated code to location`, 'ERROR_CONFIGURING_STRIPE', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}
export class CreateDeployGroupException extends TangoException {
  constructor() {
    super(`Error creating deploy group`, 'ERROR_CONFIGURING_STRIPE', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}
export class AddConfigurationException extends TangoException {
  constructor() {
    super(`Error creating stripe config`, 'ERROR_CONFIGURING_STRIPE', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}
export class AddConfigurationToLocationException extends TangoException {
  constructor() {
    super(`Error attaching stripe config to location`, 'ERROR_CONFIGURING_STRIPE', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}
export class UpdateConfigurationException extends TangoException {
  constructor(msg: string) {
    super(`Error updating stripe config: ${msg}`, 'ERROR_CONFIGURING_STRIPE', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}
export class PayoutNotFoundException extends TangoException {
  constructor(payout: string) {
    super(`Payout ${payout} not found`, 'PAYOUT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class PayoutNotInProgressException extends TangoException {
  constructor(payout: string) {
    super(`Payout ${payout} is not in progress`, 'PAYOUT_NOT_IN_PROGRESS', HttpStatus.BAD_REQUEST);
  }
}
export class PrinterNotFoundException extends TangoException {
  constructor(printer: string) {
    super(`Printer ${printer} not found`, 'PRINTER_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class PrinterUpdateFailedException extends TangoException {
  constructor(printer: string) {
    super(`Failed to update status for printer ${printer}`, 'PRINTER_UPDATE_FAILED', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}

export class SettingsNotFoundException extends TangoException {
  constructor(business: string) {
    super(`Business settings for Business ${business} not found`, 'SETTINGS_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class POSSettingsNotFoundException extends TangoException {
  constructor() {
    super(`POS settings not found`, 'POS_SETTINGS_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class PrintingSettingsNotFoundException extends TangoException {
  constructor() {
    super(`Printing settings not found`, 'PRINTING_SETTINGS_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class GeneralServiceSettingsNotFoundException extends TangoException {
  constructor() {
    super(`General service settings not found`, 'GENERAL_SERVICE_SETTINGS_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class OnlineOrderingSettingsNotFoundException extends TangoException {
  constructor() {
    super(`Online ordering settings not found`, 'ONLINE_ORDERING_SETTINGS_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class InventoryNotEnabledException extends TangoException {
  constructor() {
    super(`Inventory is not enabled`, 'INVENTORY_NOT_ENABLED', HttpStatus.UNAUTHORIZED);
  }
}
export class BusinessHoursForEveryDayException extends TangoException {
  constructor() {
    super(`Must have business hours for every day`, 'BUSINESS_HOURS_FOR_EVERY_DAY', HttpStatus.BAD_REQUEST);
  }
}
export class TableNotFoundException extends TangoException {
  constructor(table: string | number) {
    super(`Table ${table} not found`, 'TABLE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class TableInUseException extends TangoException {
  constructor(table: string | number, usedBy: string) {
    super(`Table ${table} is in use by ${usedBy}`, 'TABLE_IN_USE', HttpStatus.CONFLICT);
  }
}
export class MultipleServiceAreaException extends TangoException {
  constructor() {
    super(`Tables span multiple service areas`, 'MULTIPLE_SERVICE_AREAS', HttpStatus.BAD_REQUEST);
  }
}
export class NoPhoneNumberException extends TangoException {
  constructor() {
    super(`Phone number not provided`, 'NO_PHONE_NUMBER', HttpStatus.BAD_REQUEST);
  }
}
export class BadPhoneNumberException extends TangoException {
  constructor(phone: string) {
    super(`Phone number ${phone} is not a valid phone number`, 'PHONE_INVALID', HttpStatus.BAD_REQUEST);
  }
}
export class GeocodeFailedException extends TangoException {
  constructor(address: string) {
    super(
      `We could not find the street address ${address} via Google Maps. Try checking that the location can be found using the map on this page: https://developers.google.com/maps/documentation/geocoding/overview`,
      'GEOCODE_FAILED',
      HttpStatus.INTERNAL_SERVER_ERROR,
    );
  }
}

export class ServiceAreaNotFoundException extends TangoException {
  constructor(serviceAreaId: string) {
    super(`Service Area with ID ${serviceAreaId} not found`, 'SERVICE_AREA_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class CannotGetServiceAreasException extends TangoException {
  constructor(businessId: string) {
    super(`Failed to get service areas for business with ID ${businessId}`, 'FAILED_TO_GET_SERVICE_AREAS', HttpStatus.BAD_REQUEST);
  }
}

export class TangoDeviceNotFound extends TangoException {
  constructor(deviceId: string) {
    super(`Failed to find tango devie with ID ${deviceId}`, 'FAILED_TO_FIND_TANGO_DEVICE', HttpStatus.NOT_FOUND);
  }
}

// AUTH
export class SaveUserException extends TangoException {
  constructor() {
    super(`Error saving user`, 'ERROR_SAVING_USER', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}
export class UserConflictException extends TangoException {
  constructor() {
    super(`E-Mail or phone is already in use`, 'EMAIL_CONFLICT', HttpStatus.CONFLICT);
  }
}
export class UserNotFoundException extends TangoException {
  constructor(user: string) {
    super(`User ${user} was not found`, 'USER_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class UserNotLegacyException extends TangoException {
  constructor(userId: string) {
    super(`User ${userId} is not a legacy user`, 'USER_NOT_LEGACY', HttpStatus.BAD_REQUEST);
  }
}
export class PasswordIncorrectException extends TangoException {
  constructor() {
    super(`Password incorrect`, 'PASSWORD_INCORRECT', HttpStatus.FORBIDDEN);
  }
}
export class CodeValidationException extends TangoException {
  constructor() {
    super(`Could not validate code`, 'COULD_NOT_VALIDATE_CODE', HttpStatus.FORBIDDEN);
  }
}
export class InsufficientPermissionsException extends TangoException {
  constructor() {
    super(`User has insufficient permissions`, 'INSUFFICIENT_PERMISSIONS', HttpStatus.FORBIDDEN);
  }
}
export class InvalidTokenException extends TangoException {
  constructor() {
    super(`Token is invalid`, 'INVALID_TOKEN', HttpStatus.FORBIDDEN);
  }
}
export class NoStaffFoundException extends TangoException {
  constructor() {
    super(`Error loading staff`, 'NO_STAFF_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class NoUsersFoundException extends TangoException {
  constructor() {
    super(`Error loading users`, 'NO_USERS_FOUND', HttpStatus.NOT_FOUND);
  }
}

// ORDERING
export class NoAddressException extends TangoException {
  constructor() {
    super(`Could not determine address for delivery`, 'NO_ADDRESS', HttpStatus.BAD_REQUEST);
  }
}

export class TabNotFoundException extends TangoException {
  constructor(tab: string) {
    super(`Tab ${tab} not found`, 'TAB_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class TabNotCompletedException extends TangoException {
  constructor(tab: string) {
    super(`Tab ${tab} is not completed`, 'TAB_NOT_COMPLETED', HttpStatus.BAD_REQUEST);
  }
}
export class TabNotSpecifiedException extends TangoException {
  constructor() {
    super(`Must specify exactly one tab`, 'TAB_NOT_SPECIFIED', HttpStatus.BAD_REQUEST);
  }
}
export class TabNotOpenException extends TangoException {
  constructor(tab: string) {
    super(`Tab ${tab} is not open`, 'TAB_NOT_OPEN', HttpStatus.BAD_REQUEST);
  }
}
export class CompletedTabsNotFoundException extends TangoException {
  constructor(business: string) {
    super(`Completed Tabs for business ${business} not found`, 'TAB_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class NoTabForSessionException extends TangoException {
  constructor(session: string) {
    super(`Tab for Table Session ${session} not found`, 'TAB_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class SeatTabNotAllowedException extends TangoException {
  constructor() {
    super('Single seat tabs are not allowed for this business', 'SEAT_NOT_ALLOWED', HttpStatus.BAD_REQUEST);
  }
}
export class TableNumberRequiredException extends TangoException {
  constructor() {
    super('Must provide a table number', 'TABLE_NUMBER_REQUIRED', HttpStatus.BAD_REQUEST);
  }
}
export class OrderNotFoundException extends TangoException {
  constructor(order: string) {
    super(`Order ${order} not found`, 'ORDER_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class OrderNotDefaultException extends TangoException {
  constructor(order: string) {
    super(`Order ${order} is not a default order`, 'ORDER_NOT_DEFAULT', HttpStatus.BAD_REQUEST);
  }
}
export class DefaultOrderConflictException extends TangoException {
  constructor(tab: string) {
    super(`Tab ${tab} already has a default order`, 'DEFAULT_ORDER_CONFLICT', HttpStatus.CONFLICT);
  }
}
export class InsufficientProductException extends TangoException {
  constructor(productInOrder: string) {
    super(`Product in order ${productInOrder} has insufficient quantity to allow this operation`, 'INSUFFICIENT_PRODUCT', HttpStatus.BAD_REQUEST);
  }
}
export class NoDestinationsException extends TangoException {
  constructor() {
    super(`Product must be moved to at least one destination`, 'NO_DESTINATIONS', HttpStatus.BAD_REQUEST);
  }
}
export class NegativeQuantityException extends TangoException {
  constructor() {
    super(`Product must have a non-negative quantity`, 'NEGATIVE_QUANTITY', HttpStatus.BAD_REQUEST);
  }
}
export class PaymentDeclinedException extends TangoException {
  constructor() {
    super(`Payment could not be completed`, 'PAYMENT_DECLINED', HttpStatus.BAD_REQUEST);
  }
}
export class PaymentInProgressException extends TangoException {
  constructor(payment: string) {
    super(`Payment ${payment} is in progress`, 'PAYMENT_IN_PROGRESS', HttpStatus.CONFLICT);
  }
}
export class PaymentNotFoundException extends TangoException {
  constructor(payment: string) {
    super(`Payment ${payment} not found`, 'PAYMENT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class PaymentSourceNotFoundException extends TangoException {
  constructor(source: string) {
    super(`Payment source ${source} not found`, 'PAYMENT_SOURCE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class PaymentNotPreauthorizedException extends TangoException {
  constructor(payment: string) {
    super(`Payment ${payment} is not a pre-authorized payment`, 'PAYMENT_NOT_PREAUTH', HttpStatus.BAD_REQUEST);
  }
}
export class CouldNotGetPaymentsException extends TangoException {
  constructor(payments: string) {
    super(`Failed to get payments ${payments}`, 'COULD_NOT_GET_PAYMENTS', HttpStatus.NOT_FOUND);
  }
}
export class CouldNotGetProductsPaidException extends TangoException {
  constructor(productsPaid: string) {
    super(`Failed to get productsPaid ${productsPaid}`, 'COULD_NOT_GET_PRODUCTS_PAID', HttpStatus.NOT_FOUND);
  }
}
export class IntentNotAdjustibleException extends TangoException {
  constructor(intent: string) {
    super(`Intent ${intent} is not adjustable`, 'INTENT_NOT_ADJUSTABLE', HttpStatus.BAD_REQUEST);
  }
}
export class TabNotPreauthorizedException extends TangoException {
  constructor(tab: string) {
    super(`Tab ${tab} is not a pre-authorized tab`, 'TAB_NOT_PREAUTH', HttpStatus.BAD_REQUEST);
  }
}
export class PaymentAlreadyCompleteException extends TangoException {
  constructor(tab: string) {
    super(`Tab ${tab} has already completed payment`, 'PAYMENT_ALREADY_COMPLETE', HttpStatus.CONFLICT);
  }
}
export class PaymentAlreadyRevertedException extends TangoException {
  constructor(payment: string) {
    super(`Payment ${payment} is already reverted`, 'PAYMENT_ALREADY_REVERTED', HttpStatus.BAD_REQUEST);
  }
}
export class PaymentNotRefundedException extends TangoException {
  constructor(payment: string) {
    super(`Payment ${payment} is not fully refunded`, 'PAYMENT_NOT_REFUNDED', HttpStatus.BAD_REQUEST);
  }
}
export class PaymentMismatchException extends TangoException {
  constructor(paymentAmount: number, stripeAmount: number) {
    super(`Payment amount ${paymentAmount}, does not match stripe amount of ${stripeAmount}`, 'PAYMENT_MISMATCH', HttpStatus.BAD_REQUEST);
  }
}
export class ProcessingFeeMismatchException extends TangoException {
  constructor(processingFee: number, applicationFee: number) {
    super(`Payment amount processing fee was ${processingFee}, but the stripe application fee was ${applicationFee}`, 'PROCESSING_FEE_MISMATCH', HttpStatus.BAD_REQUEST);
  }
}
export class ProcessingFeeNotAllowedException extends TangoException {
  constructor() {
    super(`This payment cannot have a processing fee`, 'PROCESSING_FEE_NOT_ALLOWED', HttpStatus.BAD_REQUEST);
  }
}
export class ProcessingFeeMissingException extends TangoException {
  constructor() {
    super(`This business requires you to specify a processing fee`, 'PROCESSING_FEE_MISSING', HttpStatus.BAD_REQUEST);
  }
}
export class ExcessRefundException extends TangoException {
  constructor() {
    super(`Refund amount exceeds un-refunded payment amount`, 'EXCESS_REFUND', HttpStatus.BAD_REQUEST);
  }
}
export class NotInteracException extends TangoException {
  constructor(source: string) {
    super(`Source ${source} is not an interac payment`, 'NOT_INTERAC', HttpStatus.BAD_REQUEST);
  }
}
export class SourceNotFoundException extends TangoException {
  constructor(source: string) {
    super(`Payment source ${source} not found`, 'SOURCE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class PaymentIntentNotFoundException extends TangoException {
  constructor(paymentIntent: string) {
    super(`Payment Intent ${paymentIntent} not found`, 'PAYMENT_INTENT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class TerminalNotFoundException extends TangoException {
  constructor(terminal: string) {
    super(`Terminal ${terminal} not found`, 'TERMINAL_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class PreauthDeclinedException extends TangoException {
  constructor() {
    super(`Preauthorization was unsuccessful`, 'PREAUTH_FAILED', HttpStatus.BAD_REQUEST);
  }
}
export class CashEventNotFoundException extends TangoException {
  constructor(cashEvent: string) {
    super(`Cash Event ${cashEvent} not found`, 'CASH_EVENT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class TableSessionNotFoundException extends TangoException {
  constructor(tableSession: string) {
    super(`Table Session ${tableSession} not found`, 'TABLE_SESSION_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class VoidedProductNotFoundException extends TangoException {
  constructor(voidedProduct: string) {
    super(`Voided Product ${voidedProduct} not found`, 'VOIDED_PRODUCT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class BusinessDiscountNotFoundException extends TangoException {
  constructor(discountId: string, businessId: string) {
    super(`Business Discount ${discountId} for business ${businessId} not found`, 'BUSINESS_DISCOUNT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class ProductInOrderNotFoundException extends TangoException {
  constructor(productInOrder: string) {
    super(`Product In Order ${productInOrder} not found`, 'PRODUCT_IN_ORDER_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class VoidedProductMissingBusinessException extends TangoException {
  constructor(voidedProduct: string) {
    super(`Unable to identify business for voided product ${voidedProduct}`, 'BUSINESS_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class ProductInOrderMissingBusinessException extends TangoException {
  constructor(productInOrder: string) {
    super(`Unable to identify business for product in order ${productInOrder}`, 'BUSINESS_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class ErrorSavingOrderException extends TangoException {
  constructor() {
    super(`Error saving order`, 'ERROR_SAVING_ORDER', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}
export class LocationNotProvidedException extends TangoException {
  constructor() {
    super(`Location not provided`, 'LOCATION_NOT_PROVIDED', HttpStatus.BAD_REQUEST);
  }
}
export class InvalidBusinessHoursException extends TangoException {
  constructor() {
    super(`No business hours, or the current projection does not capture any open times`, 'INVALID_BUSINESS_HOURS', HttpStatus.BAD_REQUEST);
  }
}
export class PayoutIncludesFutureException extends TangoException {
  constructor() {
    super(`Cannot calculate the payout for a date range including the future`, 'PAYOUT_INCLUDES_FUTURE', HttpStatus.BAD_REQUEST);
  }
}
export class PaymentAlreadyRefundedException extends TangoException {
  constructor(payment: string) {
    super(`Payment ${payment} already refunded`, 'PAYMENT_ALREADY_REFUNDED', HttpStatus.BAD_REQUEST);
  }
}
export class SeatAlreadyPaidException extends TangoException {
  constructor(seat: string) {
    super(`Seat ${seat} was already paid for`, 'SEAT_ALREADY_PAID', HttpStatus.CONFLICT);
  }
}
export class MissingStripeConnectException extends TangoException {
  constructor(business: string) {
    super(`Business ${business} is not setup to accept payments`, 'MISSING_STRIPE_CONNECT', HttpStatus.BAD_REQUEST);
  }
}
export class GenerateConnectionTokenException extends TangoException {
  constructor() {
    super(`Unable to generate a connection token`, 'NO_CONNECTION_TOKEN', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}
export class RefundFailedException extends TangoException {
  constructor(payment: string) {
    super(`Failed to create a stripe refund for payment ${payment}`, 'STRIPE_REFUND_FAILED', HttpStatus.BAD_GATEWAY);
  }
}
export class CreateCustomerException extends TangoException {
  constructor() {
    super(`Failed to create a customer id`, 'CREATE_CUSTOMER_FAILED', HttpStatus.BAD_GATEWAY);
  }
}
export class PaymentIntentFailedException extends TangoException {
  constructor() {
    super(`Failed to create a payment intent`, 'STRIPE_PAYMENT_INTENT_FAILED', HttpStatus.BAD_GATEWAY);
  }
}
export class CancelPaymentIntentFailedException extends TangoException {
  constructor(intent: string) {
    super(`Failed to cancel payment intent ${intent}`, 'STRIPE_PAYMENT_INTENT_FAILED', HttpStatus.BAD_GATEWAY);
  }
}
export class CapturePaymentIntentFailedException extends TangoException {
  constructor(intent: string) {
    super(`Failed to capture payment intent ${intent}`, 'STRIPE_PAYMENT_INTENT_FAILED', HttpStatus.BAD_GATEWAY);
  }
}
export class WrongTabForPreauthException extends TangoException {
  constructor() {
    super(`Preauth does not match tab`, 'WRONG_TAB_FOR_PREAUTH', HttpStatus.BAD_REQUEST);
  }
}
export class StripeRateLimitException extends TangoException {
  constructor() {
    super(`Stripe is rate limited`, 'STRIPE_RATE_LIMIT', HttpStatus.TOO_MANY_REQUESTS);
  }
}
export class GetReportDataFailedException extends TangoException {
  constructor(business: string) {
    super(`Failed to get tabs, products, and payments report data for business ${business}`, 'GET_REPORT_DATA_FAILED', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}
export class NoOrdersFoundException extends TangoException {
  constructor() {
    super(`No orders found for provided product ids`, 'ORDERS_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class NoTabForOrdersException extends TangoException {
  constructor(orderIds: string[]) {
    super(`No tab found for orders ${orderIds.join(', ')}`, 'TAB_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class NoTabForProductsException extends TangoException {
  constructor(productIds: string[]) {
    super(`No tab found for products ${productIds.join(', ')}`, 'TAB_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class NoProductsException extends TangoException {
  constructor() {
    super(`No products found`, 'PRODUCTS_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class UnmergeableProductsException extends TangoException {
  constructor(prodA: string, prodB: string, property: string) {
    super(`Products ${prodA} and ${prodB} are not mergeable due to incompatible ${property}`, 'UNMERGEABLE_PRODUCTS', HttpStatus.CONFLICT);
  }
}

// GIFT CARDS

export class GiftCardCreationException extends TangoException {
  constructor() {
    super(`Gift Card creation failed`, 'GIFT_CARD_CREATION_FAILED', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}
export class GiftCardNotFoundException extends TangoException {
  constructor(giftCard: string) {
    super(`Gift Card ${giftCard} not found`, 'GIFT_CARD_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class GiftCardAlreadyActiveException extends TangoException {
  constructor(giftCard: string) {
    super(`Gift Card ${giftCard} is already active`, 'GIFT_CARD_ALREDY_ACTIVE', HttpStatus.BAD_REQUEST);
  }
}
export class MinimumBalanceException extends TangoException {
  constructor(giftCard: string, has: number, needs: number) {
    super(`Gift Card ${giftCard} does not have minimum balance (has: ${has}, needed: ${needs})`, 'GIFT_CARD_MINIMUM_BALANCE', HttpStatus.BAD_REQUEST);
  }
}
export class MissingAckrooIdException extends TangoException {
  constructor(business: string) {
    super(`Ackroo device id not found for Business ${business}`, 'ACKROO_ID_MISSING', HttpStatus.NOT_FOUND);
  }
}
export class AckrooGatewayException extends TangoException {
  constructor() {
    super(`Error response from the Ackroo API`, 'ACKROO_GATEWAY_ERROR', HttpStatus.BAD_GATEWAY);
  }
}
export class GiftCardInsufficientFundsException extends TangoException {
  constructor(giftCard: string) {
    super(`Gift Card ${giftCard} has insufficient funds`, 'INSUFFICIENT_FUNDS', HttpStatus.BAD_REQUEST);
  }
}

export class InvalidReasonForVoidingProductException extends TangoException {
  constructor(businessId: string) {
    super(
      `Invalid reason provided for voiding product, reason is not in the list of reasons provided by the business with ID ${businessId}.`,
      `INVALID_VOID_PRODUCT_REASON`,
      HttpStatus.BAD_REQUEST,
    );
  }
}

export class InvalidReasonForCompingProductException extends TangoException {
  constructor(businessId: string) {
    super(
      `Invalid reason provided for comping product, reason is not in the list of reasons provided by the business with ID ${businessId}.`,
      `INVALID_COMP_PRODUCT_REASON`,
      HttpStatus.BAD_REQUEST,
    );
  }
}

// MENU

export class FoodAndBevEnterpriseNotFoundException extends TangoException {
  constructor(business: string) {
    super(`Food and Bev Enterprise for Business ${business} not found`, 'ENTERPRISE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class StaffingEnterpriseNotFoundException extends TangoException {
  constructor(business: string) {
    super(`Staffing Enterprise for Business ${business} not found`, 'ENTERPRISE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class MenuNotFoundException extends TangoException {
  constructor(menu: string) {
    super(`Menu ${menu} not found`, 'MENU_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class MenuRemovedException extends TangoException {
  constructor(menu: string, time: Date) {
    super(`Menu ${menu} was removed on ${time.toLocaleString()}`, 'MENU_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class SomeMenusNotFoundException extends TangoException {
  constructor() {
    super(`Not all menus were found`, 'MENU_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class PriceVariantNotFoundException extends TangoException {
  constructor(variant: string) {
    super(`Price Variant ${variant} not found`, 'PRICE_VARIANT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class ModifierOptionNotFoundException extends TangoException {
  constructor(option: string) {
    super(`Modifier Option ${option} not found`, 'MODIFIER_OPTION_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class ModifierNotFoundException extends TangoException {
  constructor(modifier: string) {
    super(`Modifier ${modifier} not found`, 'MODIFIER_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class ProductNotFoundException extends TangoException {
  constructor(product: string) {
    super(`Product ${product} not found`, 'PRODUCT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class BusinessProductNotFoundException extends TangoException {
  constructor(product: string, business: string) {
    super(`Product ${product} for business ${business} not found`, 'PRODUCT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class BusinessModifierOptionNotFoundException extends TangoException {
  constructor(option: string, business: string) {
    super(`Modifier Option ${option} for business ${business} not found`, 'MODIFIER_OPTION_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class SizeOptionInNonSizeGroupException extends TangoException {
  constructor(option: string, group: string) {
    super(`Size option ${option} cannot be added to non-size group ${group}`, 'SIZE_OPTION_IN_NON_SIZE_GROUP', HttpStatus.BAD_REQUEST);
  }
}
export class NonSizeOptionInSizeGroupException extends TangoException {
  constructor(option: string, group: string) {
    super(`Non-size option ${option} cannot be added to size group ${group}`, 'NON_SIZE_OPTION_IN_SIZE_GROUP', HttpStatus.BAD_REQUEST);
  }
}
export class MultiplierForNonSizeException extends TangoException {
  constructor() {
    super(`Multiplier for non-size option cannot be set`, 'MULTIPLIER_FOR_NON_SIZE_OPTION', HttpStatus.BAD_REQUEST);
  }
}
export class AddedCostForSizeException extends TangoException {
  constructor() {
    super(`Added cost for size option cannot be set`, 'ADDED_COST_FOR_SIZE_OPTION', HttpStatus.BAD_REQUEST);
  }
}
export class ProductOrModifierException extends TangoException {
  constructor() {
    super(`Must specify product or modifier option (but not both)`, 'PRODUCT_OR_MODIFIER_OPTION', HttpStatus.BAD_REQUEST);
  }
}
export class ProductOnMenuNotFoundException extends TangoException {
  constructor(product: string, menu: string, category: string) {
    super(`Product ${product} not found on menu ${menu}, category ${category}`, 'PRODUCT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class OpenChargeException extends TangoException {
  constructor(field: string) {
    super(`Cannot set ${field} for an open charge`, 'OPEN_CHARGE_INVALID', HttpStatus.BAD_REQUEST);
  }
}
export class NotOpenChargeException extends TangoException {
  constructor(product: string) {
    super(`Product ${product} is not an open charge`, 'NOT_OPEN_CHARGE', HttpStatus.BAD_REQUEST);
  }
}
export class PriceRequiredException extends TangoException {
  constructor(product: string) {
    super(`Product ${product} is an open charge and requires a price`, 'PRICE_REQUIRED', HttpStatus.BAD_REQUEST);
  }
}
export class DiscountNotFoundException extends TangoException {
  constructor(discount: string) {
    super(`Discount ${discount} not found`, 'DISCOUNT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class DiscountNotSpecificException extends TangoException {
  constructor(discount: string) {
    super(`Discount ${discount} cannot be applied to individual products`, 'DISCOUNT_NOT_SPECIFIC', HttpStatus.CONFLICT);
  }
}
export class AmountAndPercentDiscountException extends TangoException {
  constructor() {
    super(`Discount cannot have both an amount and percent`, 'DISCOUNT_INVALID', HttpStatus.BAD_REQUEST);
  }
}
export class EmptyDiscountException extends TangoException {
  constructor() {
    super(`Discount must have an amount or percent`, 'DISCOUNT_INVALID', HttpStatus.BAD_REQUEST);
  }
}
export class NegativeDiscountException extends TangoException {
  constructor() {
    super(`Discount must be positive`, 'DISCOUNT_INVALID', HttpStatus.BAD_REQUEST);
  }
}
export class ProductsAndOrderDiscountException extends TangoException {
  constructor() {
    super(`Discount cannot apply to both products and orders`, 'DISCOUNT_INVALID', HttpStatus.BAD_REQUEST);
  }
}
export class PromocodeNotFoundException extends TangoException {
  constructor(promocode: string) {
    super(`Promocode ${promocode} not found`, 'PROMOCODE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class MenuNotDraftException extends TangoException {
  constructor(menu: string) {
    super(`Menu ${menu} is not a draft menu`, 'MENU_NOT_DRAFT', HttpStatus.BAD_REQUEST);
  }
}
export class NoTargetsException extends TangoException {
  constructor() {
    super(`Must provide at least one target to publish a menu`, 'NO_TARGETS', HttpStatus.BAD_REQUEST);
  }
}

export class PriceLevelNotFoundException extends TangoException {
  constructor(priceLevelId: string) {
    super(`Price level ${priceLevelId} not found`, 'PRICE_LEVEL_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class PriceLevelPriceNotFoundException extends TangoException {
  constructor(priceLevelPriceId: string) {
    super(`Price level price ${priceLevelPriceId} not found`, 'PRICE_LEVEL_PRICE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class PriceLevelAlreadyExistsException extends TangoException {
  constructor(priceLevelName: string, enterpriseId: string) {
    super(`Price level with name ${priceLevelName} already exists for enterprise with ID ${enterpriseId}`, 'EXISTING_PRICE_LEVEL', HttpStatus.BAD_REQUEST);
  }
}

export class ProductAndModifierOptionIdException extends TangoException {
  constructor(productId: string, modifierOptionId: string) {
    super(
      `Both a product with ID ${productId} and modifier option with ID ${modifierOptionId} was provided, please provide only one or the other`,
      'PRODUCT_AND_MODIFIER_OPTION_ID_PROVIDED',
      HttpStatus.BAD_REQUEST,
    );
  }
}

export class NoProductOrModifierOptionIdException extends TangoException {
  constructor() {
    super(`Neither a productId or a modifierOptionId was provided, please provide one or the other`, 'NO_PRODUCT_OR_MODIFIER_OPTION_ID_PROVIDED', HttpStatus.BAD_REQUEST);
  }
}

// PRINTING
export class PrintJobNotFoundException extends TangoException {
  constructor(printjob: string) {
    super(`Printjob ${printjob} not found`, 'PRINT_JOB_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class NoPrintersException extends TangoException {
  constructor(business: string) {
    super(`Business ${business} doesn't have any printers set up`, 'NO_PRINTERS_SETUP', HttpStatus.BAD_REQUEST);
  }
}
export class SendPrintJobException extends TangoException {
  constructor(printJob: string) {
    super(`Failed to send or notify print job ${printJob}`, 'PRINT_JOB_NOTIFICATION_FAILED', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}
export class MonitorPrintJobException extends TangoException {
  constructor(printJob: string) {
    super(`Failed to send print job ${printJob} to monitoring service`, 'PRINT_JOB_MONITOR_FAILED', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}

// PRINTING V2

export class TangoPrintingControllerNotFoundException extends TangoException {
  constructor(controllerId: string) {
    super(`Tango Printing Controller with ID ${controllerId} was not found`, 'PRINTING_CONTROLLER_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class TangoPrinterNotFoundException extends TangoException {
  constructor(printerId: string) {
    super(`Tango Printer with ID ${printerId} was not found`, 'PRINTER_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class TangoPrintJobNotFoundException extends TangoException {
  constructor(printJobId: string) {
    super(`Tango Print Job with ID ${printJobId} was not found`, 'PRINT_JOB_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class TangoPrintJobAlreadyPrintedException extends TangoException {
  constructor(printJobId: string) {
    super(`Tango Print Job with ID ${printJobId} has already been printed`, 'PRINT_JOB_ALREADY_PRINTED', HttpStatus.BAD_REQUEST);
  }
}

export class TangoPrintingControllersNotFoundException extends TangoException {
  constructor(businessId: string) {
    super(`Tango Printing Controllers for business with ID ${businessId} could not be found`, 'PRINTING_CONTROLLERS_FOR_BUSINESS_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class TangoCashEventNotFoundException extends TangoException {
  constructor(cashEventId: string) {
    super(`Tango Cash Event with ID ${cashEventId} could not be found`, 'CASH_EVENT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

// QR
export class QRCodeNotFoundException extends TangoException {
  constructor(qrCode: string) {
    super(`QR code ${qrCode} not found`, 'QR_CODE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class NoQRCodesFoundException extends TangoException {
  constructor(business: string) {
    super(`No QR Codes for Business ${business}`, 'QR_CODE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class QRCodeInUseException extends TangoException {
  constructor(qrCode: string) {
    super(`QR code ${qrCode} is already in use`, 'QR_CODE_IN_USE', HttpStatus.BAD_REQUEST);
  }
}

// KDS
export class KDSNotFoundException extends TangoException {
  constructor(kds: string) {
    super(`KDS ${kds} not found`, 'KDS_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class KDSOrderNotFoundException extends TangoException {
  constructor(order: string) {
    super(`KDS Order ${order} not found`, 'KDS_ORDER_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class KDSItemNotFoundException extends TangoException {
  constructor(item: string) {
    super(`KDS Item ${item} not found`, 'KDS_ITEM_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class KDSNotificationException extends TangoException {
  constructor(kds: string) {
    super(`Failed to notify KDS ${kds} of orders`, 'KDS_NOTIFICATION_FAILED', HttpStatus.BAD_GATEWAY);
  }
}
export class KDSCreateException extends TangoException {
  constructor() {
    super(`Failed to create KDS`, 'KDS_CREATE_EXCEPTION', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}
export class KDSCreateStationException extends TangoException {
  constructor(kdsId: string) {
    super(`Failed to create KDS ${kdsId} station`, 'KDS_CREATE_STATION_EXCEPTION', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}
export class KDSGetException extends TangoException {
  constructor(kdsId: string) {
    super(`Failed to get KDS ${kdsId}`, 'KDS_GET_EXCEPTION', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}
export class KDSGetStationException extends TangoException {
  constructor(kdsId: string, stationId: string) {
    super(`Failed to get KDS ${kdsId} station ${stationId}`, 'KDS_GET_STATION_EXCEPTION', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}
export class KDSGetChitException extends TangoException {
  constructor(kdsId: string, chitId: string) {
    super(`Failed to get KDS ${kdsId} chit ${chitId}`, 'KDS_GET_CHIT_EXCEPTION', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}

// REPUTATION

export class ReputationEnterpriseNotFoundException extends TangoException {
  constructor(business: string) {
    super(`Reputation Enterprise for Business ${business} not found`, 'ENTERPRISE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class MissingReviewTrackerIdException extends TangoException {
  constructor(business: string) {
    super(`Business/Enterprise ${business} does not have a review tracker id`, 'REVIEW_TRACKER_ID_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class ReviewNotFoundException extends TangoException {
  constructor(review: string) {
    super(`Review ${review} not found`, 'REVIEW_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class ReviewTrackerAccountIDNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Review Tracker Account ID not found for business ${id} not found`, 'REVIEW_TRACKER_ACCOUNT_ID_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class ReviewResponseTemplateNotFoundException extends TangoException {
  constructor(template: string) {
    super(`Review Response ${template} not found`, 'REVIEW_RESPONSE_TEMPLATE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class ReviewResponseTemplatesNotFoundException extends TangoException {
  constructor(enterprise: string) {
    super(`Review Response Templates for Enterprise ${enterprise} not found`, 'REVIEW_RESPONSE_TEMPLATE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class ReviewsNotFoundException extends TangoException {
  constructor(enterprise: string) {
    super(`Reviews for Enterprise ${enterprise} not found`, 'REVIEW_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class GetReviewTrackerTokenException extends TangoException {
  constructor() {
    super(`Failed to get review tracker token`, 'REVIEW_TRACKER_TOKEN_FAILED', HttpStatus.BAD_GATEWAY);
  }
}
export class RespondToReviewException extends TangoException {
  constructor(review: string) {
    super(`Failed to respond to review ${review}`, 'REVIEW_REPONSE_FAILED', HttpStatus.BAD_GATEWAY);
  }
}
export class GetCompetitorsException extends TangoException {
  constructor(accountId: string) {
    super(`Failed to get competitors for account ${accountId}`, 'CANNOT_FIND_COMPETITOR_LOCATIONS', HttpStatus.BAD_GATEWAY);
  }
}

export class DiscoverLocationException extends TangoException {
  constructor(accountId: string) {
    super(`Failed to get discover location data for account ${accountId}`, 'CANNOT_GET_DISCOVER_LOCATION_DATA', HttpStatus.BAD_GATEWAY);
  }
}

export class GetReviewsException extends TangoException {
  constructor(accountId: string) {
    super(`Failed to get reviews for account ${accountId}`, 'CANNOT_GET_REVIEWS', HttpStatus.BAD_GATEWAY);
  }
}

export class GetReviewResponsesException extends TangoException {
  constructor(reviewId: string) {
    super(`Failed to get reviews responses for review ${reviewId}`, 'CANNOT_GET_REVIEW_RESPONSES', HttpStatus.BAD_GATEWAY);
  }
}

// REPORTING
export class FunnelNotFoundException extends TangoException {
  constructor(funnel: string) {
    super(`Funnel ${funnel} does not exist`, 'FUNNEL_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class ViewNotFoundException extends TangoException {
  constructor(view: string) {
    super(`View ${view} not found`, 'VIEW_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class ScheduledViewNotFoundException extends TangoException {
  constructor(view: string) {
    super(`Scheduled View ${view} not found`, 'SCHEDULED_VIEW_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class ScheduledViewsNotFoundException extends TangoException {
  constructor(business: string) {
    super(`Scheduled Views for Business ${business} not found`, 'SCHEDULED_VIEW_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class LegacySettingsNotFoundException extends TangoException {
  constructor(firestoreId: string) {
    super(`Legacy Settings for firestore id ${firestoreId} not found`, 'LEGACY_SETTINGS_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class BackwardsDateRangeException extends TangoException {
  constructor() {
    super(`Start date cannot be after end date`, 'INVALID_DATE_RANGE', HttpStatus.BAD_REQUEST);
  }
}
export class SendEmailException extends TangoException {
  constructor() {
    super(`We had an issue sending the report to SendGrid`, 'UNABLE_TO_SEND_EMAIL', HttpStatus.BAD_GATEWAY);
  }
}

// STAFFING
export class InvitationNotFoundException extends TangoException {
  constructor(invitation: string) {
    super(`Invitation ${invitation} not found`, 'INVITATION_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class BusinessInvitationsNotFoundException extends TangoException {
  constructor(business: string) {
    super(`Invitations for Business ${business} not found`, 'INVITATION_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class EnterpriseInvitationsNotFoundException extends TangoException {
  constructor(enterprise: string) {
    super(`Invitations for Enterprise ${enterprise} not found`, 'INVITATION_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class StaffNotFoundException extends TangoException {
  constructor(staff: string) {
    super(`Staff Member ${staff} not found`, 'STAFF_MEMBER_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class NoBusinessOrEnterpriseException extends TangoException {
  constructor() {
    super('No enterprise or business ID was supplied when creating the staff member', 'NO_BUSINESS_OR_ENTERPRISE', HttpStatus.BAD_REQUEST);
  }
}
export class PinCodeInUseException extends TangoException {
  constructor(pincode: string) {
    super(`A staff member with the pin code ${pincode} already exists for this business`, 'PINCODE_IN_USE', HttpStatus.BAD_REQUEST);
  }
}
export class BusinessStaffNotFoundException extends TangoException {
  constructor(business: string) {
    super(`Staff Members for Business ${business} not found`, 'STAFF_MEMBER_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class EnterpriseStaffNotFoundException extends TangoException {
  constructor(enterprise: string) {
    super(`Staff Members for Enterprise ${enterprise} not found`, 'STAFF_MEMBER_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class UserStaffNotFoundException extends TangoException {
  constructor(user: string) {
    super(`Staff Members for user ${user} not found`, 'STAFF_MEMBER_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class ArchivedStaffNotFoundException extends TangoException {
  constructor(staff: string) {
    super(`Archived Staff Member ${staff} not found`, 'ARCHIVED_STAFF_MEMBER_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class EnterpriseArchivedStaffNotFoundException extends TangoException {
  constructor(enterprise: string) {
    super(`Archived Staff Members for Enterprise ${enterprise} not found`, 'ARCHIVED_STAFF_MEMBER_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class RoleNotFoundException extends TangoException {
  constructor(role: string) {
    super(`Role ${role} not found`, 'ROLE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class EnterpriseRolesNotFoundException extends TangoException {
  constructor(enterprise: string) {
    super(`Roles for Enterprise ${enterprise} not found`, 'ROLE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class BusinessRolesNotFoundException extends TangoException {
  constructor(business: string) {
    super(`Roles for Business ${business} not found`, 'ROLE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class DepartmentNotFoundException extends TangoException {
  constructor(department: string) {
    super(`Department ${department} not found`, 'DEPARTMENT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class EnterpriseDepartmentsNotFoundException extends TangoException {
  constructor(enterprise: string) {
    super(`Departments for Enterprise ${enterprise} not found`, 'DEPARTMENT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class PayrateNotFoundException extends TangoException {
  constructor(rate: string) {
    super(`Pay rate ${rate} not found`, 'PAY_RATE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class PayrateForStaffNotFoundException extends TangoException {
  constructor(rate: string, staff: string) {
    super(`Pay rate ${rate} for staff ${staff} not found`, 'PAY_RATE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class WorkEventNotFoundException extends TangoException {
  constructor(event: string) {
    super(`Work Event ${event} not found`, 'WORK_EVENT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class StaffWorkEventsNotFoundException extends TangoException {
  constructor(staff: string) {
    super(`Work Events for staff ${staff} not found`, 'WORK_EVENT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class BusinessWorkEventsNotFoundException extends TangoException {
  constructor(business: string) {
    super(`Work Events for Business ${business} not found`, 'WORK_EVENT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class EnterpriseWorkEventsNotFoundException extends TangoException {
  constructor(enterprise: string) {
    super(`Work Events for Enterprise ${enterprise} not found`, 'WORK_EVENT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class NoHourlyRateException extends TangoException {
  constructor(rate: string) {
    super(`Pay rate ${rate} has no hourly rate`, 'INVALID_PAY_RATE', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}
export class NoRateException extends TangoException {
  constructor() {
    super(`Missing both hourly rate and annual rate`, 'INVALID_PAY_RATE', HttpStatus.BAD_REQUEST);
  }
}
export class UserExistsException extends TangoException {
  constructor() {
    super(`User already exists`, 'USER_ALREADY_EXISTS', HttpStatus.CONFLICT);
  }
}

export class TipPoolNotFoundException extends TangoException {
  constructor(tipPoolId: string) {
    super(`Tip Pool with ID ${tipPoolId} not found`, 'TIP_POOL_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class NoDepartmentOrRoleIdException extends TangoException {
  constructor() {
    super(`Neither a department or role ID was provided, please provide at least one or the other`, 'NO_DEPARTMENT_OR_ROLE_ID_PROVIDED', HttpStatus.BAD_REQUEST);
  }
}

export class DepartmentAndRoleIdException extends TangoException {
  constructor(departmentId: string, roleId: string) {
    super(
      `Both a department with ID ${departmentId} and role with ID ${roleId} was provided, please provide only one or the other`,
      'DEPARTMENT_AND_ROLE_ID_PROVIDED',
      HttpStatus.BAD_REQUEST,
    );
  }
}

export class TipPoolCalculationNotFoundException extends TangoException {
  constructor(tipPoolCalculationId: string) {
    super(`Tip Pool Calculation with ID ${tipPoolCalculationId} not found`, 'TIP_POOL_CALCULATION_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

// SCHEDULING
export class ReactionNotFoundException extends TangoException {
  constructor(reaction: string) {
    super(`Reaction ${reaction} not found`, 'REACTION_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class DailyLogNotFoundException extends TangoException {
  constructor(log: string) {
    super(`Daily Log ${log} not found`, 'DAILY_LOG_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class BusinessDailyLogsNotFoundException extends TangoException {
  constructor(business: string) {
    super(`Daily Logs for business ${business} not found`, 'DAILY_LOG_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class DailyLogReplyNotFoundException extends TangoException {
  constructor(reply: string) {
    super(`Daily Log Reply ${reply} not found`, 'DAILY_LOG_REPLY_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class StaffScheduleNotFoundException extends TangoException {
  constructor(start: string, end: string) {
    super(`A schedule between the dates ${start} and ${end} could not be found`, `STAFF_SCHEDULE_NOT_FOUND`, HttpStatus.NOT_FOUND);
  }
}
export class StaffNotAvailableException extends TangoException {
  constructor(id: string, reason: string) {
    super(`The staff member ${id} is not available. Reason: ${reason}`, `STAFF_MEMBER_NOT_AVAILABLE`, HttpStatus.BAD_REQUEST);
  }
}
export class ScheduleTemplateNotFoundException extends TangoException {
  constructor(id: string) {
    super(`The schedule template ${id} does not exist`, `SCHEDULE_TEMPLATE_NOT_AVAILABLE`, HttpStatus.NOT_FOUND);
  }
}
export class BlackoutDateSelectedException extends TangoException {
  constructor(date: string) {
    super(`The selected date range contains the blackout date ${date}`, `BLACKOUT_DATE_SELECTED`, HttpStatus.BAD_REQUEST);
  }
}
export class BlackoutDateDuringRequestException extends TangoException {
  constructor(date: string) {
    super(`Approving this time off request would conflict with a blackout date: ${date}`, `BLACKOUT_DATE_CONFLICT`, HttpStatus.BAD_REQUEST);
  }
}
export class MaxTimeOffRequestsReached extends TangoException {
  constructor(role: string, date: string) {
    super(
      `You cannot request time off because your primary role ${role} has the maximum approved time off for the date ${date}`,
      `MAX_TIME_OFF_REACHED`,
      HttpStatus.BAD_REQUEST,
    );
  }
}
export class InvalidQueryParameterException extends TangoException {
  constructor(parameter: string, validValues: string[]) {
    super(
      `You have provided an invalid value for query parameter ${parameter}, valid values ${validValues.join(', ')}`,
      `INVALID_QUERY_PARAMETERS`,
      HttpStatus.BAD_REQUEST,
    );
  }
}

export class LaborTargetsNotFoundException extends TangoException {
  constructor(id: string) {
    super(`No labor targets could be found for business ${id}`, `NO_LABOUR_TARGETS`, HttpStatus.NOT_FOUND);
  }
}
export class TargetNotSuppliedException extends TangoException {
  constructor(target: string) {
    super(`You did not supply the correct target for the type of labor target you are creating, target needed: ${target}`, `TARGET_NOT_SUPPLIED`, HttpStatus.BAD_REQUEST);
  }
}
export class DepartmentNotSchedulableException extends TangoException {
  constructor(department: string) {
    super(
      `The department ${department} is not schedulable and therefore cannot be subject to requests such as labor targets`,
      `DEPARTMENT_NOT_SCHEDULABLE`,
      HttpStatus.BAD_REQUEST,
    );
  }
}
export class ShiftNotFoundException extends TangoException {
  constructor(id: string) {
    super(`The shift with ID ${id} could not be found`, `SHIFT_NOT_FOUND`, HttpStatus.NOT_FOUND);
  }
}
export class InvalidDuplicateRangeException extends TangoException {
  constructor(reason: string) {
    super(`You are trying to duplicate shifts in a range with an invalid range, reason: ${reason}`, `DUPLICATE_RANGE_INVALID`, HttpStatus.BAD_REQUEST);
  }
}
export class InvalidDuplicationException extends TangoException {
  constructor(shifts: string[]) {
    super(
      `You cannot duplicate shifts, because certain shifts will not be valid in the new schedule:\n${shifts.join('\n')}`,
      `INVALID_DUPLICATION`,
      HttpStatus.BAD_REQUEST,
    );
  }
}

export class StaffAvailabilityNotFoundException extends TangoException {
  constructor(id: string) {
    super(`The Availability for staff member with id ${id} was not found`, `NO_STAFF_AVAILABILITY`, HttpStatus.NOT_FOUND);
  }
}
export class AvailabilityRequestNotFoundException extends TangoException {
  constructor(id: string) {
    super(`The Availability Request with ID ${id} was not found`, `AVAILABILITY_REQUEST_NOT_FOUND`, HttpStatus.NOT_FOUND);
  }
}

export class TimeOffRequestNotFoundException extends TangoException {
  constructor(id: string) {
    super(`The Time Off Request with ID ${id} was not found`, `TIME_OFF_REQUEST_NOT_FOUND`, HttpStatus.NOT_FOUND);
  }
}

export class BlackoutDateAlreadyExistsException extends TangoException {
  constructor(date: string) {
    super(`A blackout date already exists for the date ${date}`, `BLACKOUT_DATE_ALREADY_EXISTS`, HttpStatus.BAD_REQUEST);
  }
}

export class BlackoutDateNotFoundException extends TangoException {
  constructor(id: string) {
    super(`A blackout date with ID ${id} could not be found`, `BLACKOUT_DATE_NOT_FOUND`, HttpStatus.NOT_FOUND);
  }
}

export class BlackoutPeriodNotFoundException extends TangoException {
  constructor(period: string) {
    super(`Blackout period ${period} not be found`, `BLACKOUT_PERIOD_NOT_FOUND`, HttpStatus.NOT_FOUND);
  }
}

export class TimeOffLimitNotFoundException extends TangoException {
  constructor(id: string) {
    super(`A time off limit with ID ${id} could not be found`, `TIME_OFF_LIMIT_NOT_FOUND`, HttpStatus.NOT_FOUND);
  }
}

export class DropRequestNotFoundException extends TangoException {
  constructor(id: string) {
    super(`A drop request with ID ${id} could not be found`, `DROP_REQUEST_NOT_FOUND`, HttpStatus.NOT_FOUND);
  }
}

export class DropRequestResolutionNoStaffIDException extends TangoException {
  constructor() {
    super(
      `You attempted to resolve a Drop Request by reassigning it, but did not provide a staff ID of who to reassign it to`,
      `DROP_REQUEST_RESOLUTION_FAILED`,
      HttpStatus.BAD_REQUEST,
    );
  }
}

export class DropRequestAlreadyOpenException extends TangoException {
  constructor(id: string) {
    super(`You are trying to open a Drop Request when one already exists for the shift ${id}`, `DROP_REQUEST_ALREADY_EXISTS`, HttpStatus.BAD_REQUEST);
  }
}

export class TradeRequestNotFoundException extends TangoException {
  constructor(id: string) {
    super(`We could not find the trade request with ID ${id}`, `TRADE_REQUEST_NOT_FOUND`, HttpStatus.NOT_FOUND);
  }
}

export class TradeRequestOfferNotFoundException extends TangoException {
  constructor(id: string) {
    super(`We could not find the trade request offer with ID ${id}`, `TRADE_REQUEST_OFFER_NOT_FOUND`, HttpStatus.NOT_FOUND);
  }
}

export class TradeCannotBeMadeException extends TangoException {
  constructor(reason: string) {
    super(`The trade cannot be made because ${reason}`, `TRADE_CANNOT_BE_MADE`, HttpStatus.BAD_REQUEST);
  }
}

export class TradeRequestAlreadyExistsException extends TangoException {
  constructor(id: string) {
    super(`A trade request already exists for this shift ${id}`, `TRADE_ALREADY_EXISTS`, HttpStatus.BAD_REQUEST);
  }
}

export class TradeOfferAlreadyMadeException extends TangoException {
  constructor(id: string) {
    super(`A trade offer has already been made to this this trade request with this shift ${id}`, `TRADE_OFFER_ALREADY_MADE`, HttpStatus.BAD_REQUEST);
  }
}

export class CoverNotPossibleException extends TangoException {
  constructor(reason: string) {
    super(`This cover is not possible because ${reason}`, `COVER_NOT_POSSIBLE`, HttpStatus.BAD_REQUEST);
  }
}

export class CoverRequestNotFoundException extends TangoException {
  constructor(id: string) {
    super(`The cover request with id ${id} could not be found`, `COVER_REQUEST_NOT_FOUND`, HttpStatus.NOT_FOUND);
  }
}

// INVENTORY

export class RecipesNotFoundException extends TangoException {
  constructor() {
    super(`Not all recipes were found`, `RECIPE_NOT_FOUND`, HttpStatus.NOT_FOUND);
  }
}
export class ItemsNotFoundException extends TangoException {
  constructor() {
    super(`Not all items were found`, `ITEM_NOT_FOUND`, HttpStatus.NOT_FOUND);
  }
}
export class RecipeNotFoundException extends TangoException {
  constructor(recipe: string) {
    super(`Recipe ${recipe} not found`, `RECIPE_NOT_FOUND`, HttpStatus.NOT_FOUND);
  }
}
export class ItemNotFoundException extends TangoException {
  constructor(item: string) {
    super(`Item ${item} not found`, `ITEM_NOT_FOUND`, HttpStatus.NOT_FOUND);
  }
}
export class ItemInUseException extends TangoException {
  constructor() {
    super(`Item(s) could not be deleted because they are used in a recipe`, `ITEM_IN_USE`, HttpStatus.CONFLICT);
  }
}
export class CircularRecipeException extends TangoException {
  constructor(recipe: string) {
    super(`Recipe ${recipe} contains itself`, `CIRCULAR_RECIPE`, HttpStatus.INTERNAL_SERVER_ERROR);
  }
}
export class UniqueEnterpriseException extends TangoException {
  constructor() {
    super(`Exactly one Enterprise must be provided`, `TOO_MANY_ENTERPRISES`, HttpStatus.BAD_REQUEST);
  }
}
export class UniqueBusinessException extends TangoException {
  constructor() {
    super(`Exactly one Business must be provided`, `TOO_MANY_BUSINESSES`, HttpStatus.BAD_REQUEST);
  }
}
export class ReportingUnitTooSmallException extends TangoException {
  constructor() {
    super(`Reporting unit must contain at least one ingredient unit`, `REPORTING_UNIT_TOO_SMALL`, HttpStatus.BAD_REQUEST);
  }
}
export class NotRawException extends TangoException {
  constructor(item: string) {
    super(`Item ${item} is not a Raw Item`, `ITEM_NOT_RAW`, HttpStatus.BAD_REQUEST);
  }
}
export class InvalidItemTypeException extends TangoException {
  constructor(itemType: string) {
    super(`Item type must be 'basic' or 'prep' (not ${itemType})`, `BAD_ITEM_TYPE`, HttpStatus.BAD_REQUEST);
  }
}

export class ScheduleNotFoundException extends TangoException {
  constructor(schedule: string) {
    super(`Schedule ${schedule} not found`, `SCHEDULE_NOT_FOUND`, HttpStatus.NOT_FOUND);
  }
}
export class ScheduleNotReadyException extends TangoException {
  constructor(schedule: string) {
    super(`Schedule ${schedule} is not ready to start`, `SCHEDULE_NOT_READY`, HttpStatus.BAD_REQUEST);
  }
}
export class CountNotFoundException extends TangoException {
  constructor(count: string) {
    super(`Count ${count} not found`, `COUNT_NOT_FOUND`, HttpStatus.NOT_FOUND);
  }
}
export class CountTooOldException extends TangoException {
  constructor(count: string) {
    super(`Count ${count} is too old`, `COUNT_EXPIRED`, HttpStatus.CONFLICT);
  }
}
export class CountClosedException extends TangoException {
  constructor(count: string) {
    super(`Count ${count} is already closed`, `COUNT_CLOSED`, HttpStatus.CONFLICT);
  }
}
export class CountInProgressException extends TangoException {
  constructor(count: string) {
    super(`Count ${count} is in progress`, `COUNT_IN_PROGRESS`, HttpStatus.CONFLICT);
  }
}
export class SubmitAndCancelException extends TangoException {
  constructor() {
    super(`Must either submit a count or cancel it (but not both)`, `SUBMIT_OR_CANCEL`, HttpStatus.BAD_REQUEST);
  }
}

export class UnitsNotFoundException extends TangoException {
  constructor() {
    super(`Not all units found`, `UNIT_NOT_FOUND`, HttpStatus.NOT_FOUND);
  }
}
export class ConversionsNotFoundException extends TangoException {
  constructor() {
    super(`Not all conversions found`, `CONVERSION_NOT_FOUND`, HttpStatus.NOT_FOUND);
  }
}
export class CrossEnterpirseConversionException extends TangoException {
  constructor(unitOne: string, unitTwo: string) {
    super(`Units ${unitOne} and ${unitTwo} belong to different Enterprises`, `CROSS_ENTERPRISE_CONVERSION`, HttpStatus.BAD_REQUEST);
  }
}
export class SelfConversionException extends TangoException {
  constructor(unit: string) {
    super(`Cannot convert unit ${unit} into itself`, `SELF_CONVERSION`, HttpStatus.BAD_REQUEST);
  }
}
export class NegativeAmountException extends TangoException {
  constructor() {
    super(`Amount must be positive`, `NEGATIVE_AMOUNT`, HttpStatus.BAD_REQUEST);
  }
}
export class OfferNotFoundException extends TangoException {
  constructor(offer: string) {
    super(`Offering ${offer} not found`, `OFFER_NOT_FOUND`, HttpStatus.NOT_FOUND);
  }
}
export class VendorNotFoundException extends TangoException {
  constructor(vendor: string) {
    super(`Vendor ${vendor} not found`, `VENDOR_NOT_FOUND`, HttpStatus.NOT_FOUND);
  }
}
export class InvoiceNotFoundException extends TangoException {
  constructor(invoice: string) {
    super(`Invoice ${invoice} not found`, `INVOICE_NOT_FOUND`, HttpStatus.NOT_FOUND);
  }
}
export class ManyDefaultOffersException extends TangoException {
  constructor(item: string) {
    super(`Tried to set multiple default offers for item ${item}`, `TOO_MANY_OFFERS`, HttpStatus.BAD_REQUEST);
  }
}
export class ManyIntegratedOffersException extends TangoException {
  constructor(integratedOffer: string) {
    super(`Tried to set multiple offers for integrated offer ${integratedOffer}`, `TOO_MANY_OFFERS`, HttpStatus.BAD_REQUEST);
  }
}
export class ExistingOfferException extends TangoException {
  constructor(integratedOffer: string) {
    super(`Integrated offer ${integratedOffer} already has an offer`, `TOO_MANY_OFFERS`, HttpStatus.CONFLICT);
  }
}
export class DuplicateOfferUpdateException extends TangoException {
  constructor() {
    super(`Cannot update the same offer twice in one request`, `DUPLICATE_OFFER_UPDATE`, HttpStatus.BAD_REQUEST);
  }
}
export class OffersNotFoundException extends TangoException {
  constructor() {
    super(`Not all offers were found`, `OFFER_NOT_FOUND`, HttpStatus.NOT_FOUND);
  }
}
export class IntegratedOfferNotFoundException extends TangoException {
  constructor(integratedOffer: string) {
    super(`Integrated Offer ${integratedOffer} not found`, `INTEGRATED_OFFER_NOT_FOUND`, HttpStatus.NOT_FOUND);
  }
}
export class IntegratedVendorNotFoundException extends TangoException {
  constructor(integratedVendor: string) {
    super(`Integrated Vendor ${integratedVendor} not found`, `INTEGRATED_VENDOR_NOT_FOUND`, HttpStatus.NOT_FOUND);
  }
}
export class VendorNotIntegratedException extends TangoException {
  constructor(vendor: string) {
    super(`Vendor ${vendor} is not an integrated vendor`, `VENDOR_NOT_INTEGRATED`, HttpStatus.BAD_REQUEST);
  }
}

export class InvalidCountryException extends TangoException {
  constructor(value: string) {
    super(`The country "${value}" is not a valid country, please update the business country`, `INVALID_COUNTRY`, HttpStatus.BAD_REQUEST);
  }
}

export class StripeConnectAccountNotFound extends TangoException {
  constructor(id: string) {
    super(`The business with ID ${id} does not have a valid stripe connect account`, `INVALID_STRIPE_CONNECT_ACCOUNT`, HttpStatus.BAD_REQUEST);
  }
}

export class AIParseFailedException extends TangoException {
  constructor() {
    super(`We failed to parse the AI response`, 'AI_PARSE_FAILED', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}

export class NoPasswordOrTokenException extends TangoException {
  constructor() {
    super(`Cannot reset without a password or a token`, 'NO_PASSWORD_OR_TOKEN', HttpStatus.BAD_REQUEST);
  }
}

export class FeedSyncFailedException extends TangoException {
  constructor(feed: string) {
    super(`Failed to sync ${feed} feed`, 'FEED_SYNC_FAILED', HttpStatus.BAD_GATEWAY);
  }
}

export class ReservationUpdateNotPossibleException extends TangoException {
  constructor(reason: string) {
    super(`Reservation update couldn't be made because ${reason}`, 'RESERVATION_NOT_UPDATED', HttpStatus.BAD_REQUEST);
  }
}

export class WaitlistUpdateNotPossibleException extends TangoException {
  constructor(reason: string) {
    super(`Waitlist update couldn't be made because ${reason}`, 'WAITLIST_NOT_UPDATED', HttpStatus.BAD_REQUEST);
  }
}

export class ReservationNotPossibleException extends TangoException {
  constructor(reason: string) {
    super(`Reservation couldn't be made because ${reason}`, 'RESERVATION_NOT_POSSIBLE', HttpStatus.BAD_REQUEST);
  }
}

export class WalkInUpdateNotPossibleException extends TangoException {
  constructor(reason: string) {
    super(`Walk In update couldn't be made because ${reason}`, 'WALKIN_NOT_POSSIBLE', HttpStatus.BAD_REQUEST);
  }
}

export class WalkInNotPossibleException extends TangoException {
  constructor(reason: string) {
    super(`Walk In couldn't be made because ${reason}`, 'RESERVATION_NOT_POSSIBLE', HttpStatus.BAD_REQUEST);
  }
}
export class WalkInNotFoundException extends TangoException {
  constructor(walkIn: string) {
    super(`Walk in ${walkIn} not found`, 'WALKIN_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class WaitlistNotPossibleException extends TangoException {
  constructor(reason: string) {
    super(`Waitlist couldn't be made because ${reason}`, 'WAITLIST_NOT_POSSIBLE', HttpStatus.BAD_REQUEST);
  }
}

export class ReservationNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Reservation ${id} not found`, 'RESERVATION_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class WaitlistNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Waitlist ${id} not found`, 'WAITLIST_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class ServicePeriodNotPossibleException extends TangoException {
  constructor(reason: string) {
    super(`Service period couldn't be made because ${reason}`, 'SERVICE_PERIOD_NOT_POSSIBLE', HttpStatus.BAD_REQUEST);
  }
}

export class ServicePeriodNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Service period ${id} not found`, 'SERVICE_PERIOD_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class InternetReaderNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Internet reader ${id} not found`, 'INTERNET_READER_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}
export class InternetReaderNotReachableException extends TangoException {
  constructor(id: string) {
    super(`Internet reader ${id} not reachable`, 'INTERNET_READER_NOT_REACHABLE', HttpStatus.BAD_REQUEST);
  }
}
export class InternetReaderNotSpecifiedException extends TangoException {
  constructor() {
    super(`No reader id or internet reader id was provided`, 'INTERNET_READER_NOT_SPECIFIED', HttpStatus.BAD_REQUEST);
  }
}

export class InternetReaderWithReaderIdNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Internet reader with stripe reader id ${id} not found`, 'INTERNET_READER_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class InternetReaderStripeDeleteException extends TangoException {
  constructor() {
    super(`Error deleting the stripe reader in Stripe dashboard`, 'ERROR_DELETING_INTERNET_READER', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}

export class TabAlreadyPreAuthorizedException extends TangoException {
  constructor(tab: string) {
    super(`Tab ${tab} already has a preauth`, 'TAB_ALREADY_PREAUTHORIZED', HttpStatus.CONFLICT);
  }
}

export class PreAuthNotFoundException extends TangoException {
  constructor(preAuth: string) {
    super(`PreAuth ${preAuth} not found`, 'PREAUTH_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class CustomerNotFoundException extends TangoException {
  constructor(customer: string) {
    super(`Customer ${customer} not found`, 'CUSTOMER_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class BlackoutDateNotPossibleException extends TangoException {
  constructor(reason: string) {
    super(`Blackout date couldn't be made because ${reason}`, 'BLACKOUT_DATE_NOT_POSSIBLE', HttpStatus.BAD_REQUEST);
  }
}

export class TableDutyNotPossibleException extends TangoException {
  constructor(reason: string) {
    super(`Table duty couldn't be made because ${reason}`, 'TABLE_DUTY_NOT_POSSIBLE', HttpStatus.BAD_REQUEST);
  }
}

export class NoNotificationSetException extends TangoException {
  constructor(userId: string) {
    super(`No notification set for user ${userId}`, 'NO_NOTIFICATION_SET', HttpStatus.NOT_FOUND);
  }
}

export class UnauthorizedBusinessException extends TangoException {
  constructor(businessId: string) {
    super(`Not authorized for business ${businessId}`, 'UNAUTHORIZED_BUSINESS', HttpStatus.FORBIDDEN);
  }
}
export class UnauthorizedEnterpriseException extends TangoException {
  constructor(enterpriseId: string) {
    super(`Not authorized for enterprise ${enterpriseId}`, 'UNAUTHORIZED_ENTERPRISE', HttpStatus.FORBIDDEN);
  }
}

export class UnauthorizedAccessException extends TangoException {
  constructor(entity: string, identity?: string) {
    super(identity ? `Not authorized to access ${entity} ${identity}` : `Not authorized to access ${entity}`, 'UNAUTHORIZED_ACCESS', HttpStatus.FORBIDDEN);
  }
}

/**
 * BANKING ERRORS
 */

export class UnableToLoginToAirwallexException extends TangoException {
  constructor() {
    super(`Unable to login to Airwallex`, 'UNABLE_TO_LOGIN_TO_AIRWALLEX', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}

export class AirwallexRequestFailedException extends TangoException {
  constructor() {
    super(`Request to Airwallex failed`, 'AIRWALLEX_REQUEST_FAILED', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}

export class OatfiRequestFailedException extends TangoException {
  constructor() {
    super(`Request to Oatfi failed`, 'OATFI_REQUEST_FAILED', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}

export class AirwallexAccountNotFoundForIDException extends TangoException {
  constructor(id: string) {
    super(`Airwallex account not found for id ${id}`, 'AIRWALLEX_ACCOUNT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class InvalidBeneficiaryTypeException extends TangoException {
  constructor(currency: string, beneficiaryType: string) {
    super(`A ${currency} account cannot create a ${beneficiaryType} beneficiary`, 'INVALID_BENEFICIARY_TYPE', HttpStatus.BAD_REQUEST);
  }
}

export class AirwallexCardholderInfoMissingException extends TangoException {
  constructor(info: string[]) {
    super(
      `Cannot create Airwallex Cardholder because staff member is missing key information: ${info.join(', ')}`,
      'AIRWALLEX_CARDHOLDER_INFO_MISSING',
      HttpStatus.BAD_REQUEST,
    );
  }
}

export class TangoCardholderNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Tango Cardholder not found for ID: ${id}`, 'TANGO_CARDHOLDER_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class InvalidBeneficiaryDetailsException extends TangoException {
  constructor() {
    super(`Invalid beneficiary details`, 'INVALID_BENEFICIARY_DETAILS', HttpStatus.BAD_REQUEST);
  }
}

export class BeneficiaryNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Beneficiary not found for ID: ${id}`, 'BENEFICIARY_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class FailedToDeleteBeneficiaryException extends TangoException {
  constructor(id: string) {
    super(`Failed to delete beneficiary with ID: ${id}`, 'FAILED_TO_DELETE_BENEFICIARY', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}

export class BeneficiaryAccountNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Beneficiary for account ID ${id} not found`, 'BENEFICIARY_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class TransactionNotFoundException extends TangoException {
  constructor(id: string, type: string = 'transaction') {
    super(`Transaction not found for ID: ${id} (${type})`, 'TRANSACTION_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class FailedToLinkAccountException extends TangoException {
  constructor() {
    super(`Failed to link account`, 'FAILED_TO_LINK_ACCOUNT', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}

export class PlaidCALinkFailedException extends TangoException {
  constructor() {
    super(`Failed to link account with Plaid`, 'PLAID_CA_LINK_FAILED', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}

export class PhysicalCardAlreadyExistsException extends TangoException {
  constructor() {
    super(`Physical card already exists`, 'PHYSICAL_CARD_ALREADY_EXISTS', HttpStatus.CONFLICT);
  }
}

export class NoShippingAddressException extends TangoException {
  constructor() {
    super(`No shipping address provided`, 'NO_SHIPPING_ADDRESS', HttpStatus.BAD_REQUEST);
  }
}

export class ProofOfPaymentImageInvalidException extends TangoException {
  constructor() {
    super(`Proof of payment image is invalid`, 'PROOF_OF_PAYMENT_IMAGE_INVALID', HttpStatus.BAD_REQUEST);
  }
}

export class TangoCardNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Tango Card not found for ID: ${id}`, 'TANGO_CARD_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class ControlGroupNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Control Group not found for ID: ${id}`, 'CONTROL_GROUP_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class ProofOfPaymentsNotFoundException extends TangoException {
  constructor() {
    super(`One or more proof of payments not found`, 'PROOF_OF_PAYMENTS_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class DebitDepositNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Debit deposit not found for ID: ${id}`, 'DEBIT_DEPOSIT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class BalanceInsufficientException extends TangoException {
  constructor() {
    super(`Balance is insufficient`, 'BALANCE_INSUFFICIENT', HttpStatus.BAD_REQUEST);
  }
}

export class NotACashAccountException extends TangoException {
  constructor(id: string) {
    super(`Bank account ${id} is not a cash account`, 'NOT_A_CASH_ACCOUNT', HttpStatus.BAD_REQUEST);
  }
}

export class CreditAccountExistsException extends TangoException {
  constructor() {
    super(`Tango Charge account already exists`, 'CHARGE_ACCOUNT_EXISTS', HttpStatus.CONFLICT);
  }
}

export class NoEmailException extends TangoException {
  constructor() {
    super(`Email address not provided`, 'NO_EMAIL', HttpStatus.BAD_REQUEST);
  }
}

export class PhoneNumberNotVerifiedException extends TangoException {
  constructor(phone: string) {
    super(`Phone number ${phone} not verified`, 'PHONE_NOT_VERIFIED', HttpStatus.BAD_REQUEST);
  }
}

export class EmailNotVerifiedException extends TangoException {
  constructor(email: string) {
    super(`Email address ${email} not verified`, 'EMAIL_NOT_VERIFIED', HttpStatus.BAD_REQUEST);
  }
}

export class PhoneNumberNotOwnersException extends TangoException {
  constructor(phone: string, ownerPhone: string) {
    super(`Phone number ${phone} does not match the owner phone number (${ownerPhone})`, 'PHONE_NOT_OWNER', HttpStatus.CONFLICT);
  }
}

export class EmailNotOwnersException extends TangoException {
  constructor(email: string, ownerEmail: string) {
    super(`Email address ${email} does not match the owner email address (${ownerEmail})`, 'EMAIL_NOT_OWNER', HttpStatus.CONFLICT);
  }
}

export class CannotOverpayException extends TangoException {
  constructor() {
    super(`Cannot overpay outstanding balance on Tango Charge Account`, 'CANNOT_OVERPAY', HttpStatus.BAD_REQUEST);
  }
}

export class NoMergeIntegrationException extends TangoException {
  constructor() {
    super(`No merge integration found for this account`, 'NO_MERGE_INTEGRATION', HttpStatus.BAD_REQUEST);
  }
}

export class MergeLivemodeDisabledException extends TangoException {
  constructor() {
    super(`Live mode is disabled for merge in the environment`, 'MERGE_LIVEMODE_DISABLED', HttpStatus.FORBIDDEN);
  }
}

export class InvalidSalesProjectionException extends TangoException {
  constructor(reason: string) {
    super(`Invalid sales projection. ${reason}`, 'INVALID_SALES_PROJECTION', HttpStatus.BAD_REQUEST);
  }
}

export class BudgetNotFoundException extends TangoException {
  constructor(budget: string) {
    super(`Budget ${budget} not found`, 'BUDGET_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class NoForexQuoteForForeignTransferException extends TangoException {
  constructor() {
    super(`No forex quote for foreign transfer`, 'NO_FOREX_QUOTE', HttpStatus.BAD_REQUEST);
  }
}

export class ForexQuoteExpiredException extends TangoException {
  constructor() {
    super(`Forex quote expired`, 'FOREX_QUOTE_EXPIRED', HttpStatus.BAD_REQUEST);
  }
}

export class ForexQuoteNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Forex quote not found for ID: ${id}`, 'FOREX_QUOTE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class RecurringCostOverlapException extends TangoException {
  constructor() {
    super(`Recurring cost conflicts with another recurring cost`, 'RECURRING_COST_OVERLAP', HttpStatus.CONFLICT);
  }
}

export class PartnerAccountNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Partner account not found for ID: ${id}`, 'PARTNER_ACCOUNT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class PenaltyTemplateNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Penalty template not found for ID: ${id}`, 'PENALTY_TEMPLATE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class ProofOfPaymentNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Proof of payment not found for ID: ${id}`, 'PROOF_OF_PAYMENT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class DemandNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Demand not found for ID: ${id}`, 'DEMAND_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class PenaltyNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Penalty not found for ID: ${id}`, 'PENALTY_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class DemandAlreadyResolvedException extends TangoException {
  constructor(id: string) {
    super(`Demand already resolved for ID: ${id}`, 'DEMAND_ALREADY_RESOLVED', HttpStatus.CONFLICT);
  }
}

export class FailedToResolveDemandException extends TangoException {
  constructor(id: string) {
    super(`Failed to resolve demand for ID: ${id}`, 'FAILED_TO_RESOLVE_DEMAND', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}

export class DebitChargeNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Debit charge not found for ID: ${id}`, 'DEBIT_CHARGE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class TangoEscrowAccountNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Tango Escrow account not found for ID: ${id}`, 'TANGO_ESCROW_ACCOUNT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class TangoEscrowAccountExistsException extends TangoException {
  constructor() {
    super(`Tango Escrow account already exists`, 'TANGO_ESCROW_ACCOUNT_EXISTS', HttpStatus.CONFLICT);
  }
}

export class InsightDialogueNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Insight dialogue not found for ID: ${id}`, 'INSIGHT_DIALOGUE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class InsightDialogueFailedException extends TangoException {
  constructor() {
    super(`Unable to provide a response`, 'INSIGHT_DIALOGUE_FAILED', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}

export class ForexQuoteAmountMismatchException extends TangoException {
  constructor() {
    super(`Forex quote amount mismatch`, 'FOREX_QUOTE_AMOUNT_MISMATCH', HttpStatus.BAD_REQUEST);
  }
}

export class LinkedAccountNotFoundException extends TangoException {
  constructor() {
    super(`Linked account not found`, 'LINKED_ACCOUNT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class TransferPaymentNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Transfer payment not found for ID: ${id}`, 'TRANSFER_PAYMENT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class SalesProjectionNotFoundException extends TangoException {
  constructor(category: string) {
    super(`Sales projection not found for category: ${category}`, 'SALES_PROJECTION_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class RecurringCostNotFoundException extends TangoException {
  constructor(name: string) {
    super(`Recurring cost not found for name: ${name}`, 'RECURRING_COST_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class NoTabProfitabilityDataException extends TangoException {
  constructor(id: string) {
    super(`Tab ${id} has no profitability data`, 'NO_TAB_PROFITABILITY_DATA', HttpStatus.NOT_FOUND);
  }
}

export class PotentialBusinessNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Potential business not found for ID: ${id}`, 'POTENTIAL_BUSINESS_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class PotentialMenuNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Potential menu not found for ID: ${id}`, 'POTENTIAL_MENU_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class GooglePlacesIdNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Google Places ID not found for ID: ${id}`, 'GOOGLE_PLACES_ID_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class GooglePlacesIdTooLongException extends TangoException {
  constructor() {
    super(`Google Places ID is too long`, 'GOOGLE_PLACES_ID_TOO_LONG', HttpStatus.BAD_REQUEST);
  }
}

export class IncorrectAccountTypeException extends TangoException {
  constructor(id: string, accountType: string) {
    super(`Account ${id} is not a ${accountType} account`, 'INCORRECT_ACCOUNT_TYPE', HttpStatus.BAD_REQUEST);
  }
}

export class AccountBalanceNotFoundException extends TangoException {
  constructor(id: string, currency: string) {
    super(`Account ${id} has no balance for currency ${currency}`, 'ACCOUNT_BALANCE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class NothingToRepayException extends TangoException {
  constructor() {
    super(`Nothing to repay`, 'NOTHING_TO_REPAY', HttpStatus.BAD_REQUEST);
  }
}

export class ForexConversionAmountDoesNotMatchQuoteException extends TangoException {
  constructor(buyAmount: number, quoteAmount: number) {
    super(`Buy amount ${buyAmount} does not match quote amount ${quoteAmount}`, 'FOREX_CONVERSION_AMOUNT_DOES_NOT_MATCH_QUOTE', HttpStatus.BAD_REQUEST);
  }
}

export class ForexConversionQuoteDeltaIsNegativeException extends TangoException {
  constructor(delta: number) {
    super(
      `Forex conversion quote delta is negative: ${delta}. Cannot perform conversion because it would result in a loss.`,
      'FOREX_CONVERSION_QUOTE_DELTA_IS_NEGATIVE',
      HttpStatus.BAD_REQUEST,
    );
  }
}

export class FlowHasNoStepsException extends TangoException {
  constructor(initial = false) {
    super(`Flow has no${initial ? ' initial steps' : ' steps'}`, 'FLOW_HAS_NO_STEPS', HttpStatus.BAD_REQUEST);
  }
}

export class FlowStepFailedException extends TangoException {
  constructor(flowStepId: string, type: string = 'step') {
    super(`Failed to process next steps for flow step ${flowStepId} (${type})`, 'FLOW_STEP_FAILED', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}

export class QuickPayReferenceNotFoundException extends TangoException {
  constructor(id: string) {
    super(`QuickPay reference not found for ID: ${id}`, 'QUICKPAY_REFERENCE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class LegalLocationNotFoundException extends TangoException {
  constructor() {
    super(`Legal location not found`, 'LEGAL_LOCATION_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class BusinessStartDateNotSetException extends TangoException {
  constructor() {
    super(`Business start date not set`, 'BUSINESS_START_DATE_NOT_SET', HttpStatus.BAD_REQUEST);
  }
}

export class InvalidPhoneNumberException extends TangoException {
  constructor(phoneNumber: string) {
    super(`Invalid phone number: ${phoneNumber}`, 'INVALID_PHONE_NUMBER', HttpStatus.BAD_REQUEST);
  }
}

export class UnderwritingRecordNotFoundException extends TangoException {
  constructor() {
    super(`Underwriting record not found`, 'UNDERWRITING_RECORD_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class CompetitorNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Competitor not found for ID: ${id}`, 'COMPETITOR_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class CardTransactionClumpNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Card transaction clump not found for ID: ${id}`, 'CARD_TRANSACTION_CLUMP_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class DailyWithdrawalLimitNotSetException extends TangoException {
  constructor() {
    super(`Daily withdrawal limit not set`, 'DAILY_WITHDRAWAL_LIMIT_NOT_SET', HttpStatus.BAD_REQUEST);
  }
}

export class GlobalAccountNotFoundException extends TangoException {
  constructor() {
    super(`Global account not found`, 'GLOBAL_ACCOUNT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class GlobalAccountDoesNotSupportDirectDebitException extends TangoException {
  constructor() {
    super(`Global account does not support direct debit`, 'GLOBAL_ACCOUNT_DOES_NOT_SUPPORT_DIRECT_DEBIT', HttpStatus.BAD_REQUEST);
  }
}

export class ExistingOatfiPaymentMethodDoesNotMatchException extends TangoException {
  constructor() {
    super(`Existing Oatfi payment method does not match`, 'EXISTING_OATFI_PAYMENT_METHOD_DOES_NOT_MATCH', HttpStatus.BAD_REQUEST);
  }
}

export class OatfiPaymentMethodNotFoundException extends TangoException {
  constructor() {
    super(`Oatfi payment method not found`, 'OATFI_PAYMENT_METHOD_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class NoURLForPotentialMenuException extends TangoException {
  constructor(id: string) {
    super(`No URL for potential menu for ID: ${id}`, 'NO_URL_FOR_POTENTIAL_MENU', HttpStatus.BAD_REQUEST);
  }
}

export class PotentialMenuAlreadyExistsException extends TangoException {
  constructor(id: string) {
    super(`Potential menu already exists for ID: ${id}`, 'POTENTIAL_MENU_ALREADY_EXISTS', HttpStatus.CONFLICT);
  }
}

export class PotentialMenuConflictException extends TangoException {
  constructor(url: string) {
    super(`Potential menu conflict for URL: ${url}`, 'POTENTIAL_MENU_CONFLICT', HttpStatus.CONFLICT);
  }
}
export class MissingProcessingRateException extends TangoException {
  constructor(feeType: string) {
    super(`${feeType} is missing. A Valid ${feeType} must be provided`, 'MISSING_PROCESSING_RATE', HttpStatus.BAD_REQUEST);
  }
}

export class NoRepaymentMethodFoundException extends TangoException {
  constructor() {
    super(`No repayment method found`, 'NO_REPAYMENT_METHOD_FOUND', HttpStatus.BAD_REQUEST);
  }
}

export class OatfiRepaymentFailedException extends TangoException {
  constructor() {
    super(`Oatfi repayment failed`, 'OATFI_REPAYMENT_FAILED', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}

export class PlaidAccessTokenInvalidException extends TangoException {
  constructor() {
    super(`Plaid access token is invalid`, 'PLAID_ACCESS_TOKEN_INVALID', HttpStatus.BAD_REQUEST);
  }
}

export class OnboardingStateNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Onboarding state not found for ID: ${id}`, 'ONBOARDING_STATE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class OnboardingSubmittedException extends TangoException {
  constructor() {
    super(`Onboarding already submitted`, 'ONBOARDING_SUBMITTED', HttpStatus.BAD_REQUEST);
  }
}

export class OnboardingNotReadyException extends TangoException {
  constructor() {
    super(`Onboarding not ready for submission`, 'ONBOARDING_NOT_READY', HttpStatus.BAD_REQUEST);
  }
}

export class TangoLeadNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Tango lead not found for ID: ${id}`, 'TANGO_LEAD_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class SwiftFeeQuoteNotFoundException extends TangoException {
  constructor() {
    super(`Swift fee quote not found`, 'SWIFT_FEE_QUOTE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class SwiftFeeQuoteInvalidException extends TangoException {
  constructor() {
    super(`Swift fee quote is invalid`, 'SWIFT_FEE_QUOTE_INVALID', HttpStatus.BAD_REQUEST);
  }
}

export class SwiftPaymentNotSupportedException extends TangoException {
  constructor() {
    super(`Swift payment is not supported`, 'SWIFT_PAYMENT_NOT_SUPPORTED', HttpStatus.BAD_REQUEST);
  }
}

export class RecipientAlreadyExistsException extends TangoException {
  constructor() {
    super(`Recipient already exists`, 'RECIPIENT_ALREADY_EXISTS', HttpStatus.CONFLICT);
  }
}

export class NotAuthorisedToAddRecipientException extends TangoException {
  constructor() {
    super(`Not authorised to add recipient`, 'NOT_AUTHORISED_TO_ADD_RECIPIENT', HttpStatus.FORBIDDEN);
  }
}

export class BusinessAndEnterpriseCannotBeSetException extends TangoException {
  constructor() {
    super(`Business and enterprise cannot be set at the same time`, 'BUSINESS_AND_ENTERPRISE_CANNOT_BE_SET', HttpStatus.BAD_REQUEST);
  }
}

export class RecipientNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Recipient not found for ID: ${id}`, 'RECIPIENT_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class WeekStartNotMondayException extends TangoException {
  constructor() {
    super(`Week start is not Monday`, 'WEEK_START_NOT_MONDAY', HttpStatus.BAD_REQUEST);
  }
}

export class WeekEndInFutureException extends TangoException {
  constructor() {
    super(`Week end is in the future`, 'WEEK_END_IN_FUTURE', HttpStatus.BAD_REQUEST);
  }
}

export class InvalidDateRangeException extends TangoException {
  constructor(start: string, end: string, reason: string) {
    super(`Invalid date range: ${start} to ${end} because ${reason}`, 'INVALID_DATE_RANGE', HttpStatus.BAD_REQUEST);
  }
}

export class PreAuthorisedSessionNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Pre-authorised session not found for ID: ${id}`, 'PRE_AUTHORISED_SESSION_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class AtLeastOneBusinessOrEnterpriseRequiredException extends TangoException {
  constructor() {
    super(`At least one business or enterprise is required`, 'AT_LEAST_ONE_BUSINESS_OR_ENTERPRISE_REQUIRED', HttpStatus.BAD_REQUEST);
  }
}

export class NoToastIdException extends TangoException {
  constructor() {
    super(`No Toast ID provided`, 'NO_TOAST_ID', HttpStatus.BAD_REQUEST);
  }
}

export class NoSftpUrlException extends TangoException {
  constructor() {
    super(`No SFTP URL provided`, 'NO_SFTP_URL', HttpStatus.BAD_REQUEST);
  }
}

export class DataLinkAlreadyExistsException extends TangoException {
  constructor(businessId: string, source: string) {
    super(`Data link already exists for business ${businessId} and source ${source}`, 'DATA_LINK_ALREADY_EXISTS', HttpStatus.CONFLICT);
  }
}

export class LinkInfoDoesNotMatchLinkTypeException extends TangoException {
  constructor(linkId: string) {
    super(`Link info does not match link type: ${linkId}`, 'LINK_INFO_DOES_NOT_MATCH_LINK_TYPE', HttpStatus.BAD_REQUEST);
  }
}

export class IncorrectDataSourceTypeToProcessException extends TangoException {
  constructor(linkId: string, expected: string, actual: string) {
    super(`Incorrect data source type to process: ${linkId} (expected: ${expected}, actual: ${actual})`, 'INCORRECT_DATA_SOURCE_TYPE_TO_PROCESS', HttpStatus.BAD_REQUEST);
  }
}

export class NoMarginEdgeRestaurantUnitIdException extends TangoException {
  constructor() {
    super(`No Margin Edge restaurant unit ID provided`, 'NO_MARGIN_EDGE_RESTAURANT_UNIT_ID', HttpStatus.BAD_REQUEST);
  }
}

export class NoMarginEdgeApiKeyException extends TangoException {
  constructor() {
    super(`No Margin Edge API key provided`, 'NO_MARGIN_EDGE_API_KEY', HttpStatus.BAD_REQUEST);
  }
}

export class InvalidInventoryItemDataException extends TangoException {
  constructor(message: string) {
    super(`Invalid inventory item data: ${message}`, 'INVALID_INVENTORY_ITEM_DATA', HttpStatus.BAD_REQUEST);
  }
}

export class DataLinkNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Data link not found for ID: ${id}`, 'DATA_LINK_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class IncorrectHarvestTypeException extends TangoException {
  constructor(harvestType: string, expected: string[]) {
    super(`Incorrect harvest type: ${harvestType} (expected one of: ${expected.join(', ')})`, 'INCORRECT_HARVEST_TYPE', HttpStatus.BAD_REQUEST);
  }
}

export class MarginEdgeApiException extends TangoException {
  constructor(message: string) {
    super(`Margin Edge API error: ${message}`, 'MARGIN_EDGE_API_ERROR', HttpStatus.BAD_REQUEST);
  }
}

export class NoMarginEdgeTokenException extends TangoException {
  constructor() {
    super(`No Margin Edge token provided`, 'NO_MARGIN_EDGE_TOKEN', HttpStatus.BAD_REQUEST);
  }
}

export class ForecastWindowNotValidException extends TangoException {
  constructor(reason: string) {
    super(`Forecast window is not valid because ${reason}`, 'FORECAST_WINDOW_NOT_VALID', HttpStatus.BAD_REQUEST);
  }
}

export class RemoteHarvesterNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Remote harvester not found for ID: ${id}`, 'REMOTE_HARVESTER_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class ExpiredChallengeException extends TangoException {
  constructor() {
    super(`Expired challenge`, 'EXPIRED_CHALLENGE', HttpStatus.BAD_REQUEST);
  }
}

export class PublicKeyChangedException extends TangoException {
  constructor() {
    super(`Public key changed`, 'PUBLIC_KEY_CHANGED', HttpStatus.UNAUTHORIZED);
  }
}

export class InvalidSignatureException extends TangoException {
  constructor() {
    super(`Invalid signature`, 'INVALID_SIGNATURE', HttpStatus.UNAUTHORIZED);
  }
}

export class JobTypeNotFeasibleException extends TangoException {
  constructor(serviceName: string, jobType: string) {
    super(`Job type ${jobType} is not feasible for service ${serviceName}`, 'JOB_TYPE_NOT_FEASIBLE', HttpStatus.BAD_REQUEST);
  }
}

export class RemoteHarvesterJobNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Remote harvester job not found for ID: ${id}`, 'REMOTE_HARVESTER_JOB_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class RemoteHarvesterCredentialNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Remote harvester credential not found for ID: ${id}`, 'REMOTE_HARVESTER_CREDENTIAL_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class ReviewableNotFoundException extends TangoException {
  constructor(id: string) {
    super(`Reviewable not found for ID: ${id}`, 'REVIEWABLE_NOT_FOUND', HttpStatus.NOT_FOUND);
  }
}

export class SerpApiException extends TangoException {
  constructor(message: string) {
    super(`SERP API Error: ${message}`, 'SERP_API_ERROR', HttpStatus.BAD_GATEWAY);
  }
}

export class DatabaseException extends TangoException {
  constructor(operation: string, details?: string) {
    super(`Database operation failed: ${operation}${details ? ` - ${details}` : ''}`, 'DATABASE_ERROR', HttpStatus.INTERNAL_SERVER_ERROR);
  }
}

export class InvalidQueryException extends TangoException {
  constructor() {
    super(`Invalid query`, 'INVALID_QUERY', HttpStatus.BAD_REQUEST);
  }
}
