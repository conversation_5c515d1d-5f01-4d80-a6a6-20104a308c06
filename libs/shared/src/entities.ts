import { User, UserAddress } from 'apps/auth/src/entities/user.entity';
import { Business } from 'apps/business/src/entities/business/business.entity';
import { BusinessEnterpriseLink } from 'apps/business/src/entities/business/business-enterprise.entity';
import { Enterprise } from 'apps/business/src/entities/enterprise/enterprise.entity';
import { Contract, ContractSection, LineItem } from 'apps/business/src/entities/business/contract.entity';
import { MenuCategory, MenuCategoryProduct } from 'apps/menu/src/entities/menu-category.entity';
import { Menu, MenuCategoryLink } from 'apps/menu/src/entities/menu.entity';
import { PublishedMenu } from 'apps/menu/src/entities/published-menu.entity';

import { ModifierOption, BusinessModifierOption } from 'apps/menu/src/entities/modifier-option.entity';
import { Modifier, BusinessModifier, ModifierModifierOption } from 'apps/menu/src/entities/modifier.entity';
import { Product, BusinessProduct, ProductModifier } from 'apps/menu/src/entities/product.entity';
import { QRCode } from 'apps/qrcodes/src/entities/qr-code.entity';
import {
  Location,
  Allergen,
  Unit,
  PrimaryGroup,
  SecondaryGroup,
  UnitConversion,
  Item,
  Recipe,
  BusinessItem,
  Ingredient,
  ScheduledCount,
  Count,
  CountItem,
  VendorAccount,
  ManualVendor,
  IntegratedVendor,
  Offering,
  IntegratedOffering,
  Invoice,
  InvoiceExpense,
  ItemPurchase,
} from 'apps/inventory/src/entities';
import { Tab } from 'apps/ordering/src/entities/tabs/tabs.entity';
import { Order } from 'apps/ordering/src/entities/orders/orders.entity';
import { Payment } from 'apps/ordering/src/entities/payments/payments.entity';
import { ProductInOrder } from 'apps/ordering/src/entities/orders/products-in-order.entity';
import { ProductPaid } from 'apps/ordering/src/entities/payments/products-paid.entity';
import { CashEvent } from 'apps/ordering/src/entities/cash-events/cash-events.entity';
import { BusinessHour } from 'apps/business/src/entities/business/business-hour.entity';
import { BusinessLocation } from 'apps/business/src/entities/business/location.entity';
import { CashDrawer } from 'apps/business/src/entities/business/cash-drawer.entity';
import { InternetReader } from 'apps/business/src/entities/business/internet-reader.entity';
import { Printer } from 'apps/business/src/entities/business/printer.entity';
import { ServiceArea } from 'apps/booking/src/entities/service-area.entity';
import { VoidedProduct } from 'apps/ordering/src/entities/payments/voided-products.entity';
import { TableSession } from 'apps/ordering/src/entities/table-sessions/table-session.entity';
import { Table } from 'apps/booking/src/entities/table.entity';
import {
  BusinessSetting,
  DeliverectSetting,
  GeneralServiceSetting,
  GeneralSetting,
  LoyaltyAndGiftCardsSetting,
  OnlineOrderingSetting,
  InventorySetting,
  OperatingHoursSetting,
  POSSetting,
  RatingsAndReviewsSetting,
  SchedulingSetting,
  ShiftTypesSetting,
  PrintingSetting,
  ForecastingSetting,
} from 'apps/business/src/entities/business/settings.entity';
import { MenuHour } from 'apps/menu/src/entities/menu-hours.entity';
import { Tax } from 'apps/business/src/entities/business/tax.entity';
import {
  BusinessDiscountLink,
  Discount,
  DiscountQuantityItem,
  DiscountQuantityItemBasket,
  DiscountAvailability,
  DiscountBogo,
  DiscountCombos,
} from 'apps/menu/src/entities/discount/discount.entity';
import { GiftCard } from 'apps/gift-cards/src/entities/gift-card.entity';
import { KitchenDisplaySystem } from 'apps/kds/src/entities/kds.entity';
import { KDSIntervals } from 'apps/kds/src/entities/intervals.entity';
import { StripeRefund } from 'apps/ordering/src/entities/payments/stripe-refund.entity';
import { CashRefund } from 'apps/ordering/src/entities/payments/cash-refund.entity';
import { KDSOrder } from 'apps/kds/src/entities/order.entity';
import { KDSOrderItem } from 'apps/kds/src/entities/order-item.entity';
import { PrintJob } from 'apps/printing/src/entities/printing.entity';
import { KDSCompleted } from 'apps/kds/src/entities/completed.entity';
import { DiscountPromocode } from 'apps/menu/src/entities/discount/promocode.entity';
import { GiftCardTransaction } from 'apps/gift-cards/src/entities/gift-card-transaction-history.entity';
import { Competitor, Review, ReviewMedia, ReviewResponse, ReviewResponseTemplate } from 'apps/reputation/src/entities/review.entity';
import { UnityReport } from 'apps/reporting/src/entities/UnityReport.entity';
import { ScheduledUnityReport } from 'apps/reporting/src/entities/ScheduledUnityReport';
import { LoginSession } from 'apps/auth/src/entities/session.entity';
import { StaffPaymentInfo } from 'apps/staffing/src/entities/staff/staff-payment-info.entity';
import { StaffAvailability, StaffDayAvailability } from 'apps/scheduling/src/entities/scheduling/staff-availability.entity';
import { StaffMember } from 'apps/staffing/src/entities/staff/staff-member.entity';
import { Invitation, InvitationPosition } from 'apps/staffing/src/entities/staff/invitation.entity';
import { PayRate } from 'apps/staffing/src/entities/payroll/pay-rate.entity';
import { Shift } from 'apps/scheduling/src/entities/scheduling/shift.entity';
import { EmergencyContact } from 'apps/staffing/src/entities/staff/emergency-contact.entity';
import { Department } from 'apps/business/src/entities/enterprise/department.entity';
import { Role } from 'apps/business/src/entities/enterprise/role.entity';
import { TimeOffRequest } from 'apps/scheduling/src/entities/scheduling/time-off-request.entity';
import { CoverRequest, CoverRequestOffer } from 'apps/scheduling/src/entities/scheduling/cover-request.entity';
import { ShiftTemplate } from 'apps/scheduling/src/entities/scheduling/shift-template.entity';
import { TradeRequest, TradeRequestOffer } from 'apps/scheduling/src/entities/scheduling/trade-request.entity';
import { DropRequest } from 'apps/scheduling/src/entities/scheduling/drop-request.entity';
import { DailyLogReply } from 'apps/scheduling/src/entities/dailylogs/daily-log-reply.entity';
import { DailyLogReaction } from 'apps/scheduling/src/entities/dailylogs/daily-log-reaction.entity';
import { DepartmentTarget, LaborTarget } from 'apps/scheduling/src/entities/targets/labor-target.entity';
import { DailyLog } from 'apps/scheduling/src/entities/dailylogs/daily-log.entity';
import { RoleTimeOffSetting } from 'apps/scheduling/src/entities/settings/time-off-setting.entity';
import { BlackoutDate } from 'apps/scheduling/src/entities/settings/blackout-date.entity';
import { WorkEvent } from 'apps/staffing/src/entities/payroll/work-event.entity';
import { BusinessDiscount as OldBusinessDiscount } from 'apps/menu/src/entities/business/business-discount.entity';
import { Permission } from 'apps/staffing/src/entities/staff/permission.entity';
import { HelpGuide } from 'apps/business/src/entities/business/help.entity';
import { Payout } from 'apps/business/src/entities/business/payout.entity';
import { PriceLevel, PriceLevelPrice } from 'apps/menu/src/entities/price-level.entity';
import { CategoryPriceLevel, PriceVariant } from 'apps/menu/src/entities/price-variant.entity';
import {
  TipPool,
  TipPoolContributor,
  TipPoolContributorProductTypeFilter,
  TipPoolContributorServiceAreaFilter,
  TipPoolReceiver,
} from 'apps/staffing/src/entities/tipouts/tip-pool.entity';
import {
  TipPoolCalculation,
  TipPoolContribution,
  TipPoolStaffContribution,
  TipPoolDistribution,
  TipPoolStaffDistribution,
} from 'apps/staffing/src/entities/tipouts/tip-pool-calculation.entity';
import { BasicDiscount } from 'apps/menu/src/entities/basic-discount.entity';
import { TangoPrintController, TangoPrintJob, TangoPrinter, PrintersBlacklist } from 'apps/printing/src/entities/print.entity';
import { PayrollItem, OneOffPayment, BankAccount, Payroll } from 'apps/staffing/src/entities/payroll/payroll.entity';
import { ServiceAreaGeometries } from 'apps/booking/src/entities/service-area-geometries.entity';
import {
  Reservation,
  ReservationBlackoutDate,
  ReservationBlackoutPeriod,
  ServiceAreaPeriod,
  ServiceAreaPeriodTurnTime,
  ServicePeriod,
  ServicePeriodTime,
} from 'apps/booking/src/entities/reservation.entity';
import { PaysFor } from 'apps/ordering/src/entities/payments/pays-for.entity';
import { StripeSource } from 'apps/ordering/src/entities/payments/source.stripe.entity';
import { GiftCardSource } from 'apps/ordering/src/entities/payments/source.gift-card.entity';
import { CashSource } from 'apps/ordering/src/entities/payments/source.cash.entity';
import { PreAuth } from 'apps/ordering/src/entities/payments/pre-auth.entity';
import { WalkInParty } from 'apps/booking/src/entities/walk-in.entity';
import { ProcessingRate } from 'apps/business/src/entities/business/processing-rate.entity';
import { WaitlistParty } from 'apps/booking/src/entities/waitlist.entity';
import { LossSource } from 'apps/ordering/src/entities/payments/source.loss.entity';
import { ServiceFee, ServiceFeeRule } from 'apps/business/src/entities/business/service-fee.entity';
import { TangoDevice, TangoDeviceEvent } from 'apps/business/src/entities/business/tango-device.entity';
import { BusinessCustomer, Customer, CustomerPaymentMethod, DeliveryCustomer } from 'apps/business/src/entities/business/customer.entity';
import { TangoGiftCard, TangoGiftCardLoadEvent, TangoGiftCardSpendEvent } from 'apps/gift-cards/src/entities/tango-gift-card.entity';
import { TangoKDSCompleted } from 'apps/kds/src/entities/v2/tango-kds-completed.entity';
import { TangoKDSItem } from 'apps/kds/src/entities/v2/tango-kds-item.entity';
import { TangoKDSOrder } from 'apps/kds/src/entities/v2/tango-kds-order.entity';
import { TableDuty } from 'apps/business/src/entities/business/table-duty.entity';
import { CachedBusinessStats, CachedEnterpriseStats } from 'apps/reporting/src/entities/CachedBusinessStats.entity';
import { ReportingProduct } from 'apps/ordering/src/entities/reporting/reporting-product.entity';
import { ReportingProductEvent } from 'apps/ordering/src/entities/reporting/reporting-product-event.entity';
import { ExternalSource } from 'apps/ordering/src/entities/payments/source.external.entity';
import { ReportingNotification } from 'apps/reporting/src/entities/ReportingNotifications.entity';
import { TipOut } from 'apps/reporting/src/entities/Tipout.entity';
import { TangoBankAccount } from 'apps/banking/src/entities/tango-bank-account.entity';
import { GlobalAccount } from 'apps/banking/src/entities/global-account.entity';
import { AccountBalance, AccountBalanceHistory } from 'apps/banking/src/entities/balance.entity';
import { TangoCardholder } from 'apps/banking/src/entities/cardholder.entity';
import {
  ACHClearingAccount,
  Beneficiary,
  CheckClearingAccount,
  EFTClearingAccount,
  FedwireClearingAccount,
  InteracClearingAccount,
  SWIFTClearingAccount,
} from 'apps/banking/src/entities/beneficiary.entity';
import { BankAccountTransaction } from 'apps/banking/src/entities/transaction.entity';
import { TangoCard } from 'apps/banking/src/entities/tango-card.entity';
import { KDS } from 'apps/kds/src/entities/v3/kds.entity';
import { KDSStation } from 'apps/kds/src/entities/v3/kds-station.entity';
import { KDSChit } from 'apps/kds/src/entities/v3/kds-chit.entity';
import { KDSChitEvent } from 'apps/kds/src/entities/v3/kds-chit-event.entity';
import { KDSChitItem } from 'apps/kds/src/entities/v3/kds-chit-item.entity';
import { KDSChitItemEvent } from 'apps/kds/src/entities/v3/kds-chit-item-event.entity';
import { KDSStationItem } from 'apps/kds/src/entities/v3/kds-station-item.entity';
import { KDSStationItemEvent } from 'apps/kds/src/entities/v3/kds-station-item-event.entity';
import { InventoryEvent } from 'apps/inventory/src/entities/tracking/event.entity';
import { ControlGroup, ControlGroupInterval } from 'apps/banking/src/entities/control-group.entity';
import { LinkedAccount } from 'apps/banking/src/entities/linked-account.entity';
import { POPLineItem, ProofOfPayment } from 'apps/banking/src/entities/proof-of-payment.entity';
import { MergeIntegration } from 'apps/banking/src/entities/merge-integration.entity';
import {
  ActualRecurringCost,
  Budget,
  DepartmentBudget,
  IncidentalCost,
  ProjectionOverride,
  RecurringCost,
  SalesProjection,
} from 'apps/reporting/src/entities/Budget.entity';
import { Actuals } from 'apps/reporting/src/entities/Actuals.entity';
import { CachedStaffProfitability, CachedTabProfitability } from 'apps/reporting/src/entities/CachedPerformanceStats.entity';
import { ForexQuote } from 'apps/banking/src/entities/forex-quote.entity';
import { ForexPayment } from 'apps/banking/src/entities/forex-payment.entity';
import { Demand } from 'apps/banking/src/entities/demand.entity';
import { Penalty } from 'apps/banking/src/entities/penalty.entity';
import { Resolution } from 'apps/banking/src/entities/resolution.entity';
import { PenaltyTemplate } from 'apps/banking/src/entities/penalty-template.entity';
import { DebitCharge } from 'apps/banking/src/entities/debit-charge.entity';
import { MenuLabelsMappings } from 'apps/menu/src/entities/ai/MenuLabelsMappings.entity';
import { InsightDialogue, InsightDialogueAction, InsightDialogueMessage } from 'apps/reporting/src/entities/Dialog.entity';
import { PlatformFee } from 'apps/banking/src/entities/platform-fees.entity';
import { PotentialBusiness } from 'apps/ai/src/entities/potential-business.entity';
import { PotentialBusinessHour } from 'apps/ai/src/entities/potential-business-hour.entity';
import { PotentialBusinessLocation } from 'apps/ai/src/entities/potential-business-location.entity';
import { PotentialMenu, PotentialMenuBusiness, PotentialMenuScrapeAttempt } from 'apps/ai/src/entities/potential-menu.entity';
import { PotentialProduct } from 'apps/ai/src/entities/potential-product.entity';
import { PotentialModifier } from 'apps/ai/src/entities/potential.modifier.entity';
import { TangoTransfer, TangoTransferFlowStepData } from 'apps/banking/src/entities/transactions/tango-transfer.entity';
import { AccountTransfer, AccountTransferFlowStepData } from 'apps/banking/src/entities/transactions/account-transfer.entity';
import { BeneficiaryPayment, BeneficiaryPaymentFlowStepData } from 'apps/banking/src/entities/transactions/beneficiary-payment.entity';
import { CardTransaction, CardTransactionStatusEvent } from 'apps/banking/src/entities/transactions/card-transaction.entity';
import { DebitDeposit, DebitDepositFlowStepData } from 'apps/banking/src/entities/transactions/debit-deposit.entity';
import { Deposit } from 'apps/banking/src/entities/transactions/deposit.entity';
import { FundFlow, FundFlowStep } from 'apps/banking/src/entities/transactions/flow.entity';
import { ForexConversion, ForexConversionFlowStepData } from 'apps/banking/src/entities/transactions/forex-conversion.entity';
import { AccountTransaction } from 'apps/banking/src/entities/transactions/account-transaction.entity';
import { CustomerSatisfaction } from 'apps/ordering/src/entities/reporting/customer-satisfaction.entity';
import { CardTransactionClump } from 'apps/banking/src/entities/transactions/card-transaction-clump.entity';
import { OatfiUnderwriting } from 'apps/banking/src/entities/oatfi-underwriting.entity';
import { BusinessCompetitor } from 'apps/business/src/entities/business/competitors.entity';
import { HistoricPricePoint } from 'apps/ai/src/entities/historic-price-point.entity';
import { OatfiRepayment, OatfiRepaymentFactoredIn, OatfiRepaymentFlowStepData } from 'apps/banking/src/entities/transactions/oatfi-repayment.entity';
import { TangoOatfiInterestPayment, TangoOatfiPaymentMethod } from 'apps/banking/src/entities/tango-oatfi-interest.entity';
import { OnboardingState } from 'apps/business/src/entities/onboarding/onboarding-state.entity';
import { LeadContact, TangoLead } from 'apps/business/src/entities/onboarding/tango-lead.entity';
import { PotentialMenuSnapshot } from 'apps/ai/src/entities/potential-menu-snapshot.entity';
import { Ingestion } from 'apps/reporting/src/entities/Ingestion.entity';
import { ReportRecipient } from 'apps/reporting/src/entities/ReportRecipient.entity';
import { AjaxEmail, AjaxEmailAttachment } from 'apps/ai/src/entities/ajax-email.entity';
import { AjaxAction, AjaxActionResult } from 'apps/ai/src/entities/ajax-actions.entity';
import { AverageCachedBusinessStats, AverageCachedBusinessStatSample } from 'apps/reporting/src/entities/AverageCachedBusinessStats.entity';
import { PreAuthorisedSession } from 'apps/auth/src/entities/preauthed-session.entity';
import { AugmentedSchedulingRecipient } from 'apps/scheduling/src/entities/settings/augmented-scheduling-recipient.entity';
import { DataLink } from 'apps/reporting/src/entities/DataLink.entity';
import { DataHarvest, HarvestedBinaryData, HarvestedFile } from 'apps/reporting/src/entities/DataHarvest.entity';
import { InventoryPricedItem, PricedItemBusinessInfo } from 'apps/inventory/src/entities/lite/priced-item.entity';
import { PricedIngredient } from 'apps/inventory/src/entities/lite/priced-ingredient.entity';
import { PricedRecipe } from 'apps/inventory/src/entities/lite/priced-recipe.entity';
import { PricedItemEvent } from 'apps/inventory/src/entities/lite/priced-item-event.entity';
import { CachedForecast } from 'apps/reporting/src/entities/CachedForecast.entity';
import { CachedAugmentedSchedule } from 'apps/scheduling/src/entities/scheduling/cached-augmented-schedule.entity';
import { VendorItemPackaging } from 'apps/reporting/src/entities/VendorItemPackaging.entity';
import { KPISettings } from 'apps/reporting/src/entities/KPISettings.entity';
import { AvailabilityLite } from 'apps/scheduling/src/entities/scheduling/availability-lite.entity';
import { RemoteHarvester } from 'apps/reporting/src/entities/RemoteHarvester.entity';
import { RemoteHarvesterCredential } from 'apps/reporting/src/entities/RemoteHarvesterCredentials.entity';
import { RemoteHarvesterJob } from 'apps/reporting/src/entities/RemoteHarvesterJob.entity';
import { RemoteHarvesterSchedule } from 'apps/reporting/src/entities/RemoteHarvestSchedule.entity';
import { Insight } from 'apps/reporting/src/entities/Insight.entity';
import { InsightInbox } from 'apps/reporting/src/entities/Insight.entity';
import { Reviewable, IngestedReview, IngestedReviewResponse, ReviewableSnapshot } from 'apps/reporting/src/entities/GoogleReview.entity';
import { CopilotHistory } from 'apps/reporting/src/entities/Copilot.entity';

export const TangoCloudEntities = [
  BasicDiscount,
  Business,
  BusinessEnterpriseLink,
  BusinessHour,
  BusinessLocation,
  Contract,
  ContractSection,
  LineItem,
  Tax,
  Enterprise,
  ServiceArea,
  ServiceAreaGeometries,
  Table,
  InternetReader,
  Customer,
  BusinessCustomer,
  DeliveryCustomer,
  CustomerPaymentMethod,
  TableDuty,
  Reservation,
  ServicePeriod,
  ServicePeriodTime,
  ServiceAreaPeriod,
  ServiceAreaPeriodTurnTime,
  WalkInParty,
  WaitlistParty,
  ReservationBlackoutDate,
  ReservationBlackoutPeriod,
  Printer,
  CashDrawer,
  TableSession,
  Tab,
  Payment,
  PaysFor,
  VoidedProduct,
  ProductPaid,
  Order,
  ProductInOrder,
  StripeSource,
  GiftCardSource,
  CashSource,
  LossSource,
  ExternalSource,
  PreAuth,
  ServiceFee,
  ServiceFeeRule,
  CashEvent,
  User,
  UserAddress,
  QRCode,
  Menu,
  MenuCategoryLink,
  MenuHour,
  MenuCategory,
  Product,
  BusinessProduct,
  MenuCategoryProduct,
  Modifier,
  BusinessModifier,
  ProductModifier,
  ModifierOption,
  BusinessModifierOption,
  ModifierModifierOption,
  Discount,
  DiscountQuantityItem,
  DiscountQuantityItemBasket,
  DiscountAvailability,
  DiscountBogo,
  DiscountCombos,
  DiscountPromocode,
  Allergen,
  Location,
  Unit,
  UnitConversion,
  PrimaryGroup,
  SecondaryGroup,
  Item,
  BusinessItem,
  Recipe,
  Ingredient,
  ScheduledCount,
  Count,
  CountItem,
  VendorAccount,
  ManualVendor,
  IntegratedVendor,
  Offering,
  IntegratedOffering,
  Invoice,
  InvoiceExpense,
  ItemPurchase,
  InventoryEvent,
  BusinessSetting,
  DeliverectSetting,
  POSSetting,
  GeneralSetting,
  GeneralServiceSetting,
  TipOut,
  SchedulingSetting,
  LoyaltyAndGiftCardsSetting,
  RatingsAndReviewsSetting,
  OnlineOrderingSetting,
  InventorySetting,
  ShiftTypesSetting,
  ForecastingSetting,
  TangoGiftCard,
  TangoGiftCardSpendEvent,
  TangoGiftCardLoadEvent,
  GiftCard,
  GiftCardTransaction,
  KitchenDisplaySystem,
  KDSIntervals,
  StripeRefund,
  CashRefund,
  KDSOrder,
  KDSOrderItem,
  KDSCompleted,
  PrintJob,
  OperatingHoursSetting,
  BusinessDiscountLink,
  InventorySetting,
  Review,
  ReviewMedia,
  ReviewResponse,
  ReviewResponseTemplate,
  UnityReport,
  ScheduledUnityReport,
  OldBusinessDiscount,
  Competitor,
  Permission,
  LoginSession,
  StaffPaymentInfo,
  StaffAvailability,
  StaffDayAvailability,
  StaffMember,
  Invitation,
  InvitationPosition,
  PayRate,
  Shift,
  EmergencyContact,
  Department,
  Role,
  TimeOffRequest,
  CoverRequest,
  ShiftTemplate,
  TradeRequest,
  DropRequest,
  DailyLogReply,
  DailyLogReaction,
  LaborTarget,
  DepartmentTarget,
  DailyLog,
  RoleTimeOffSetting,
  BlackoutDate,
  WorkEvent,
  HelpGuide,
  Payout,
  TradeRequestOffer,
  CoverRequestOffer,
  PriceLevel,
  PriceLevelPrice,
  PublishedMenu,
  PriceVariant,
  CategoryPriceLevel,
  TipPool,
  TipPoolContributor,
  TipPoolReceiver,
  TipPoolContributorServiceAreaFilter,
  TipPoolContributorProductTypeFilter,
  TipPoolCalculation,
  TipPoolContribution,
  TipPoolStaffContribution,
  TipPoolDistribution,
  TipPoolStaffDistribution,
  TangoPrintController,
  TangoPrinter,
  TangoPrintJob,
  PayrollItem,
  OneOffPayment,
  BankAccount,
  Payroll,
  PrintersBlacklist,
  ProcessingRate,
  TangoDevice,
  TangoDeviceEvent,
  TangoKDSCompleted,
  TangoKDSItem,
  TangoKDSOrder,
  PrintingSetting,
  CachedBusinessStats,
  AverageCachedBusinessStats,
  AverageCachedBusinessStatSample,
  ReportingProduct,
  ReportingProductEvent,
  ReportingNotification,
  CachedEnterpriseStats,
  TangoBankAccount,
  MergeIntegration,
  GlobalAccount,
  AccountBalance,
  TangoCardholder,
  Beneficiary,
  InteracClearingAccount,
  EFTClearingAccount,
  ACHClearingAccount,
  FedwireClearingAccount,
  SWIFTClearingAccount,
  CheckClearingAccount,
  BankAccountTransaction,
  TangoCard,
  ControlGroupInterval,
  ControlGroup,
  LinkedAccount,
  ProofOfPayment,
  POPLineItem,
  AccountBalanceHistory,
  KDS,
  KDSStation,
  KDSChit,
  KDSChitEvent,
  KDSChitItem,
  KDSChitItemEvent,
  KDSStationItem,
  KDSStationItemEvent,
  Budget,
  Actuals,
  ProjectionOverride,
  CachedTabProfitability,
  CachedStaffProfitability,
  RecurringCost,
  ActualRecurringCost,
  IncidentalCost,
  DepartmentBudget,
  SalesProjection,
  ForexQuote,
  ForexPayment,
  Demand,
  Penalty,
  Resolution,
  PenaltyTemplate,
  DebitCharge,
  MenuLabelsMappings,
  InsightDialogue,
  InsightDialogueAction,
  InsightDialogueMessage,
  PlatformFee,
  PotentialBusiness,
  PotentialBusinessHour,
  PotentialBusinessLocation,
  PotentialMenu,
  PotentialMenuScrapeAttempt,
  PotentialMenuSnapshot,
  PotentialProduct,
  HistoricPricePoint,
  PotentialMenuBusiness,
  PotentialModifier,
  // Transactions
  AccountTransfer,
  BeneficiaryPayment,
  CardTransaction,
  CardTransactionStatusEvent,
  DebitDeposit,
  Deposit,
  TangoTransfer,
  ForexConversion,
  AccountTransaction,
  // Flows
  FundFlow,
  FundFlowStep,
  ForexConversionFlowStepData,
  TangoTransferFlowStepData,
  AccountTransferFlowStepData,
  BeneficiaryPaymentFlowStepData,
  DebitDepositFlowStepData,
  CustomerSatisfaction,
  CardTransactionClump,
  OatfiUnderwriting,
  OatfiRepayment,
  OatfiRepaymentFactoredIn,
  OatfiRepaymentFlowStepData,
  TangoOatfiPaymentMethod,
  TangoOatfiInterestPayment,
  // Competitors API
  BusinessCompetitor,
  OnboardingState,
  TangoLead,
  LeadContact,
  // New Competitors API
  Reviewable,
  IngestedReview,
  IngestedReviewResponse,
  ReviewableSnapshot,
  // Ingestion
  Ingestion,

  // Copilot
  CopilotHistory,

  ReportRecipient,
  AjaxEmail,
  AjaxEmailAttachment,
  AjaxAction,
  AjaxActionResult,

  PreAuthorisedSession,
  AugmentedSchedulingRecipient,
  DataLink,
  DataHarvest,
  HarvestedFile,
  HarvestedBinaryData,

  // Inventory Lite
  InventoryPricedItem,
  PricedItemBusinessInfo,
  PricedIngredient,
  PricedRecipe,
  VendorItemPackaging,
  PricedItemEvent,

  // Forecast
  CachedForecast,

  // Scheduling
  CachedAugmentedSchedule,
  AvailabilityLite,

  // KPISettings
  KPISettings,

  // Remote Harvester
  RemoteHarvester,
  RemoteHarvesterCredential,
  RemoteHarvesterJob,
  RemoteHarvesterSchedule,

  // Insights
  Insight,
  InsightInbox,
];
