import { ClientsModule, ClientsModuleOptions, Transport } from '@nestjs/microservices';
import { protobufPackage as businessProtobuffPackage } from 'apps/business/src/proto/proto/business/business.pb';
import { protobufPackage as bookingProtobufPackage } from 'apps/booking/src/proto/booking/bookings.pb';
import { protobufPackage as googleBookingsProtobufPackage } from 'apps/business/src/proto/proto/business/google-bookings.pb';
import { protobufPackage as authProtobuffPackage } from 'apps/auth/src/proto/auth.pb';
import { protobufPackage as schedulingProtobuffPackage } from 'apps/scheduling/src/proto/scheduling/scheduling.pb';
import { protobufPackage as menuProtobufPackage } from 'apps/menu/src/proto/proto/menu-management.pb';
import { protobufPackage as orderingProtobufPackage } from 'apps/ordering/src/proto/proto/ordering/ordering.pb';
import { protobufPackage as inventoryProtobufPackage } from 'apps/inventory/src/proto/inventory/shared.pb';
import { protobufPackage as printingRealtimeProtobufPackage } from 'apps/realtime/src/proto/proto/realtime/printing.pb';
import { protobufPackage as kdsProtobufPackage } from 'apps/kds/src/proto/kds.pb';
import { protobufPackage as kdsProtobufPackageV3 } from 'apps/kds/src/proto/kds/kds.pb';
import { protobufPackage as printingProtobufPackage } from 'apps/printing/src/proto/printing/printing.pb';
import { protobufPackage as giftcardsProtobufPackage } from 'apps/gift-cards/src/proto/proto/gift-cards/gift-cards.pb';
import { protobufPackage as qrcodesProtobufPackage } from 'apps/qrcodes/src/proto/qr-codes.pb';
import { protobufPackage as reportingProtobufPackage } from 'apps/reporting/src/proto/reporting/reporting.pb';
import { protobufPackage as reputationProtobufPackage } from 'apps/reputation/src/proto/reputation-management.pb';
import { protobufPackage as peopleProtobufPackage } from 'apps/staffing/src/proto/people/shared.pb';
import { protobufPackage as AIProtobufPackage } from 'apps/ai/src/proto/ai/ai.pb';
import { protobufPackage as PrintProtobufPackage } from 'apps/printing/src/proto/proto/print/print.pb';
import { protobufPackage as BankingProtobufPackage } from 'apps/banking/src/proto/banking/shared.pb';
import { protobufPackage as sandboxProtobufPackage } from 'apps/sandbox/src/proto/sandbox/sandbox.pb';

export interface IClient {
  packageName: string;
  protoPath: string[];
  url: string;
  maxSendMessageLength?: number;
  maxReceiveMessageLength?: number;
  keepCase?: boolean;
  longsAsString?: boolean;
}
const MB = 1024 * 1024;

const MICROSERVICE_CLIENT_HOST = process.env.MICROSERVICE_CLIENT_HOST || '**********'; // or ********** if you want to use docker locally

export type AvailableClientNames =
  | 'AUTH_PACKAGE'
  | 'BUSINESS_PACKAGE'
  | 'GOOGLE_BOOKINGS_PACKAGE'
  | 'SCHEDULING_PACKAGE'
  | 'MENU_PACKAGE'
  | 'ORDERING_PACKAGE'
  | 'INVENTORY_PACKAGE'
  | 'REALTIME_PACKAGE'
  | 'KDS_PACKAGE'
  | 'KDS_V3_PACKAGE'
  | 'PRINTING_PACKAGE'
  | 'GIFT_CARDS_PACKAGE'
  | 'QR_CODES_PACKAGE'
  | 'REPORTING_PACKAGE'
  | 'REPUTATION_PACKAGE'
  | 'STAFFING_PACKAGE'
  | 'AI_PACKAGE'
  | 'PRINT_PACKAGE'
  | 'BOOKING_PACKAGE'
  | 'BANKING_PACKAGE'
  | 'SANDBOX_PACKAGE';

export const AvailableClients: { [title in AvailableClientNames]: IClient } = {
  AUTH_PACKAGE: {
    packageName: authProtobuffPackage,
    protoPath: ['tango-proto/proto/auth.proto'],
    url: process.env.NODE_ENV === 'production' ? process.env.AUTH_SERVICE_HOST + ':' + process.env.AUTH_SERVICE_PORT : `${MICROSERVICE_CLIENT_HOST}:50051`,
  },

  BUSINESS_PACKAGE: {
    packageName: businessProtobuffPackage,
    protoPath: [
      'tango-proto/proto/business/business.proto',
      'tango-proto/proto/business/payouts.proto',
      'tango-proto/proto/business/taxes.proto',
      'tango-proto/proto/business/settings.proto',
      'tango-proto/proto/business/service-areas.proto',
      'tango-proto/proto/business/service-fees.proto',
      'tango-proto/proto/business/internet-readers.proto',
      'tango-proto/proto/business/tables.proto',
      'tango-proto/proto/business/cash-drawers.proto',
      'tango-proto/proto/business/printers.proto',
      'tango-proto/proto/business/help.proto',
      'tango-proto/proto/business/contracts.proto',
      'tango-proto/proto/business/stripe.proto',
      'tango-proto/proto/business/customers.proto',
      'tango-proto/proto/business/table-duty.proto',
      'tango-proto/proto/business/reservations.proto',
      'tango-proto/proto/business/walkins.proto',
      'tango-proto/proto/business/waitlist.proto',
      'tango-proto/proto/business/webhooks.proto',
      'tango-proto/proto/business/shared.proto',
      'tango-proto/proto/business/store-health.proto',
      'tango-proto/proto/business/processing-rates.proto',
      'tango-proto/proto/ai/ai.proto',
      'tango-proto/proto/business/competitors.proto',
      'tango-proto/proto/business/join.proto',
    ],
    url: process.env.NODE_ENV === 'production' ? process.env.BUSINESS_SERVICE_HOST + ':' + process.env.BUSINESS_SERVICE_PORT : `${MICROSERVICE_CLIENT_HOST}:50052`,
  },

  SCHEDULING_PACKAGE: {
    packageName: schedulingProtobuffPackage,
    protoPath: [
      'tango-proto/proto/scheduling/scheduling.proto',
      'tango-proto/proto/scheduling/dailylogs.proto',
      'tango-proto/proto/scheduling/ai.proto',
      'tango-proto/proto/scheduling/shared.proto',
    ],
    url: process.env.NODE_ENV === 'production' ? process.env.SCHEDULING_SERVICE_HOST + ':' + process.env.SCHEDULING_SERVICE_PORT : `${MICROSERVICE_CLIENT_HOST}:50054`,
  },

  STAFFING_PACKAGE: {
    packageName: peopleProtobufPackage,
    protoPath: [
      'tango-proto/proto/people/shared.proto',
      'tango-proto/proto/people/team.proto',
      'tango-proto/proto/people/payroll.proto',
      'tango-proto/proto/people/tipouts.proto',
    ],
    url: process.env.NODE_ENV === 'production' ? process.env.STAFFING_SERVICE_HOST + ':' + process.env.STAFFING_SERVICE_PORT : `${MICROSERVICE_CLIENT_HOST}:50055`,
  },

  MENU_PACKAGE: {
    packageName: menuProtobufPackage,
    protoPath: ['tango-proto/proto/menu-management.proto'],
    url: process.env.NODE_ENV === 'production' ? process.env.MENU_SERVICE_HOST + ':' + process.env.MENU_SERVICE_PORT : `${MICROSERVICE_CLIENT_HOST}:50053`,
    maxReceiveMessageLength: 100 * MB,
    maxSendMessageLength: 100 * MB,
  },

  ORDERING_PACKAGE: {
    packageName: orderingProtobufPackage,
    protoPath: [
      'tango-proto/proto/ordering/messages.proto',
      'tango-proto/proto/ordering/ordering.proto',
      'tango-proto/proto/ordering/cash-events.proto',
      'tango-proto/proto/ordering/payments.proto',
      'tango-proto/proto/ordering/preview-payments.proto',
      'tango-proto/proto/ordering/reporting.proto',
    ],
    url: process.env.NODE_ENV === 'production' ? process.env.ORDERING_SERVICE_HOST + ':' + process.env.ORDERING_SERVICE_PORT : `${MICROSERVICE_CLIENT_HOST}:50059`,
    maxReceiveMessageLength: 100 * MB,
    maxSendMessageLength: 100 * MB,
  },

  INVENTORY_PACKAGE: {
    packageName: inventoryProtobufPackage,
    protoPath: [
      'tango-proto/proto/inventory/shared.proto',
      'tango-proto/proto/inventory/info.proto',
      'tango-proto/proto/inventory/items.proto',
      'tango-proto/proto/inventory/tracking.proto',
      'tango-proto/proto/inventory/vendors.proto',
    ],
    url: process.env.NODE_ENV === 'production' ? process.env.INVENTORY_SERVICE_HOST + ':' + process.env.INVENTORY_SERVICE_PORT : `${MICROSERVICE_CLIENT_HOST}:50061`,
  },

  REALTIME_PACKAGE: {
    packageName: printingRealtimeProtobufPackage,
    protoPath: [
      'tango-proto/proto/realtime/kds.proto',
      'tango-proto/proto/realtime/printing.proto',
      'tango-proto/proto/realtime/print.proto',
      'tango-proto/proto/realtime/dinein.proto',
      'tango-proto/proto/realtime/ordering.proto',
      'tango-proto/proto/realtime/reservation.proto',
      'tango-proto/proto/realtime/menu.proto',
      'tango-proto/proto/realtime/tracking.proto',
      'tango-proto/proto/realtime/scheduling.proto',
      'tango-proto/proto/realtime/staffing.proto',
      'tango-proto/proto/realtime/store-health.proto',
      'tango-proto/proto/realtime/banking.proto',
      'tango-proto/proto/realtime/join.proto',
      'tango-proto/proto/realtime/nexus.proto',
    ],
    url:
      process.env.NODE_ENV === 'production' ? process.env.REALTIME_GRPC_SERVICE_HOST + ':' + process.env.REALTIME_GRPC_SERVICE_PORT : `${MICROSERVICE_CLIENT_HOST}:50071`,
    maxReceiveMessageLength: 100 * MB,
    maxSendMessageLength: 100 * MB,
  },

  KDS_PACKAGE: {
    packageName: kdsProtobufPackage,
    protoPath: ['tango-proto/proto/kds.proto'],
    url: process.env.NODE_ENV === 'production' ? process.env.KDS_SERVICE_HOST + ':' + process.env.KDS_SERVICE_PORT : `${MICROSERVICE_CLIENT_HOST}:50062`,
  },

  KDS_V3_PACKAGE: {
    packageName: kdsProtobufPackageV3,
    protoPath: ['tango-proto/proto/kds/kds.proto'],
    url: process.env.NODE_ENV === 'production' ? process.env.KDS_SERVICE_HOST + ':' + process.env.KDS_SERVICE_PORT : `${MICROSERVICE_CLIENT_HOST}:50062`,
  },

  PRINTING_PACKAGE: {
    packageName: printingProtobufPackage,
    protoPath: ['tango-proto/proto/printing/printing.proto'],
    url: process.env.NODE_ENV === 'production' ? process.env.PRINTING_SERVICE_HOST + ':' + process.env.PRINTING_SERVICE_PORT : `${MICROSERVICE_CLIENT_HOST}:50072`,
  },

  GIFT_CARDS_PACKAGE: {
    packageName: giftcardsProtobufPackage,
    protoPath: ['tango-proto/proto/gift-cards/gift-cards.proto', 'tango-proto/proto/gift-cards/ackroo-gift-cards.proto'],
    url: process.env.NODE_ENV === 'production' ? process.env.GIFT_CARDS_SERVICE_HOST + ':' + process.env.GIFT_CARDS_SERVICE_PORT : `${MICROSERVICE_CLIENT_HOST}:50058`,
  },

  QR_CODES_PACKAGE: {
    packageName: qrcodesProtobufPackage,
    protoPath: ['tango-proto/proto/qr-codes.proto'],
    url: process.env.NODE_ENV === 'production' ? process.env.QRCODES_SERVICE_HOST + ':' + process.env.QRCODES_SERVICE_PORT : `${MICROSERVICE_CLIENT_HOST}:50060`,
  },

  REPORTING_PACKAGE: {
    packageName: reportingProtobufPackage,
    protoPath: [
      'tango-proto/proto/reporting/shared.proto',
      'tango-proto/proto/reporting/analysis.proto',
      'tango-proto/proto/reporting/scheduled.proto',
      'tango-proto/proto/reporting/reporting.proto',
      'tango-proto/proto/reporting/data.proto',
      'tango-proto/proto/reporting/detailed-data.proto',
      'tango-proto/proto/reporting/generate.proto',
      'tango-proto/proto/reporting/unity.proto',
      'tango-proto/proto/reporting/tipout.proto',
      'tango-proto/proto/reporting/budget.proto',
      'tango-proto/proto/reporting/insights.proto',
      'tango-proto/proto/reporting/ingest.proto',
      'tango-proto/proto/reporting/harvest.proto',
      'tango-proto/proto/reporting/mailing.proto',
      'tango-proto/proto/reporting/forecasting.proto',
      'tango-proto/proto/reporting/kpis.proto',
      'tango-proto/proto/reporting/digest.proto',
      'tango-proto/proto/reporting/reviews.proto',
      'tango-proto/proto/reporting/authorization.proto',
      'tango-proto/proto/reporting/copilot.proto',
    ],
    url: process.env.NODE_ENV === 'production' ? process.env.REPORTING_SERVICE_HOST + ':' + process.env.REPORTING_SERVICE_PORT : `${MICROSERVICE_CLIENT_HOST}:50063`,
    maxReceiveMessageLength: 100 * MB,
    maxSendMessageLength: 100 * MB,
  },

  REPUTATION_PACKAGE: {
    packageName: reputationProtobufPackage,
    protoPath: ['tango-proto/proto/reputation-management.proto'],
    url: process.env.NODE_ENV === 'production' ? process.env.REPUTATION_SERVICE_HOST + ':' + process.env.REPUTATION_SERVICE_PORT : `${MICROSERVICE_CLIENT_HOST}:50064`,
  },

  AI_PACKAGE: {
    packageName: AIProtobufPackage,
    protoPath: ['tango-proto/proto/ai/shared.proto', 'tango-proto/proto/ai/ai.proto', 'tango-proto/proto/ai/ajax.proto'],
    url: process.env.NODE_ENV === 'production' ? process.env.AI_SERVICE_HOST + ':' + process.env.AI_SERVICE_PORT : `${MICROSERVICE_CLIENT_HOST}:50065`,
  },

  PRINT_PACKAGE: {
    packageName: PrintProtobufPackage,
    protoPath: ['tango-proto/proto/print/print.proto', 'tango-proto/proto/print/shared.proto'],
    url: process.env.NODE_ENV === 'production' ? process.env.PRINTING_SERVICE_HOST + ':' + process.env.PRINTING_SERVICE_PORT : `${MICROSERVICE_CLIENT_HOST}:50072`,
    maxReceiveMessageLength: 100 * MB,
    maxSendMessageLength: 100 * MB,
  },

  BANKING_PACKAGE: {
    packageName: BankingProtobufPackage,
    protoPath: [
      'tango-proto/proto/banking/account.proto',
      'tango-proto/proto/banking/authorization.proto',
      'tango-proto/proto/banking/webhooks.proto',
      'tango-proto/proto/banking/deposits.proto',
      'tango-proto/proto/banking/issuing.proto',
      'tango-proto/proto/banking/payments.proto',
      'tango-proto/proto/banking/transactions.proto',
      'tango-proto/proto/banking/spending-controls.proto',
      'tango-proto/proto/banking/linked.proto',
      'tango-proto/proto/banking/merge.proto',
      'tango-proto/proto/banking/pop.proto',
      'tango-proto/proto/banking/reporting.proto',
      'tango-proto/proto/banking/forex.proto',
      'tango-proto/proto/banking/demand.proto',
      'tango-proto/proto/banking/repayment.proto',
      'tango-proto/proto/banking/oatfi.proto',
      'tango-proto/proto/banking/shared.proto',
    ],
    url: process.env.NODE_ENV === 'production' ? process.env.BANKING_SERVICE_HOST + ':' + process.env.BANKING_SERVICE_PORT : `${MICROSERVICE_CLIENT_HOST}:50073`,
    keepCase: true,
  },
  BOOKING_PACKAGE: {
    packageName: bookingProtobufPackage,
    protoPath: [
      'tango-proto/proto/booking/bookings.proto',
      'tango-proto/proto/booking/google-bookings.proto',
      'tango-proto/proto/booking/messages.proto',
      'tango-proto/proto/booking/reservations.proto',
      'tango-proto/proto/booking/waitlist.proto',
      'tango-proto/proto/booking/walkins.proto',
      'tango-proto/proto/booking/webhooks.proto',
    ],
    url: process.env.NODE_ENV === 'production' ? process.env.BOOKING_SERVICE_HOST + ':' + process.env.BOOKING_SERVICE_PORT : `${MICROSERVICE_CLIENT_HOST}:50078`,
    maxSendMessageLength: 100 * MB,
    maxReceiveMessageLength: 100 * MB,
  },
  GOOGLE_BOOKINGS_PACKAGE: {
    packageName: googleBookingsProtobufPackage,
    protoPath: ['tango-proto/proto/booking/google-bookings.proto'],
    // same as booking service, these run on the same microservice
    url: process.env.NODE_ENV === 'production' ? process.env.BOOKING_SERVICE_HOST + ':' + process.env.BOOKING_SERVICE_PORT : `${MICROSERVICE_CLIENT_HOST}:50078`,
    keepCase: true,
    longsAsString: true,
  },
  SANDBOX_PACKAGE: {
    packageName: sandboxProtobufPackage,
    protoPath: ['tango-proto/proto/sandbox/sandbox.proto'],
    url: process.env.NODE_ENV === 'production' ? process.env.SANDBOX_SERVICE_HOST + ':' + process.env.SANDBOX_SERVICE_PORT : `${MICROSERVICE_CLIENT_HOST}:50075`,
  },
};

export const registerClientModule = (clients: IClient[]) => {
  const clientsToRegister: ClientsModuleOptions = clients.map((client) => ({
    name: client.packageName,
    transport: Transport.GRPC,
    options: {
      url: client.url,
      package: client.packageName,
      protoPath: client.protoPath,
      maxReceiveMessageLength: client.maxReceiveMessageLength || 4 * MB,
      maxSendMessageLength: client.maxSendMessageLength || 4 * MB,
      loader: {
        defaults: true,
        keepCase: client.keepCase || false,
        longs: client.longsAsString ? String : Number,
      },
    },
  }));
  return ClientsModule.register(clientsToRegister);
};
