{"name": "tango-flock", "version": "1.0.0", "description": "Tango Flock is a Database Migration tool", "main": "build/index.js", "source": "index.ts", "license": "MIT", "dependencies": {"@anthropic-ai/sdk": "^0.54.0", "@google-cloud/logging": "^10.5.0", "@google-cloud/storage": "^7.12.0", "@swc/helpers": "^0.4.14", "axios": "^1.3.4", "bcrypt": "^5.1.0", "chalk": "4.1.2", "chance": "^1.1.11", "clever-ts-utilities": "^2.0.0", "cli-select": "^1.1.2", "commander": "^10.0.0", "csv-parse": "^5.3.6", "dayjs": "^1.11.7", "dotenv": "^16.0.3", "fast-csv": "^5.0.1", "firebase": "^9.17.1", "firebase-admin": "^11.5.0", "fs": "^0.0.1-security", "hash.js": "^1.1.7", "json-to-ts": "^1.7.0", "jsonwebtoken": "^9.0.0", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "natural": "^8.0.1", "node-rsa": "^1.1.1", "openai": "4.98.0", "parcel-transformer-csv": "^0.0.3", "pg": "^8.9.0", "pg-copy-streams": "^6.0.5", "prompt": "^1.3.0", "prompt-sync": "^4.2.0", "puppeteer": "^23.9.0", "sharp": "^0.33.4", "sqlite": "^4.1.2", "sqlite3": "^5.1.4", "string-similarity": "^4.0.4", "stripe": "^14.3.0", "typeorm": "^0.3.20", "x-crawl": "^10.0.2", "zlib": "^1.0.5", "zod": "^3.23.8"}, "scripts": {"flock:start": "node build/index.js", "flock:build": "parcel build", "flock:watch": "parcel watch", "flock:clean": "rm -rf build .parcel-cache", "flock:veryClean": "rm -rf build .parcel-cache node_modules", "flock:build:clean": "yarn flock:clean && yarn flock:build", "flock:build:veryClean": "yarn flock:veryClean && yarn && yarn flock:build", "flock:prepare": "husky install"}, "devDependencies": {"@types/bcrypt": "^5.0.0", "@types/chance": "^1.1.3", "@types/firebase": "^3.2.1", "@types/lodash": "^4.14.191", "@types/node-rsa": "^1.1.4", "@types/pg-copy-streams": "^1.2.2", "@types/prompt": "^1.1.5", "@types/prompt-sync": "^4.2.0", "@typescript-eslint/eslint-plugin": "^5.13.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.2.0", "eslint-config-airbnb": "19.0.4", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-react": "^7.28.0", "eslint-plugin-react-hooks": "^4.3.0", "husky": "^8.0.3", "parcel": "^2.8.3", "prettier": "2.8.7", "typescript": "^4.9.5"}, "alias": {"apps": "../apps", "@tangopay/shared": "../libs/shared/src"}}