import { Command } from 'commander';
import { getSource } from '../../readsource';
import { SUB_PRODUCTS_CTE_QUERY, SUB_TABS_CTE_QUERY, SUB_WORK_EVENTS_CTE_QUERY } from '../../../apps/reporting/src/services/queries/base-queries';
import Agent from '../../../apps/reporting/src/services/agents/agent';
import SchemaSelectorAgent from '../../../apps/reporting/src/services/agents/schemaSelectorAgent';
import QueryDecomposerAgent from '../../../apps/reporting/src/services/agents/queryDecomposerAgent';
import SQLGeneratorAgent from '../../../apps/reporting/src/services/agents/sqlGeneratorAgent';
import ValidatorAgent from '../../../apps/reporting/src/services/agents/validatorAgent';
import ConfidenceScorerAgent from '../../../apps/reporting/src/services/agents/confidenceScoreAgent';
import { Menu } from '../../../apps/menu/src/entities/menu.entity';
import { StaffMember } from '../../../apps/staffing/src/entities/staff/staff-member.entity';

async function executeSQLQuery(query: string) {
  const source = getSource();

  // Check if already initialized, if not, initialize it
  if (!source.isInitialized) {
    await source.initialize();
  }

  const queryRunner = source.createQueryRunner();

  if (queryRunner) {
    await queryRunner.connect();
    const response = await queryRunner.query(query);
    queryRunner.release();
    return response;
  } else {
    throw new Error('Failed to create query runner');
  }
}

async function getMenus(businessId: string) {
  const source = getSource();
  const menus = await source.getRepository<Menu>(Menu).find({
    where: {
      business: {
        id: businessId,
      },
    },
    relations: {
      menuCategories: {
        menuCategory: {
          contents: {
            product: true,
          },
        },
      },
    },
  });

  return menus;
}

async function getStaff(businessId: string) {
  const source = getSource();
  const staff = await source.getRepository<StaffMember>(StaffMember).find({
    where: {
      business: {
        id: businessId,
      },
    },
    relations: {
      user: true,
      payRates: {
        role: {
          department: true,
        },
      },
    },
  });

  return staff;
}

type Reference = Record<string, string>;

class RestaurantSQLGenerator {
  agents: { [key: string]: Agent };

  constructor() {
    this.agents = {};
  }

  async getReferences(businessId: string) {
    const fullMenu = await getMenus(businessId);
    const { menus, menuCategories, products } = fullMenu.reduce(
      (acc, menu) => {
        // Add the menu
        acc['menus'][menu.id] = menu.nameExternal;
        // Add the menu categories
        menu.menuCategories.forEach((menuCategory) => {
          acc['menuCategories'][menuCategory.menuCategoryId] = menuCategory.menuCategory?.name || '';
          // Add the products
          menuCategory.menuCategory?.contents.forEach((content) => {
            acc['products'][content.productId] = content.product.nameExternal;
          });
        });
        return acc;
      },
      { menus: {}, menuCategories: {}, products: {} } as { menus: Reference; menuCategories: Reference; products: Reference },
    );

    const staffMembers = await getStaff(businessId);
    const { staff, roles, departments } = staffMembers.reduce(
      (acc, staffMember) => {
        // Add the staff
        const name = staffMember.user?.firstName ? `${staffMember.user?.firstName} ${staffMember.user?.lastName}` : staffMember.ingestionData?.employeeName || '';
        acc['staff'][staffMember.id] = name;
        // Add the roles
        staffMember.payRates.forEach((payRate) => {
          acc['roles'][payRate.role.id] = payRate.role.name;
          // Add the departments
          acc['departments'][payRate.role.department.id] = payRate.role.department.name;
        });
        return acc;
      },
      { staff: {}, roles: {}, departments: {} } as { staff: Reference; roles: Reference; departments: Reference },
    );

    return {
      menus,
      menuCategories,
      products,
      staff,
      roles,
      departments,
    };
  }

  async generateSQL(userQuery: string, businessId: string, options: { [T: string]: any } = {}) {
    const context: { [T: string]: any } = {
      businessId,
      userQuery,
      timestamp: new Date().toISOString(),
      options,
    };

    // Get the references
    const { menus, menuCategories, products, staff, roles, departments } = await this.getReferences(businessId);

    // Setup all the agents
    this.agents.schemaSelector = new SchemaSelectorAgent(menus, menuCategories, products, [], staff, roles, departments, '');
    this.agents.queryDecomposer = new QueryDecomposerAgent();
    this.agents.sqlGenerator = new SQLGeneratorAgent();
    this.agents.validator = new ValidatorAgent();
    this.agents.confidenceScorer = new ConfidenceScorerAgent();

    try {
      // Schema Selection and Entity Matching
      console.log('🔍 Analyzing query and selecting schema...');
      const schemaInfo = await this.agents.schemaSelector.execute(userQuery, businessId);

      // Check for missing information
      if (schemaInfo.has_missing_information) {
        return {
          status: 200,
          response: {
            message: `Which ${schemaInfo.missing_information.type} are you referring to? ${JSON.stringify(schemaInfo.missing_information.options)}`,
            UI: '',
            data: undefined,
          },
        };
      }

      console.log({ schemaInfo });
      context.schemaInfo = schemaInfo;

      // Query Decomposition
      console.log('🔨 Decomposing query into steps...');
      const decomposition = await this.agents.queryDecomposer.execute(userQuery, {
        schemaInfo,
        businessId,
      });
      console.log({ decomposition: JSON.stringify(decomposition, null, 2) });
      context.decomposition = decomposition;

      // SQL Generation
      console.log('⚡ Generating SQL query...');
      const sqlQuery = await this.agents.sqlGenerator.execute(userQuery, {
        steps: decomposition.steps,
        schemaInfo,
        entityMatches: schemaInfo.products_matches || {},
        timeContext: schemaInfo.time_context,
        businessId,
      });
      const cteHeaders = ((context.schemaInfo?.ctes || []) as string[])
        .map((cte) => {
          switch (cte) {
            case 'products':
              return SUB_PRODUCTS_CTE_QUERY;
            case 'tabs':
              return SUB_TABS_CTE_QUERY;
            case 'work_events':
              return SUB_WORK_EVENTS_CTE_QUERY;
            default:
              return null;
          }
        })
        .filter(Boolean);

      let finalQuery;
      if (cteHeaders.length > 0) {
        // Ensure sqlQuery doesn't start with WITH
        const cleanSqlQuery = sqlQuery.replace(/^\s*WITH\s+/i, '').trim();
        finalQuery = `WITH ${cteHeaders.join(',\n')}\n\n${cleanSqlQuery}`;
      } else {
        finalQuery = sqlQuery;
      }

      context.sqlQuery = finalQuery;
      // const cteHeadersString = cteHeaders.length > 0 ? `WITH ${cteHeaders.join(', \n')}\n` : '';
      // console.log({ sqlQuery: JSON.stringify(sqlQuery, null, 2) });
      // context.sqlQuery = cteHeadersString + sqlQuery;

      // Validation
      console.log('✅ Validating SQL query...');
      const validation = await this.agents.validator.execute(context.sqlQuery, {
        originalQuery: userQuery,
        schemaInfo,
      });
      console.log(JSON.stringify(validation));
      context.validation = validation;
      if (context.suggested_query) {
        context.previousSqlQuery = context.sqlQuery;
        context.sqlQuery = context.suggested_query;
      }

      // Confidence Scoring
      console.log('📊 Scoring confidence...');
      const confidence = await this.agents.confidenceScorer.execute(userQuery, {
        sqlQuery: context.sqlQuery,
        schemaInfo,
        validation,
        decomposition,
      });
      console.log({ confidence });
      context.confidence = confidence;

      const shouldExecute = validation.valid && (confidence.overall_confidence > 70 || validation.suggested_query);

      // Return comprehensive result
      return {
        success: shouldExecute,
        query: context.sqlQuery,
        confidence: confidence.overall_confidence,
        recommendation: confidence.recommendation,
        usedSuggestedQuery: !!validation.suggested_query,
        metadata: {
          queryType: schemaInfo.query_type,
          complexity: decomposition.complexity,
          entities: schemaInfo.entities_to_search,
          validation,
        },
        alternatives: [],
        debugInfo: options.debug ? context : undefined,
      };
    } catch (error) {
      console.error('❌ SQL generation failed:', error);
      return {
        success: false,
        error: (error as Error).message,
        recommendation: 'fallback',
      };
    }
  }

  async executeQuery(userQuery: string, businessId: string, options = {}) {
    const result = await this.generateSQL(userQuery, businessId, options);

    if (result.success && result.recommendation === 'execute') {
      try {
        console.log('🚀 Executing SQL query...');
        const data = await executeSQLQuery(result.query);
        return {
          ...result,
          data,
          rowCount: data.length,
        };
      } catch (error) {
        console.error('❌ Query execution failed:', error);
        return {
          ...result,
          success: false,
          executionError: (error as Error).message,
        };
      }
    }

    return result;
  }
}

async function handler(businessId: string, question: string) {
  const generator = new RestaurantSQLGenerator();

  const result = await generator.generateSQL(question, businessId, { debug: false });

  console.log({ result });

  console.log('\nResult:');
  console.log('Success:', result.success);
  console.log('Confidence:', result.confidence);
  console.log('Recommendation:', result.recommendation);

  if (result.success) {
    console.log('\nGenerated SQL:');
    console.log(result.query);
  } else {
    console.log('\nError:', result.error);
    console.log('Alternatives:', result.alternatives);
  }
}

export default function register(program: Command) {
  program
    .command('query-copilot')
    .description('Ask a question about your business')
    .argument('<businessId>', 'The business ID to query')
    .argument('<question>', 'The question you have about your business')
    .action(handler);
}
