import { program } from 'commander';
import dotenv from 'dotenv';
import registerSchedules from './cmd/schedules';
import registerSync from './cmd/syncSchema';
import registerTagger from './cmd/tagger';
import registerMatrix from './cmd/matrix';
import registerCredify from './cmd/credify';
import registerCreate from './cmd/create';
import registerLink from './cmd/link';
import registerInitializeRecipes from './cmd/initializeRecipes';
import registerLog from './cmd/streamLogs';
import registerResetPassword from './cmd/resetPassword';
import registerGetUser from './cmd/getUser';
import registerNamer from './cmd/namer';
import registerApplyAllTaxes from './cmd/applyAllTaxes';
import registerProductSync from './cmd/syncProducts';
import registerGetDeliverectProducts from './cmd/getDeliverectProducts';
import registerReviewTrackerSync from './cmd/syncReviewTrackers';
import registerClone from './cmd/clone';
import registerEnableInventory from './cmd/enableInventory';
import registerHydrateReviews from './cmd/hydrateReviews';
import registerGetManagers from './cmd/getManagers';
import registerSlide from './cmd/slide';
import registerCopyTable from './cmd/clone/copyTable';
import registerRecoverPayments from './cmd/recoverDirtyWaterPayments';
import registerConnect from './cmd/connect';
import { getCloudSQLCmdsPid } from './utils/globals';
import registerSetMenuLastPublishedAt from './cmd/setMenuLastPublishedAt';
import registerPresentCard from './cmd/presentCard';
import registerSetPassword from './cmd/setPassword';
import registerAddPermissionToRole from './cmd/addPermissionToRoleId';
import registerAddPermissionWhere from './cmd/addPermissionWhere';
import registerCreatePrintingSetting from './cmd/createPrintingSettings';
import registerAddAvailability from './cmd/addAvailabilityToAll';
import registerSelectivelyReviseHistory from './cmd/backfill/backfillReportingProducts';
import registerBackfillOrderType from './cmd/backfill/backfillOrderType';
import registerPayForComped from './cmd/payForComped';
import registerBackfillPayments from './cmd/paymentConversion';
import registerImportGiftCards from './cmd/giftCards/importCSV';
import registerResetPasswordsForBusiness from './cmd/resetPasswordsForBusiness';
import registerBackfillCashRefunds from './cmd/backfill/backfillCashRefunds';
import registerShrinkImages from './cmd/shrinkImages';
import registerFixReportingMods from './cmd/setReportingModifierCost';
import registerAddCustomerIDtToTab from './cmd/addCustomerIDtoTab';
import registerBackfillSubtotalsAndTax from './cmd/backfill/fillSubTotalsAndTax';
import registerBackfillTip from './cmd/backfill/backfillTip';
import registerMarkReportingProductVoided from './cmd/markReportingProductVoided';
import registerBackfillBusinessIdOnWorkEvents from './cmd/backfillBusinessIdOnWorkEvents';
import registerGenerateToken from './cmd/generateToken';
import registerCompressMenus from './cmd/compression/compressPublishedMenus';
import registerCompressStats from './cmd/compression/compressStats';
import registerMoveEnterprise from './cmd/moveEnterprise';
import registerImportFoodCost from './cmd/foodCost/importOCCSV';
import registerFixDeliverectDiscounts from './cmd/fixDeliverectDiscounts';
import registerBackfillFingerprint from './cmd/backfillFingerprint';
import registerBackfillLabels from './cmd/backfill/backfillLabels';
import registerImportCustomers from './cmd/customers/importCSV';
import registerBackfillCOGS from './cmd/backfill/backfillCOGS';
import registerCrawlForMenus from './cmd/crawlForMenus';
import registerCloneImproved from './cmd/clone/improved';
import registerMoveMoneyFromTango from './cmd/airwallex/move-money-from-tango';
import registerGetAirwallexStatement from './cmd/airwallex/get-airwallex-report';
import registerGetProductsFromMarginEdge from './cmd/getProductsFromMarginEdge';
import registerGetMarginEdgeRecipes from './cmd/getMarginEdgeRecipes';
import registerGetToastWeekData from './cmd/getWeekData';
import registerCacheUpdate from './cmd/cache-update';
import registerIngestToastData from './cmd/ingestToastData';
import registerUploadSchedule from './cmd/uploadSchedule';
import registerUpdateProductItems from './cmd/matching/updateProductItems';
import registerUpdateVariants from './cmd/matching/updateVariants';
import registerIngestBackups from './cmd/matching/ingestBackups';
import registerIngestSchedule from './cmd/ingestSchedule';
import registerIngestAvailability from './cmd/ingestAvailability';
import registerEncryptCredentials from './cmd/encryptCredentials';
import registerPrompting from './cmd/prompting';
import registerTestPrompt from './cmd/testPrompt';
import registerCreateBusinesses from './cmd/createBusinesses';
import registerCopilot from './cmd/copilot';

dotenv.config();

program
  .name('tango-flock')
  .description('A CLI Migration tool for the Tango DB')
  .option('-p, --production-access', `true if this run is allowed to access production data (only true if we're on main and up to date)`)
  .version('1.0.0')
  .hook('postAction', () => {
    getCloudSQLCmdsPid().forEach((pid: any) => {
      process.kill(pid);
    });
  });

console.log(`
 _____  ____  _      _____ ____    _____ _     ____  ____  _  __
/__ __\\/  _ \\/ \\  /|/  __//  _ \\  /    // \\   /  _ \\/   _\\/ |/ /
  / \\  | / \\|| |\\ ||| |  _| / \\|  |  __\\| |   | / \\||  /  |   / 
  | |  | |-||| | \\||| |_//| \\_/|  | |   | |_/\\| \\_/||  \\__|   \\ 
  \\_/  \\_/ \\|\\_/  \\|\\____\\\\____/  \\_/   \\____/\\____/\\____/\\_|\\_\\
                                                                `);
console.log('\x1b[33m Did you run yarn build... Or use yarn watch? \x1b[0m\n\n');

registerUpdateProductItems(program);
registerPrompting(program);
registerUpdateVariants(program);
registerSchedules(program);
registerSync(program);
registerTagger(program);
registerMatrix(program);
registerCredify(program);
registerCreate(program);
registerLink(program);
registerInitializeRecipes(program);
registerLog(program);
registerResetPassword(program);
registerGetUser(program);
registerNamer(program);
registerApplyAllTaxes(program);
registerProductSync(program);
registerGetDeliverectProducts(program);
registerReviewTrackerSync(program);
registerClone(program);
registerEnableInventory(program);
registerHydrateReviews(program);
registerGetManagers(program);
registerSlide(program);
registerCopyTable(program);
registerRecoverPayments(program);
registerConnect(program);
registerSetMenuLastPublishedAt(program);
registerPresentCard(program);
registerSetPassword(program);
registerAddPermissionToRole(program);
registerAddPermissionWhere(program);
registerFixDeliverectDiscounts(program);
registerCreatePrintingSetting(program);
registerAddAvailability(program);
registerSelectivelyReviseHistory(program);
registerBackfillOrderType(program);
registerPayForComped(program);
registerBackfillPayments(program);
registerImportGiftCards(program);
registerResetPasswordsForBusiness(program);
registerBackfillCashRefunds(program);
registerShrinkImages(program);
registerFixReportingMods(program);
registerAddCustomerIDtToTab(program);
registerBackfillSubtotalsAndTax(program);
registerBackfillTip(program);
registerMarkReportingProductVoided(program);
registerBackfillBusinessIdOnWorkEvents(program);
registerGenerateToken(program);
registerCompressMenus(program);
registerCompressStats(program);
registerMoveEnterprise(program);
registerImportFoodCost(program);
registerBackfillLabels(program);
registerImportCustomers(program);
registerBackfillCOGS(program);
registerBackfillFingerprint(program);
registerCrawlForMenus(program);
registerCloneImproved(program);
registerMoveMoneyFromTango(program);
registerGetAirwallexStatement(program);
registerGetProductsFromMarginEdge(program);
registerGetMarginEdgeRecipes(program);
registerGetToastWeekData(program);
registerCacheUpdate(program);
registerIngestToastData(program);
registerUploadSchedule(program);
registerIngestBackups(program);
registerIngestSchedule(program);
registerIngestAvailability(program);
registerEncryptCredentials(program);
registerTestPrompt(program);
registerCreateBusinesses(program);
registerCopilot(program);
program.parse();

process.on('uncaughtException', (err) => {
  console.error(err, 'Uncaught Exception thrown');
  getCloudSQLCmdsPid().forEach((pid: any) => {
    process.kill(pid);
  });
  process.exit(1);
});
