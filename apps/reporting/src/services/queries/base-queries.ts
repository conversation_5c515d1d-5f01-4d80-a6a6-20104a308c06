// Add staff member here...
// Add user to staff member
// Coalesce the name
export const SUB_PRODUCTS_CTE_QUERY = `
"products" AS (
  SELECT 
    "product".id AS reporting_product_id,
    "product".product_id,
    "tabs".id as tab_id,
    "tabs".staff_id,
    alcohol,
    COALESCE(NULLIF("product".menu_category, ''), 'Uncategorized') AS "menu_category",
    menu_name,
    "tabs".order_type,
    COALESCE(NULLIF("product".name, ''), 'Unnamed Product') AS "name",
    "product".quantity AS ordered_quantity,
    "product".type,
    "subType",
    "product".business_id,
    "product".ordered_at,
    (
      (
        date_trunc('day', "product".ordered_at AT TIME ZONE 'UTC' AT TIME ZONE "business".timezone)
        + INTERVAL '4 hours'
        - CASE 
            WHEN ("product".ordered_at AT TIME ZONE 'UTC' AT TIME ZONE "business".timezone)::time < TIME '04:00'
            THEN INTERVAL '1 day'
            ELSE INTERVAL '0'
          END
      ) AT TIME ZONE "business".timezone
    ) AT TIME ZONE 'UTC' AS "ordered_day",
    COALESCE(float_sub_total_amount, sub_total_amount) * (
      ("product".quantity - COALESCE(SUM("voids".quantity), 0)) / "product".quantity
    ) AS "sub_total_amount",
    COALESCE(SUM("voids".quantity), 0) AS "voided_quantity",
    "product".quantity - COALESCE(SUM("voids".quantity), 0) AS "through_quantity",
    COALESCE((array_agg("discounts".data))[1], '[]'::jsonb) AS "discounts_applied",
    COALESCE((array_agg("discounts".discount_amount))[1], 0) AS "discount_amount"
  FROM "ReportingProduct" AS "product"
  LEFT JOIN "ReportingProductEvent" AS "voids"
    ON "voids".product_id = "product".id AND "voids".event_type = 'voided'
  LEFT JOIN LATERAL (
    SELECT 
      *,
      (
        SELECT SUM(("elem"->>'amount')::numeric)
        FROM jsonb_array_elements(COALESCE(data, '[]'::jsonb)) AS elem
      ) AS discount_amount
    FROM "ReportingProductEvent"
    WHERE "ReportingProductEvent".product_id = "product".id
      AND "ReportingProductEvent".event_type = 'discount_changed'
    ORDER BY "ReportingProductEvent".event_at DESC
    LIMIT 1
  ) AS "discounts" ON true
  INNER JOIN "Businesses" AS "business" ON "business".id = "product".business_id
  INNER JOIN "Tabs" AS "tabs" ON "tabs".id = "product".tab_id
  GROUP BY "product".id, "business".timezone, "tabs".order_type, "tabs".id, "tabs".staff_id 
)
`;

export const PRODUCTS_CTE_QUERY = `
WITH ${SUB_PRODUCTS_CTE_QUERY}
`;

export const SUB_TABS_CTE_QUERY = `
"tabs" AS (
  SELECT 
    "tabs".id,
    "tabs".business_id,
    "tabs".staff_id,
    "tabs".created_at,
    "tabs".number_of_guests,
    "tabs".tip_amount,
    ROUND(EXTRACT(EPOCH FROM "tabs"."completedAt" - "tabs"."created_at") / 60) AS minutes_open,
    (
      (
        date_trunc('day', "tabs".created_at AT TIME ZONE 'UTC' AT TIME ZONE "business".timezone)
        + INTERVAL '4 hours'
        - CASE 
            WHEN ("tabs".created_at AT TIME ZONE 'UTC' AT TIME ZONE "business".timezone)::time < TIME '04:00'
            THEN INTERVAL '1 day'
            ELSE INTERVAL '0'
          END
      ) AT TIME ZONE "business".timezone
    ) AT TIME ZONE 'UTC' AS "created_day"
    FROM "Tabs" AS "tabs"
    INNER JOIN "Businesses" AS "business" ON "business".id = "tabs".business_id
)
`;

export const TABS_CTE_QUERY = `
WITH ${SUB_TABS_CTE_QUERY}
`;

export const SUB_WORK_EVENTS_CTE_QUERY = `
work_events AS (
  SELECT
    "WorkEvents".id,
    "WorkEvents".business_id,
    "WorkEvents".staff_id,
    "PayRates".role_id,
    "WorkEvents".start_time,
    "WorkEvents".end_time,
    "WorkEvents".regular_minutes,
    "WorkEvents".overtime_minutes,
    "WorkEvents".unpaid_minutes,
    "PayRates".hourly_rate,
    COALESCE("PayRates".overtime_rate, 0) AS overtime_rate,
    ROUND(EXTRACT(EPOCH FROM "WorkEvents".end_time - "WorkEvents".start_time) / 60, 2) AS minutes_worked,
    ROUND((EXTRACT(EPOCH FROM "WorkEvents".end_time - "WorkEvents".start_time) / 3600) * "PayRates".hourly_rate / 100, 2) AS total_pay
    (
      (
        date_trunc('day', "WorkEvents".start_time AT TIME ZONE 'UTC' AT TIME ZONE "business".timezone)
          + INTERVAL '4 hours'
            - CASE
              WHEN ("WorkEvents".start_time AT TIME ZONE 'UTC' AT TIME ZONE "business".timezone)::time < TIME '04:00'
              THEN INTERVAL '1 day'
              ELSE INTERVAL '0'
            END
      ) AT TIME ZONE "business".timezone
    ) AT TIME ZONE 'UTC' AS "work_day",
    "business".timezone
  FROM "WorkEvents"
  INNER JOIN "PayRates" ON "PayRates".id = "WorkEvents".pay_rate_id
  INNER JOIN "Businesses" AS "business" ON "business".id = "WorkEvents".business_id::uuid
  WHERE "WorkEvents".deleted_at IS NULL
)
`;

export const WORK_EVENTS_CTE_QUERY = `
WITH ${SUB_WORK_EVENTS_CTE_QUERY}
`;

export const DAILY_SALES_LABOUR_GUEST_CTE_QUERY = `
WITH ${SUB_PRODUCTS_CTE_QUERY}, ${SUB_TABS_CTE_QUERY}, ${SUB_WORK_EVENTS_CTE_QUERY},
"daily_sales_guests_labour" AS (
    SELECT
        "daily_guests_sales".*,
        COALESCE("work_events".total_pay, 0) AS "total_pay",
        COALESCE("work_events".regular_pay, 0) AS "regular_pay",
        COALESCE("work_events".overtime_pay, 0) AS "overtime_pay",
        COALESCE("work_events".minutes_worked, 0) AS "minutes_worked",
        COALESCE("work_events".regular_minutes, 0) AS "regular_minutes",
        COALESCE("work_events".unpaid_minutes, 0) AS "unpaid_minutes",
        COALESCE("work_events".overtime_minutes, 0) AS "overtime_minutes"
    FROM (
            SELECT
                "daily_sales".*,
                COALESCE("tabs".guest_count, 0) AS "guest_count",
                COALESCE("tabs".check_count, 0) AS "check_count",
                COALESCE("tabs".minutes_open, 0) AS "minutes_open"
            FROM
                (
                    SELECT
                        "products".ordered_day,
                        "products".business_id,
                        SUM("products".sub_total_amount) AS "sales_total",
                        SUM(
                            CASE
                                WHEN "products".order_type = 'dineIn'
                                THEN "products".sub_total_amount
                                ELSE 0
                            END
                        ) AS "dine_in_total",
                        SUM(
                            CASE
                                WHEN "products".order_type = 'carryOut'
                                THEN "products".sub_total_amount
                                ELSE 0
                            END
                        ) AS "carry_out_total",
                        SUM(
                            CASE
                                WHEN "products".order_type = 'delivery'
                                THEN "products".sub_total_amount
                                ELSE 0
                            END
                        ) AS "delivery_total",
                        SUM("products".through_quantity) AS "sales_unit",
                        SUM("products".discount_amount) AS "discount_total"
                    FROM "products"
                    GROUP BY "products".ordered_day, "products".business_id
                ) AS "daily_sales"
            LEFT JOIN (
                SELECT
                    "tabs".created_day,
                    "tabs".business_id,
                    COUNT("tabs".id) AS "check_count",
                    SUM("tabs".number_of_guests) AS "guest_count",
                    SUM("tabs".minutes_open) AS "minutes_open"
                FROM "tabs"
                GROUP BY "tabs".created_day, "tabs".business_id
                ) "tabs" ON "tabs".created_day = "daily_sales".ordered_day AND "tabs".business_id = "daily_sales".business_id
         ) "daily_guests_sales"
    LEFT JOIN (
        SELECT
            "work_events".work_day,
            "work_events".business_id,
            SUM("work_events".total_pay) AS "total_pay",
            SUM("work_events".regular_pay) AS "regular_pay",
            SUM("work_events".overtime_pay) AS "overtime_pay",
            SUM("work_events".minutes_worked) AS "minutes_worked",
            SUM("work_events".regular_minutes) AS "regular_minutes",
            SUM("work_events".unpaid_minutes) AS "unpaid_minutes",
            SUM("work_events".overtime_minutes) AS "overtime_minutes"
        FROM "work_events"
        GROUP BY "work_events".work_day, "work_events".business_id
    ) "work_events" ON "work_events".work_day = "daily_guests_sales".ordered_day AND "work_events".business_id::uuid = "daily_guests_sales".business_id
)
`;

export function SUB_TIMEPOINTS_CTE_QUERY(businessId: string, startDate: Date, endDate: Date) {
  return `
"timepoints" AS (
    SELECT
        "timepoints" AS "timepoint",
        EXTRACT(ISODOW FROM "timepoints") AS "day",
        -- minutes after midnight
        (
          (
            date_trunc('day', timepoints AT TIME ZONE 'UTC' AT TIME ZONE "business".timezone)
              + INTERVAL '4 hours'
                - CASE
                  WHEN (timepoints AT TIME ZONE 'UTC' AT TIME ZONE "business".timezone)::time < TIME '04:00'
                  THEN INTERVAL '1 day'
                  ELSE INTERVAL '0'
                END
          ) AT TIME ZONE "business".timezone
        ) AT TIME ZONE 'UTC' AS "business_day",
        EXTRACT(EPOCH FROM "timepoints" - (
          (
            date_trunc('day', timepoints AT TIME ZONE 'UTC' AT TIME ZONE "business".timezone)
              + INTERVAL '4 hours'
                - CASE
                  WHEN (timepoints AT TIME ZONE 'UTC' AT TIME ZONE "business".timezone)::time < TIME '04:00'
                  THEN INTERVAL '1 day'
                  ELSE INTERVAL '0'
                END
          ) AT TIME ZONE "business".timezone
        ) AT TIME ZONE 'UTC') / 60 AS "offset"
    FROM generate_series(
        '${startDate.toISOString()}'::timestamp,
        '${endDate.toISOString()}'::timestamp,
        '15 minutes'
     ) "timepoints"
    INNER JOIN "Businesses" AS "business" ON "business".id='${businessId}'
    WHERE "business".id='${businessId}'
)
  `;
}

export const ACTIVE_WORK_EVENTS_CTE_QUERY = `
  WITH active_work_events AS (
    SELECT
        "Businesses".id AS "business_id",
        "Roles".id AS "role_id",
        "Roles".department_id AS "department_id",
        "WorkEvents".start_time,
        COALESCE("WorkEvents".end_time, NOW()) AS "end_time",
        COALESCE("PayRates".adjusted_hourly_rate, "PayRates".hourly_rate) AS "hourly_rate",
        (
            (
                date_trunc('day', "WorkEvents".start_time AT TIME ZONE 'UTC' AT TIME ZONE "Businesses".timezone)
                    + INTERVAL '4 hours'
                        - CASE
                            WHEN ("WorkEvents".start_time AT TIME ZONE 'UTC' AT TIME ZONE "Businesses".timezone)::time < TIME '04:00'
                            THEN INTERVAL '1 day'
                            ELSE INTERVAL '0'
                        END
            ) AT TIME ZONE "Businesses".timezone
        ) AT TIME ZONE 'UTC' AS "work_day",
        EXTRACT(ISODOW FROM (
            (
                date_trunc('day', "WorkEvents".start_time AT TIME ZONE 'UTC' AT TIME ZONE "Businesses".timezone)
                    + INTERVAL '4 hours'
                        - CASE
                            WHEN ("WorkEvents".start_time AT TIME ZONE 'UTC' AT TIME ZONE "Businesses".timezone)::time < TIME '04:00'
                            THEN INTERVAL '1 day'
                            ELSE INTERVAL '0'
                        END
            ) AT TIME ZONE "Businesses".timezone
        ) AT TIME ZONE 'UTC') AS "isodow"
    FROM "WorkEvents"
    INNER JOIN "Businesses" ON "Businesses".id = "WorkEvents".business_id::uuid
    INNER JOIN "PayRates" ON "PayRates".id = "WorkEvents".pay_rate_id
    INNER JOIN "Roles" ON "PayRates".role_id = "Roles".id
    WHERE "WorkEvents".deleted_at IS NULL
    AND EXTRACT(EPOCH FROM COALESCE("WorkEvents".end_time, NOW()) - "WorkEvents".start_time) / (60 * 60) < 18
)
`;

export const SHIFTS_CTE_QUERY = `
WITH schedule AS (
    SELECT
        "Businesses".id AS "business_id",
        "Roles".department_id AS "department_id",
        "Roles".id AS "role_id",
        "Shifts".start_time,
        "Shifts".end_time,
        COALESCE("PayRates".adjusted_hourly_rate, "PayRates".hourly_rate) AS "hourly_rate",
        (
            (
                date_trunc('day', "Shifts".start_time AT TIME ZONE 'UTC' AT TIME ZONE "Businesses".timezone)
                    + INTERVAL '4 hours'
                        - CASE
                            WHEN ("Shifts".start_time AT TIME ZONE 'UTC' AT TIME ZONE "Businesses".timezone)::time < TIME '04:00'
                            THEN INTERVAL '1 day'
                            ELSE INTERVAL '0'
                        END
            ) AT TIME ZONE "Businesses".timezone
        ) AT TIME ZONE 'UTC' AS "work_day",
        EXTRACT(ISODOW FROM (
            (
                date_trunc('day', "Shifts".start_time AT TIME ZONE 'UTC' AT TIME ZONE "Businesses".timezone)
                    + INTERVAL '4 hours'
                        - CASE
                            WHEN ("Shifts".start_time AT TIME ZONE 'UTC' AT TIME ZONE "Businesses".timezone)::time < TIME '04:00'
                            THEN INTERVAL '1 day'
                            ELSE INTERVAL '0'
                        END
            ) AT TIME ZONE "Businesses".timezone
        ) AT TIME ZONE 'UTC') AS "isodow"
    FROM "Shifts"
    INNER JOIN "Businesses" ON "Businesses".id="Shifts".business_id
    INNER JOIN "PayRates" ON "PayRates".id="Shifts".pay_rate_id
    INNER JOIN "Roles" ON "Roles".id="PayRates".role_id
)
`;

export const SUB_DAILY_TRENDS_CTE_QUERY = `
"daily_trends" AS (
  SELECT 
    "products".business_id,
    CASE EXTRACT(DOW FROM "products".ordered_day)
      WHEN 0 THEN 'sunday'
      WHEN 1 THEN 'monday'
      WHEN 2 THEN 'tuesday'
      WHEN 3 THEN 'wednesday'
      WHEN 4 THEN 'thursday'
      WHEN 5 THEN 'friday'
      WHEN 6 THEN 'saturday'
    END as day_name,
    EXTRACT(DOW FROM "products".ordered_day) as day_number,
    COUNT(DISTINCT "products".ordered_day) as days_count,
    ROUND(SUM("products".sub_total_amount)::NUMERIC / 100, 2) as total_revenue,
    COUNT(DISTINCT CONCAT("products".business_id, "products".ordered_day, "products".name)) as total_orders,
    SUM("products".through_quantity) as total_items,
    ROUND((SUM("products".sub_total_amount) / NULLIF(COUNT(DISTINCT CONCAT("products".business_id, "products".ordered_day, "products".name)), 0))::NUMERIC / 100, 2) as average_order_value,
    ROUND((SUM("products".sub_total_amount) / NULLIF(COUNT(DISTINCT "products".ordered_day), 0))::NUMERIC / 100, 2) as average_daily_revenue
  FROM "products"
  GROUP BY "products".business_id, EXTRACT(DOW FROM "products".ordered_day)
)
`;

export const DAILY_TRENDS_CTE_QUERY = `
WITH ${SUB_PRODUCTS_CTE_QUERY}, ${SUB_DAILY_TRENDS_CTE_QUERY}
`;

export const SUB_TOP_ITEMS_CTE_QUERY = `
"top_items" AS (
  SELECT 
    "products".business_id,
    "products".name,
    COALESCE("products".menu_category, 'Uncategorized') as menu_category,
    "products".type,
    "products".alcohol,
    ROUND(SUM("products".sub_total_amount)::NUMERIC / 100, 2) as total_revenue,
    SUM("products".through_quantity) as total_orders,
    SUM("products".ordered_quantity) as total_quantity_ordered,
    SUM("products".voided_quantity) as total_voided,
    ROUND((SUM("products".sub_total_amount) / NULLIF(SUM("products".through_quantity), 0))::NUMERIC / 100, 2) as average_item_price,
    COUNT(DISTINCT "products".ordered_day) as days_sold,
    ROUND((SUM("products".through_quantity)::NUMERIC / NULLIF(COUNT(DISTINCT "products".ordered_day), 0)), 2) as average_daily_quantity,
    -- Revenue rank within business
    ROW_NUMBER() OVER (PARTITION BY "products".business_id ORDER BY SUM("products".sub_total_amount) DESC) as revenue_rank,
    -- Quantity rank within business  
    ROW_NUMBER() OVER (PARTITION BY "products".business_id ORDER BY SUM("products".through_quantity) DESC) as quantity_rank,
    -- Category revenue rank
    ROW_NUMBER() OVER (PARTITION BY "products".business_id, "products".menu_category ORDER BY SUM("products".sub_total_amount) DESC) as category_revenue_rank
  FROM "products"
  GROUP BY "products".business_id, "products".name, "products".menu_category, "products".type, "products".alcohol
)
`;

export const TOP_ITEMS_CTE_QUERY = `
WITH ${SUB_PRODUCTS_CTE_QUERY}, ${SUB_TOP_ITEMS_CTE_QUERY}
`;

export const SUB_OVERALL_METRICS_CTE_QUERY = `
"overall_metrics" AS (
  SELECT 
    "products".business_id,
    -- Revenue metrics
    ROUND(SUM("products".sub_total_amount)::NUMERIC / 100, 2) as total_revenue,
    ROUND(AVG("products".sub_total_amount)::NUMERIC / 100, 2) as average_sale_amount,
    ROUND(MIN("products".sub_total_amount)::NUMERIC / 100, 2) as min_sale_amount,
    ROUND(MAX("products".sub_total_amount)::NUMERIC / 100, 2) as max_sale_amount,
    
    -- Order metrics
    COUNT(DISTINCT CONCAT("products".business_id, "products".ordered_day, "products".name)) as total_orders,
    COUNT(*) as total_line_items,
    ROUND((COUNT(*)::NUMERIC / NULLIF(COUNT(DISTINCT CONCAT("products".business_id, "products".ordered_day, "products".name)), 0)), 2) as average_items_per_order,
    
    -- Quantity metrics
    SUM("products".through_quantity) as total_items_sold,
    SUM("products".ordered_quantity) as total_items_ordered,
    SUM("products".voided_quantity) as total_items_voided,
    ROUND((SUM("products".voided_quantity)::NUMERIC / NULLIF(SUM("products".ordered_quantity), 0) * 100), 2) as void_rate_percentage,
    
    -- Average order value
    ROUND((SUM("products".sub_total_amount) / NULLIF(COUNT(DISTINCT CONCAT("products".business_id, "products".ordered_day, "products".name)), 0))::NUMERIC / 100, 2) as average_order_value,
    
    -- Time period metrics
    MIN("products".ordered_day) as first_sale_date,
    MAX("products".ordered_day) as last_sale_date,
    COUNT(DISTINCT "products".ordered_day) as total_business_days,
    ROUND((SUM("products".sub_total_amount) / NULLIF(COUNT(DISTINCT "products".ordered_day), 0))::NUMERIC / 100, 2) as average_daily_revenue,
    
    -- Product diversity metrics
    COUNT(DISTINCT "products".name) as unique_products_sold,
    COUNT(DISTINCT "products".menu_category) as unique_categories_sold,
    COUNT(DISTINCT "products".type) as unique_product_types,
    
    -- Category breakdown
    ROUND(SUM(CASE WHEN "products".type = 'Food.' THEN "products".sub_total_amount ELSE 0 END)::NUMERIC / 100, 2) as food_revenue,
    ROUND(SUM(CASE WHEN "products".alcohol = true THEN "products".sub_total_amount ELSE 0 END)::NUMERIC / 100, 2) as alcohol_revenue,
    ROUND(SUM(CASE WHEN "products".alcohol = false AND "products".type != 'Food.' THEN "products".sub_total_amount ELSE 0 END)::NUMERIC / 100, 2) as non_alcoholic_beverage_revenue,
    
    -- Order type breakdown
    ROUND(SUM(CASE WHEN "products".order_type = 'dineIn' THEN "products".sub_total_amount ELSE 0 END)::NUMERIC / 100, 2) as dine_in_revenue,
    ROUND(SUM(CASE WHEN "products".order_type = 'carryOut' THEN "products".sub_total_amount ELSE 0 END)::NUMERIC / 100, 2) as carry_out_revenue,
    ROUND(SUM(CASE WHEN "products".order_type = 'delivery' THEN "products".sub_total_amount ELSE 0 END)::NUMERIC / 100, 2) as delivery_revenue
  FROM "products"
  GROUP BY "products".business_id
)
`;

export const OVERALL_METRICS_CTE_QUERY = `
WITH ${SUB_PRODUCTS_CTE_QUERY}, ${SUB_OVERALL_METRICS_CTE_QUERY}
`;

// Combined CTE that includes all context CTEs
export const SALES_CONTEXT_CTE_QUERY = `
WITH ${SUB_PRODUCTS_CTE_QUERY}, ${SUB_DAILY_TRENDS_CTE_QUERY}, ${SUB_TOP_ITEMS_CTE_QUERY}, ${SUB_OVERALL_METRICS_CTE_QUERY}
`;

export const SUB_SCHEDULE_DETAILS_CTE_QUERY = `
"schedule_details" AS (
  SELECT 
    "business".id as business_id,
    "shifts".work_day as schedule_date,
    COALESCE("users".first_name || ' ' || "users".last_name, 'Unknown Staff') as staff_name,
    "staff".id as staff_id,
    COALESCE("roles".name, 'Unknown Role') as role_name,
    "roles".id as role_id,
    COALESCE("departments".name, 'Unknown Department') as department_name,
    "departments".id as department_id,
    CASE 
      WHEN EXTRACT(EPOCH FROM "shifts".end_time - "shifts".start_time) / 3600 >= 8 THEN 'Full Time'
      WHEN EXTRACT(EPOCH FROM "shifts".end_time - "shifts".start_time) / 3600 >= 4 THEN 'Part Time'
      ELSE 'Short Shift'
    END as shift_type,
    "shifts".start_time,
    "shifts".end_time,
    ROUND(EXTRACT(EPOCH FROM "shifts".end_time - "shifts".start_time) / 3600, 2) as scheduled_hours,
    COALESCE("pay_rates".adjusted_hourly_rate, "pay_rates".hourly_rate, 0) as pay_rate_hourly,
    COALESCE("pay_rates".adjusted_hourly_rate, "pay_rates".hourly_rate, 0) * 2080 as pay_rate_annual, -- 40 hours * 52 weeks
    ROUND((COALESCE("pay_rates".adjusted_hourly_rate, "pay_rates".hourly_rate, 0) * EXTRACT(EPOCH FROM "shifts".end_time - "shifts".start_time) / 3600)::NUMERIC, 2) as scheduled_labor_cost,
    "shifts".isodow as day_of_week,
    CASE "shifts".isodow
      WHEN 1 THEN 'Monday'
      WHEN 2 THEN 'Tuesday'
      WHEN 3 THEN 'Wednesday'
      WHEN 4 THEN 'Thursday'
      WHEN 5 THEN 'Friday'
      WHEN 6 THEN 'Saturday'
      WHEN 7 THEN 'Sunday'
    END as day_name
  FROM "schedule" as "shifts"
  INNER JOIN "Businesses" as "business" ON "business".id = "shifts".business_id
  INNER JOIN "PayRates" as "pay_rates" ON "pay_rates".id = "shifts".pay_rate_id
  INNER JOIN "Roles" as "roles" ON "roles".id = "pay_rates".role_id
  LEFT JOIN "Departments" as "departments" ON "departments".id = "roles".department_id
  LEFT JOIN "StaffMember" as "staff" ON "staff".role_id = "roles".id AND "staff".business_id = "business".id
  LEFT JOIN "Users" as "users" ON "users".id = "staff".user_id
)
`;

export const SCHEDULE_DETAILS_CTE_QUERY = `
WITH ${SUB_WORK_EVENTS_CTE_QUERY}, ${SHIFTS_CTE_QUERY}, ${SUB_SCHEDULE_DETAILS_CTE_QUERY}
`;

export const SUB_ACTUAL_WORK_DETAILS_CTE_QUERY = `
"actual_work_details" AS (
  SELECT 
    "work_events".business_id,
    "work_events".work_day,
    COALESCE("users".first_name || ' ' || "users".last_name, 'Unknown Staff') as staff_name,
    "work_events".staff_id,
    COALESCE("roles".name, 'Unknown Role') as role_name,
    "work_events".role_id,
    COALESCE("departments".name, 'Unknown Department') as department_name,
    "departments".id as department_id,
    CASE 
      WHEN "work_events".minutes_worked >= 480 THEN 'Full Time' -- 8+ hours
      WHEN "work_events".minutes_worked >= 240 THEN 'Part Time' -- 4+ hours
      ELSE 'Short Shift'
    END as shift_type,
    "work_events".start_time,
    "work_events".end_time,
    ROUND("work_events".minutes_worked / 60.0, 2) as actual_hours,
    "work_events".hourly_rate as pay_rate_hourly,
    "work_events".hourly_rate * 2080 as pay_rate_annual,
    ROUND("work_events".total_pay::NUMERIC / 100, 2) as actual_labor_cost,
    ROUND("work_events".regular_pay::NUMERIC / 100, 2) as regular_pay,
    ROUND("work_events".overtime_pay::NUMERIC / 100, 2) as overtime_pay,
    "work_events".regular_minutes,
    "work_events".overtime_minutes,
    EXTRACT(ISODOW FROM "work_events".work_day) as day_of_week,
    CASE EXTRACT(ISODOW FROM "work_events".work_day)
      WHEN 1 THEN 'Monday'
      WHEN 2 THEN 'Tuesday'
      WHEN 3 THEN 'Wednesday'
      WHEN 4 THEN 'Thursday'
      WHEN 5 THEN 'Friday'
      WHEN 6 THEN 'Saturday'
      WHEN 7 THEN 'Sunday'
    END as day_name
  FROM "work_events"
  INNER JOIN "Roles" as "roles" ON "roles".id = "work_events".role_id
  LEFT JOIN "Departments" as "departments" ON "departments".id = "roles".department_id
  LEFT JOIN "StaffMember" as "staff" ON "staff".id = "work_events".staff_id
  LEFT JOIN "Users" as "users" ON "users".id = "staff".user_id
)
`;

export const ACTUAL_WORK_DETAILS_CTE_QUERY = `
WITH ${SUB_WORK_EVENTS_CTE_QUERY}, ${SUB_ACTUAL_WORK_DETAILS_CTE_QUERY}
`;

export const SUB_STAFF_PERFORMANCE_CTE_QUERY = `
"staff_performance" AS (
  SELECT 
    "actual_work_details".business_id,
    "actual_work_details".staff_name,
    "actual_work_details".staff_id,
    "actual_work_details".role_name,
    "actual_work_details".department_name,
    -- Actual work metrics
    ROUND(SUM("actual_work_details".actual_hours), 2) as total_hours,
    ROUND(SUM("actual_work_details".actual_labor_cost), 2) as total_earned,
    COUNT(*) as total_shifts,
    ROUND(AVG("actual_work_details".pay_rate_hourly)::NUMERIC / 100, 2) as pay_rate_hourly,
    ROUND(AVG("actual_work_details".pay_rate_annual)::NUMERIC / 100, 2) as pay_rate_annual,
    -- Performance metrics
    ROUND(AVG("actual_work_details".actual_hours), 2) as average_hours_per_shift,
    ROUND(SUM("actual_work_details".regular_pay)::NUMERIC / 100, 2) as total_regular_pay,
    ROUND(SUM("actual_work_details".overtime_pay)::NUMERIC / 100, 2) as total_overtime_pay,
    SUM("actual_work_details".regular_minutes) as total_regular_minutes,
    SUM("actual_work_details".overtime_minutes) as total_overtime_minutes,
    COUNT(DISTINCT "actual_work_details".work_day) as days_worked,
    MIN("actual_work_details".work_day) as first_work_day,
    MAX("actual_work_details".work_day) as last_work_day,
    -- Tips
    COALESCE("tips_data".total_tips, 0) as total_tips,
    COALESCE("tips_data".tables_served, 0) as tables_served,
    COALESCE("tips_data".average_tip_per_table, 0) as average_tip_per_table,
    -- Scheduled vs actual comparison (if scheduled data exists)
    COALESCE(COUNT("scheduled".schedule_date), 0) as total_scheduled_shifts,
    COALESCE(ROUND(SUM("scheduled".scheduled_hours), 2), 0) as total_scheduled_hours,
    COALESCE(ROUND(SUM("scheduled".scheduled_labor_cost), 2), 0) as total_scheduled_cost
  FROM "actual_work_details" as "actual_work_details"
  LEFT JOIN (
    SELECT 
      staff_id,
      business_id,
      ROUND(SUM(tip_amount)::NUMERIC / 100, 2) as total_tips,
      COUNT(*) as tables_served,
      ROUND(AVG(tip_amount)::NUMERIC / 100, 2) as average_tip_per_table
    FROM "tabs" 
    WHERE staff_id IS NOT NULL
    GROUP BY staff_id, business_id
  ) "tips_data" ON "tips_data".staff_id = "actual_work_details".staff_id 
    AND "tips_data".business_id = "actual_work_details".business_id
  LEFT JOIN "schedule_details" as "scheduled" 
    ON "scheduled".business_id = "actual_work_details".business_id 
    AND "scheduled".staff_id = "actual_work_details".staff_id
    AND "scheduled".schedule_date = "actual_work_details".work_day
  GROUP BY 
    "actual_work_details".business_id,
    "actual_work_details".staff_name,
    "actual_work_details".staff_id,
    "actual_work_details".role_name,
    "actual_work_details".department_name
)
`;

export const STAFF_PERFORMANCE_CTE_QUERY = `
WITH ${SUB_WORK_EVENTS_CTE_QUERY}, ${SHIFTS_CTE_QUERY}, ${SUB_SCHEDULE_DETAILS_CTE_QUERY}, ${SUB_ACTUAL_WORK_DETAILS_CTE_QUERY}, ${SUB_STAFF_PERFORMANCE_CTE_QUERY}
`;

export const SUB_SCHEDULE_SUMMARY_CTE_QUERY = `
"schedule_summary" AS (
  SELECT 
    "schedule_data".business_id,
    -- Overall totals
    ROUND(SUM("schedule_data".scheduled_hours), 2) as total_scheduled_hours,
    ROUND(SUM("schedule_data".scheduled_labor_cost), 2) as total_scheduled_cost,
    COUNT(*) as total_scheduled_shifts,
    COUNT(DISTINCT "schedule_data".staff_id) as total_scheduled_staff,
    COUNT(DISTINCT "schedule_data".schedule_date) as total_scheduled_days,
    
    -- Actual work totals (if available)
    COALESCE(ROUND(SUM("actual_work_details".total_hours), 2), 0) as total_actual_hours,
    COALESCE(ROUND(SUM("actual_work_details".total_earned), 2), 0) as total_actual_cost,
    COALESCE(SUM("actual_work_details".total_shifts), 0) as total_actual_shifts,
    COALESCE(COUNT(DISTINCT "actual_work_details".staff_id), 0) as total_active_staff,
    
    -- Daily averages
    ROUND(AVG("schedule_data".scheduled_hours), 2) as average_hours_per_shift,
    ROUND(SUM("schedule_data".scheduled_labor_cost) / NULLIF(COUNT(DISTINCT "schedule_data".schedule_date), 0), 2) as average_cost_per_day,
    ROUND(COUNT(*)::NUMERIC / NULLIF(COUNT(DISTINCT "schedule_data".schedule_date), 0), 2) as average_shifts_per_day,
    
    -- Time period
    MIN("schedule_data".schedule_date) as period_start,
    MAX("schedule_data".schedule_date) as period_end,
    
    -- Busiest day analysis
    (SELECT "day_stats".day_name 
     FROM (
       SELECT 
         "schedule_data_inner".day_name,
         COUNT(*) as shift_count,
         ROW_NUMBER() OVER (ORDER BY COUNT(*) DESC) as rn
       FROM "schedule_details" as "schedule_data_inner"
       WHERE "schedule_data_inner".business_id = "schedule_data".business_id
       GROUP BY "schedule_data_inner".day_name
     ) "day_stats"
     WHERE "day_stats".rn = 1
    ) as busiest_day,
    
    (SELECT MAX("day_stats".shift_count)
     FROM (
       SELECT 
         "schedule_data_inner".day_name,
         COUNT(*) as shift_count
       FROM "schedule_details" as "schedule_data_inner" 
       WHERE "schedule_data_inner".business_id = "schedule_data".business_id
       GROUP BY "schedule_data_inner".day_name
     ) "day_stats"
    ) as busiest_day_shifts,
    
    -- Most scheduled staff
    (SELECT "staff_stats".staff_name
     FROM (
       SELECT 
         "schedule_data_inner".staff_name,
         COUNT(*) as shift_count,
         ROW_NUMBER() OVER (ORDER BY COUNT(*) DESC) as rn
       FROM "schedule_details" as "schedule_data_inner"
       WHERE "schedule_data_inner".business_id = "schedule_data".business_id
       GROUP BY "schedule_data_inner".staff_name
     ) "staff_stats"
     WHERE "staff_stats".rn = 1
    ) as most_scheduled_staff,
    
    (SELECT MAX("staff_stats".shift_count)
     FROM (
       SELECT 
         "schedule_data_inner".staff_name,
         COUNT(*) as shift_count
       FROM "schedule_details" as "schedule_data_inner"
       WHERE "schedule_data_inner".business_id = "schedule_data".business_id
       GROUP BY "schedule_data_inner".staff_name
     ) "staff_stats"
    ) as most_scheduled_shifts
    
  FROM "schedule_details" as "schedule_data"
  LEFT JOIN "staff_performance" as "actual_work_details" 
    ON "actual_work_details".business_id = "schedule_data".business_id
  GROUP BY "schedule_data".business_id
)
`;

export const SCHEDULE_SUMMARY_CTE_QUERY = `
WITH ${SUB_WORK_EVENTS_CTE_QUERY}, ${SHIFTS_CTE_QUERY}, ${SUB_SCHEDULE_DETAILS_CTE_QUERY}, ${SUB_ACTUAL_WORK_DETAILS_CTE_QUERY}, ${SUB_STAFF_PERFORMANCE_CTE_QUERY}, ${SUB_SCHEDULE_SUMMARY_CTE_QUERY}
`;

// Combined CTE that includes all schedule/labor CTEs
export const FULL_SCHEDULE_LABOR_CTE_QUERY = `
WITH ${SUB_WORK_EVENTS_CTE_QUERY}, ${SHIFTS_CTE_QUERY}, ${SUB_SCHEDULE_DETAILS_CTE_QUERY}, ${SUB_ACTUAL_WORK_DETAILS_CTE_QUERY}, ${SUB_STAFF_PERFORMANCE_CTE_QUERY}, ${SUB_SCHEDULE_SUMMARY_CTE_QUERY}
`;
