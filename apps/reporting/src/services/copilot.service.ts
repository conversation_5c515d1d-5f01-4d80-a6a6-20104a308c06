import { HttpStatus, Logger } from '@nestjs/common';
import { QueryCopilotResponse } from '../proto/reporting/copilot.pb';
import { InjectRepository } from '@nestjs/typeorm';
import { MoreThan, Repository } from 'typeorm';
import { Menu } from 'apps/menu/src/entities/menu.entity';
import { StaffMember } from 'apps/staffing/src/entities/staff/staff-member.entity';
import Agent from './agents/agent';
import SchemaSelectorAgent from './agents/schemaSelectorAgent';
import QueryDecomposerAgent from './agents/queryDecomposerAgent';
import SQLGeneratorAgent from './agents/sqlGeneratorAgent';
import ValidatorAgent from './agents/validatorAgent';
import ConfidenceScorerAgent from './agents/confidenceScoreAgent';
import { SUB_PRODUCTS_CTE_QUERY, SUB_TABS_CTE_QUERY, SUB_WORK_EVENTS_CTE_QUERY } from './queries/base-queries';
import { Shift } from 'apps/scheduling/src/entities/scheduling/shift.entity';
import { WorkEvent } from 'apps/staffing/src/entities/payroll/work-event.entity';
import dayjs from 'dayjs';
import { QueryCopilotRequestDto, HistoricalChatDto } from '../dtos/copilot.dto';
import { dateToProtoTimestamp } from '@tangopay/shared/utils/timestamp-converter';

const toHistoricalChat = (chat: HistoricalChatDto) => {
  return {
    ...chat,
    createdAt: dateToProtoTimestamp(chat.createdAt),
  };
};

type Reference = Record<string, string>;

interface SchemaInfo {
  ctes: string[];
  time_context: {
    type: 'relative' | 'absolute' | 'none';
    start_date: string;
    end_date: string;
  };
  query_type: 'sales' | 'labor' | 'inventory' | 'menu' | 'cross_domain';
  options: { id: string; name: string }[];
  has_missing_information: boolean;
  missing_information?: { type: string; name: string }[];
  items_to_query: { type: string; name: string }[];
}

interface QueryContext {
  businessId: string;
  userQuery: string;
  timestamp: string;
  schemaInfo?: SchemaInfo;
  decomposition?: any;
  previousSqlQuery?: string;
  sqlQuery?: string;
  validation?: any;
  confidence?: any;
  historicalChat: HistoricalChatDto[];
}

export class CopilotService {
  private readonly logger = new Logger(CopilotService.name);

  @InjectRepository(Menu)
  private readonly menuRepository: Repository<Menu>;

  @InjectRepository(StaffMember)
  private readonly staffMemberRepository: Repository<StaffMember>;

  @InjectRepository(Shift)
  private readonly shiftRepository: Repository<Shift>;

  @InjectRepository(WorkEvent)
  private readonly workEventRepository: Repository<WorkEvent>;

  agents: { [key: string]: Agent };

  constructor() {
    this.agents = {};
  }

  async getMenus(businessId: string) {
    const result = await this.menuRepository.find({
      where: {
        enterprise: {
          businessLinks: {
            business: {
              id: businessId,
            },
          },
        },
      },
      relations: {
        menuCategories: {
          menuCategory: {
            contents: {
              product: true,
            },
          },
        },
      },
    });

    return result;
  }

  async getStaff(businessId: string) {
    const staffMembers = await this.staffMemberRepository.find({
      where: {
        business: {
          id: businessId,
        },
      },
      relations: {
        user: true,
        payRates: {
          role: {
            department: true,
          },
        },
      },
    });

    const dateThreshold = dayjs().subtract(12, 'months').toDate();
    const shifts = await this.shiftRepository.find({
      where: {
        business: {
          id: businessId,
        },
        createdAt: MoreThan(dateThreshold),
      },
      relations: {
        staffMember: true,
      },
    });

    const workEvents = await this.workEventRepository.find({
      where: {
        businessId,
        startTime: MoreThan(dateThreshold),
      },
      relations: {
        staffMember: true,
      },
    });

    const recentStaff = new Set([...shifts.map((shift) => shift.staffMember.id), ...workEvents.map((workEvent) => workEvent.staffMember.id)]);
    const activeStaffMembers = staffMembers.filter((staff) => recentStaff.has(staff.id));

    return activeStaffMembers;
  }

  async getReferences(businessId: string) {
    const fullMenu = await this.getMenus(businessId);
    const { menus, menuCategories, products } = fullMenu.reduce(
      (acc, menu) => {
        // Add the menu
        acc['menus'][menu.id] = menu.nameExternal;
        // Add the menu categories
        menu.menuCategories.forEach((menuCategory) => {
          acc['menuCategories'][menuCategory.menuCategoryId] = menuCategory.menuCategory?.name || '';
          // Add the products
          menuCategory.menuCategory?.contents.forEach((content) => {
            acc['products'][content.productId] = content.product.nameExternal;
          });
        });
        return acc;
      },
      { menus: {}, menuCategories: {}, products: {} } as { menus: Reference; menuCategories: Reference; products: Reference },
    );

    const staffMembers = await this.getStaff(businessId);
    const { staff, roles, departments } = staffMembers.reduce(
      (acc, staffMember) => {
        // Add the staff
        const name = staffMember.user?.firstName ? `${staffMember.user?.firstName} ${staffMember.user?.lastName}` : staffMember.ingestionData?.employeeName || '';
        acc['staff'][staffMember.id] = name;
        staffMember.payRates.forEach((payRate) => {
          // Add the roles
          acc['roles'][payRate.role.id] = payRate.role.name;
          // Add the departments
          acc['departments'][payRate.role.department.id] = payRate.role.department.name;
        });
        return acc;
      },
      { staff: {}, roles: {}, departments: {} } as { staff: Reference; roles: Reference; departments: Reference },
    );

    return {
      menus,
      menuCategories,
      products,
      staff,
      roles,
      departments,
    };
  }

  async queryCopilot(payload: QueryCopilotRequestDto): Promise<QueryCopilotResponse> {
    const context: QueryContext = {
      businessId: payload.businessId,
      userQuery: payload.query,
      timestamp: new Date().toISOString(),
      historicalChat: payload.historicalChat,
    };

    // Get the references
    const { menus, menuCategories, products, staff, roles, departments } = await this.getReferences(payload.businessId);

    // Setup the agents
    this.agents.schemaSelector = new SchemaSelectorAgent(menus, menuCategories, products, staff, roles, departments, payload.metadata || '');
    this.agents.queryDecomposer = new QueryDecomposerAgent();
    this.agents.sqlGenerator = new SQLGeneratorAgent();
    this.agents.validator = new ValidatorAgent();
    this.agents.confidenceScorer = new ConfidenceScorerAgent();

    try {
      // Schema selection and information validation
      const schemaInfo = await this.agents.schemaSelector.execute(context.userQuery, context.businessId, payload.historicalChat);

      // Check for missing information
      if (schemaInfo.has_missing_information) {
        return {
          status: HttpStatus.OK,
          error: undefined,
          response: {
            answer: {
              message: `Which ${schemaInfo.missing_information.type} are you referring to? ${JSON.stringify(schemaInfo.missing_information.options)}`,
              UI: 'question',
              data: undefined,
              UIType: 'follow-up',
              totalTokens: this.agents.schemaSelector.getInputTokens(),
              followUpQuestions: [],
            },
            historicalChat: payload.historicalChat.map(toHistoricalChat),
          },
        };
      }

      context.schemaInfo = schemaInfo;

      console.log({ totalTokensForSchemaSelector: this.agents.schemaSelector.getTotalTokens() });

      // Query decomposition
      const decomposition = await this.agents.queryDecomposer.execute(
        context.userQuery,
        {
          schemaInfo,
          businessId: context.businessId,
        },
        payload.historicalChat,
      );

      context.decomposition = decomposition;

      console.log({ totalTokensForQueryDecomposer: this.agents.queryDecomposer.getTotalTokens() });

      // SQL generation
      const sqlQuery = await this.agents.sqlGenerator.execute(
        context.userQuery,
        {
          steps: decomposition.steps,
          schemaInfo,
          entityMatches: schemaInfo.products_matches || {},
          timeContext: schemaInfo.time_context,
          businessId: context.businessId,
        },
        payload.historicalChat,
      );

      console.log({ totalTokensForSQLGenerator: this.agents.sqlGenerator.getTotalTokens() });

      const cteHeaders = ((context.schemaInfo?.ctes || []) as string[])
        .map((cte) => {
          switch (cte) {
            case 'products':
              return SUB_PRODUCTS_CTE_QUERY;
            case 'tabs':
              return SUB_TABS_CTE_QUERY;
            case 'work_events':
              return SUB_WORK_EVENTS_CTE_QUERY;
            default:
              return null;
          }
        })
        .filter(Boolean);

      context.sqlQuery = cteHeaders.length > 0 ? `WITH ${cteHeaders.join(',\n')}\n\n${sqlQuery.replace(/^\s*WITH\s+/i, '').trim()}` : sqlQuery;

      // Validation
      const validation = await this.agents.validator.execute(context.sqlQuery || '', context, payload.historicalChat);
      context.validation = validation;

      console.log({ totalTokensForValidator: this.agents.validator.getTotalTokens() });

      // Use the suggested query if given
      if (validation.suggested_query) {
        context.previousSqlQuery = context.sqlQuery;
        context.sqlQuery = validation.suggested_query;
      }

      const confidence = await this.agents.confidenceScorer.execute(
        context.userQuery,
        {
          sqlQuery: context.sqlQuery || '',
          schemaInfo,
          validation,
          decomposition,
        },
        payload.historicalChat,
      );
      context.confidence = confidence;

      // Make the final decision
      const shouldExecute = validation.valid && (confidence.overall_confidence > 70 || validation.suggested_query);

      this.logger.log({
        success: shouldExecute,
        query: context.sqlQuery,
        confidence: confidence.overall_confidence,
        recommendation: confidence.recommendation,
        usedSuggestedQuery: !!validation.suggested_query,
        metadata: {
          queryType: schemaInfo.query_type,
          complexity: decomposition.complexity,
          entities: schemaInfo.entities_to_search,
          validation,
        },
      });

      // Check for max tokens
      const totalTokens = Math.max(
        this.agents.schemaSelector.getInputTokens(),
        this.agents.queryDecomposer.getInputTokens(),
        this.agents.sqlGenerator.getInputTokens(),
        this.agents.validator.getInputTokens(),
        this.agents.confidenceScorer.getInputTokens(),
      );

      return {
        status: HttpStatus.OK,
        error: null,
        response: {
          answer: {
            message: '',
            UI: '',
            data: {},
            UIType: '',
            totalTokens,
            followUpQuestions: [],
          },
          historicalChat: [],
        },
      };
    } catch (error) {
      this.logger.error(error);
      return {
        status: HttpStatus.INTERNAL_SERVER_ERROR,
        error: error,
        response: null,
      };
    }
  }
}
