import { HttpStatus, Inject, Injectable } from '@nestjs/common';
import { Logger } from '@tangopay/shared/logger/logger';
import { parseCheckCSV } from './toast/check_detail';
import { GenericResponse } from '../../proto/reporting/shared.pb';
import {
  IngestedMenu,
  IngestedMenuCategory,
  IngestedModifierGroup,
  IngestedModifierOption,
  IngestedProduct,
  MatchableStaffMember,
  MenuTreeCategory,
  MenuTreeData,
  MenuTreeProduct,
  ReportIngestionSearcherService,
} from './searcher.service';
import { Business, Enterprise } from 'apps/business/src/proto/proto/business/business.pb';
import { StaffMember } from 'apps/staffing/src/entities/staff/staff-member.entity';
import { Tab } from 'apps/ordering/src/entities/tabs/tabs.entity';
import { randomBytes, randomUUID } from 'crypto';
import dayjs, { Dayjs } from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { parsePaymentCSV, PaymentDetail } from './toast/payment_details';
import { Payment } from 'apps/ordering/src/entities/payments/payments.entity';
import { DeepPartial } from 'typeorm';
import { extractMenu } from './toast/menu';
import { parseOrderCSV } from './toast/order_details';
import { ItemSelectionDetails, parseItemSelectionCSV } from './toast/item_selection_details';
import { ProductInOrder } from 'apps/ordering/src/entities/orders/products-in-order.entity';
import * as natural from 'natural';
import { VoidedProduct } from 'apps/ordering/src/entities/payments/voided-products.entity';
import { Product } from 'apps/ordering/src/entities/shared/product.entity';
import { TabNotFoundException } from '@tangopay/shared/errors';
import { PaysFor } from 'apps/ordering/src/entities/payments/pays-for.entity';
import { ClockPunch, parseClockPunchesCSV } from './toast/clock_punches';
import { Order } from 'apps/ordering/src/entities/orders/orders.entity';

dayjs.extend(customParseFormat);
dayjs.extend(utc);
dayjs.extend(timezone);

type MenuTree = Map<
  string,
  {
    id: string;
    name: string;
    needsToBeSaved: boolean;
    categories: Map<
      string,
      {
        id: string;
        name: string;
        needsToBeSaved: boolean;
        products: Map<
          string,
          {
            id: string;
            name: string;
            salesCategory: string;
            prices: number[];
            savedPrice?: number;
            needsToBeSaved: boolean;
          }
        >;
      }
    >;
  }
>;

@Injectable()
export class ReportIngestionToastService {
  private readonly logger = new Logger(ReportIngestionToastService.name);

  @Inject(ReportIngestionSearcherService)
  private readonly searcherService: ReportIngestionSearcherService;

  private async extractStaffMembers(business: Business, details: { serverName: string }[]) {
    const uniqueServers = new Set(
      details.map((d) => {
        if (!d.serverName) console.log(d);
        return d.serverName;
      }),
    );

    const serversMap = new Map<string, StaffMember>();

    const staffMembers = await this.searcherService.matchStaffMembers(
      business,
      Array.from(uniqueServers).map((s) => ({
        name: s,
        externalId: s,
        externalIdLabel: 'toastServerName',
      })),
    );

    for (const staffMember of staffMembers) {
      serversMap.set(staffMember.ingestionData?.employeeName, staffMember);
    }

    return serversMap;
  }

  /**
   * Order details directly correspond to tabs.
   */
  public async ingestOrderDetails(csv: string, business: Business): Promise<GenericResponse> {
    const orderDetails = parseOrderCSV(csv);

    const serversMap = await this.extractStaffMembers(business, orderDetails);

    const tabs = await Promise.all(
      orderDetails.map((d, i) => {
        const openedAt = dayjs.tz(d.opened, 'M/D/YY h:mm A', business.timezone).utc();
        const paidAt = d.paid ? dayjs.tz(d.paid, 'M/D/YY h:mm A', business.timezone).utc() : null;
        const closedAt = d.closed ? dayjs.tz(d.closed, 'M/D/YY h:mm A', business.timezone).utc() : null;
        const allSpend = d.total + d.tax + d.discount + d.tip + d.gratuity;
        // if the date is invalid or the total spend is 0, skip the tab
        if (!openedAt.isValid() || (paidAt && !paidAt.isValid())) {
          console.log(`Skipping tab ${d.orderId} (${d.orderNumber}) because the date is invalid or the total spend is 0`);
          return null;
        }
        const data: Partial<Tab> = {
          completed: true,
          name: d.table ? `Table #${d.table}` : `Order #${d.orderNumber}`,
          numberOfGuests: d.noOfGuests,
          tableNumber: d.table,
          paymentComplete: true,
          status: 'completed',
          tabNumber: randomBytes(5).toString('hex').toUpperCase(),
          checkNumber: d.orderNumber,
          orderType: d.diningOption.includes('Dine In') ? 'dineIn' : d.diningOption.includes('Take') ? 'carryOut' : 'delivery',
          createdAt: openedAt.toDate(),
          completedAt: closedAt?.toDate(),
          paidAt: paidAt?.toDate(),
          ingestionData: {
            toastOrderId: d.orderId,
            toastTaxAmount: d.tax,
          },
        };

        return this.searcherService.findOrCreateTab(business, d.orderId, data, serversMap.get(d.serverName));
      }),
    );

    return {
      status: HttpStatus.OK,
      error: null,
    };
  }

  private typeToPaymentType(type: string) {
    if (type === 'Credit') {
      return 'stripe';
    }
    if (type === 'Cash') {
      return 'cash';
    }
    if (type === 'Gift Card') {
      return 'giftCard';
    }
    if (type === 'Other') {
      return 'external';
    }
  }

  public async ingestPaymentDetails(csv: string, business: Business): Promise<GenericResponse> {
    const paymentDetails = parsePaymentCSV(csv);

    const serversMap = await this.extractStaffMembers(business, paymentDetails);

    const detailsByOrderId = new Map<string, PaymentDetail[]>();
    paymentDetails.forEach((d) => {
      if (!detailsByOrderId.has(d.orderId)) {
        detailsByOrderId.set(d.orderId, []);
      }
      detailsByOrderId.get(d.orderId)?.push(d);
    });

    const allTabs = await this.searcherService.findTabsByIngestedIds(business, [...detailsByOrderId.keys()]);

    if (allTabs.size > 0) await this.searcherService.deletePaymentsForTabs([...allTabs.values()].map((t) => t.id));

    const tabs = await Promise.all(
      [...detailsByOrderId.keys()].map(async (orderId) => {
        const payments = [...(detailsByOrderId.get(orderId) ?? [])].filter((d) => d.status !== 'OPEN' && d.status !== 'VOIDED');
        // if the payment is open, the it did not clear
        if (payments.length === 0) return [];
        const tab = allTabs.get(orderId);
        if (!tab) {
          console.log(`Tab not found for orderId: ${orderId}`);
          return null;
        }
        const postTaxTotal = payments.reduce((acc, curr) => acc + curr.total, 0);
        let lastPaymentDate: dayjs.Dayjs | null = null;
        let checkId: string | null = null;
        for (const payment of payments) {
          const shareOfTax = (payment.total / postTaxTotal) * (tab.ingestionData?.toastTaxAmount ?? 0);
          const paymentDate = dayjs.tz(payment.paidDate, 'M/D/YY h:mm A', business.timezone).utc();
          if (!lastPaymentDate || paymentDate.isAfter(lastPaymentDate)) {
            lastPaymentDate = paymentDate;
          }
          if (!checkId) {
            checkId = payment.checkId;
          }
          const taxAmount = shareOfTax;
          const tipAmount = payment.tip;
          const gratuityAmount = payment.gratuity;
          const subTotalAmount = payment.total - taxAmount - tipAmount - gratuityAmount;
          const refundedAmount = payment.refundAmount ?? 0;
          const processingFee = payment.issuerFees ?? 0;
          const data: DeepPartial<Payment> = {
            amount: {
              currency: business.currency,
              deliveryFee: 0,
              netTotal: payment.total - tipAmount,
              tax: taxAmount,
              tip: tipAmount,
              refundedAmount: refundedAmount,
              processingFee: processingFee,
            },
            subTotalAmount: subTotalAmount,
            taxAmount: taxAmount,
            taxBreakdown: [],
            exemptTaxAmount: 0,
            exemptTaxBreakdown: [],
            serviceFeeAmount: 0,
            serviceFeeBreakdown: [
              {
                id: '05050505-0000-0000-0000-************',
                amount: payment.gratuity,
                name: 'Service Fee',
                toEmployee: true,
              },
            ],
            tipAmount: tipAmount,
            refundedAmount: refundedAmount,
            tipIsUnset: false,
            feeRemaining: 0,
            feesPaid: payment.gratuity,
            percentageFeeOnTip: 0,
            type: this.typeToPaymentType(payment.type),
            isComplete: true,
            paysForSeats: [],
            createdAt: paymentDate.toDate(),
            ingestionData: {
              toastPaymentId: payment.paymentId,
              toastCheckId: payment.checkId,
            },
            stripePayload:
              payment.type === 'Credit'
                ? {
                    amount: payment.total,
                    applicationFeeAmount: payment.issuerFees,
                    authorizationCode: '',
                    brand: payment.cardType.toLowerCase(),
                    chargeId: '',
                    clientSecret: '',
                    country: business.country,
                    created: '',
                    currency: business.currency,
                    customerId: '',
                    fingerprint: '',
                    funding: '',
                    isInteracPayment: false,
                    lastFour: payment.last4,
                    readMethod: '',
                    stripeId: '',
                  }
                : null,
            cashPayload:
              payment.type === 'Cash'
                ? {
                    amount: payment.total,
                    change: 0,
                    drawerId: '',
                  }
                : null,
            giftCardPayload:
              payment.type === 'Gift Card'
                ? {
                    amount: payment.total,
                    giftCardNumber: '',
                  }
                : null,
            preAuthPayload: null,
            refunded: payment.refunded !== 'NONE',
            deleted: false,
            stripeSources:
              payment.type === 'Credit'
                ? [
                    {
                      amount: payment.total,
                      feeAmount: payment.issuerFees ?? 0,
                      amountRefunded: payment.refundAmount ?? 0,
                      reverted: false,
                      stripeId: '',
                      lastFour: payment.last4,
                      cardholderName: '',
                      brand: payment.cardType.toLowerCase(),
                      uncaptured: false,
                      incrementalAuthSupport: false,
                      customerId: '',
                      generatedCardId: '',
                      cardType: 'credit',
                      interac: false,
                      authorizationCode: '',
                      onlinePayment: false,
                    },
                  ]
                : [],
            cashSources:
              payment.type === 'Cash'
                ? [
                    {
                      amount: payment.total,
                      cashReceived: payment.total,
                      changeReturned: 0,
                      amountRefunded: payment.refundAmount ?? 0,
                      reverted: false,
                    },
                  ]
                : [],
            giftCardSources:
              payment.type === 'Gift Card'
                ? [
                    {
                      amount: payment.total,
                      giftCardNumber: '',
                      transactionId: '',
                    },
                  ]
                : [],
            lossSources: [],
            externalSources:
              payment.type === 'Other'
                ? [
                    {
                      amount: payment.amount,
                      type: payment.externalType.toLocaleLowerCase(),
                    },
                  ]
                : [],
          };

          const staffMember = serversMap.get(payment.serverName);
          await this.searcherService.findOrCreatePayment(business, payment.paymentId, data, tab, staffMember);
        }

        await this.searcherService.updateTab(tab, {
          paidAt: !tab.completedAt || lastPaymentDate.isAfter(tab.completedAt) ? lastPaymentDate.toDate() : tab.paidAt,
          ingestionData: {
            ...tab.ingestionData,
            toastCheckId: [...(tab.ingestionData?.toastCheckId ?? []), checkId],
          },
        });
      }),
    );

    return {
      status: HttpStatus.OK,
      error: null,
    };
  }

  /**
   * Check details pseudo-correspond to payments.
   */
  public async ingestCheckDetail(csv: string, business: Business): Promise<GenericResponse> {
    const checkDetails = parseCheckCSV(csv);

    const serversMap = await this.extractStaffMembers(business, checkDetails);

    const customers = await Promise.all(
      checkDetails.map((d) =>
        (d.customerPhone || d.customerEmail) && d.customerName
          ? this.searcherService.findOrCreateCustomer(business, {
              name: d.customerName,
              phoneNumber: d.customerPhone,
              email: d.customerEmail,
            })
          : null,
      ),
    );

    const tabs = await Promise.all(
      checkDetails.map(async (d, i) => {
        const customer = customers[i];
        if (!customer) return;
        const tab = await this.searcherService.findTabByIngestionData(business, d.checkId, 'toastCheckId');
        if (!tab) {
          return null;
        }

        return this.searcherService.findOrCreateTab(
          business,
          tab.ingestedId,
          {
            customer: {
              name: `${customer.firstName} ${customer.lastName}`,
              phoneNumber: customer.phoneNumber,
              email: customer.email,
              allergies: [],
            },
          },
          serversMap.get(d.serverName),
          customer,
        );
      }),
    );

    return {
      status: HttpStatus.OK,
      error: null,
    };
  }

  public async ingestMenu(json: string, enterprise: Enterprise): Promise<GenericResponse> {
    const { menus } = extractMenu(json);

    const modifierOptions = new Map<string, IngestedModifierOption>();
    const modifierGroups = new Map<string, IngestedModifierGroup>();
    const products = new Map<string, IngestedProduct>();
    const categories = new Map<string, IngestedMenuCategory>();
    const ingestedMenus = new Map<string, IngestedMenu>();

    menus.forEach((m) => {
      const flattenedGroups = m.groups.flatMap((g) => [
        g,
        ...g.subgroups.map((sg) => {
          sg.name = `${g.name} - ${sg.name}`;
          return sg;
        }),
      ]);
      // console.log(m.name);
      const categoryRefIds = flattenedGroups.map((g) => {
        const productRefIds = g.items.map((i) => {
          const groupRefId = i.optionGroups
            .map((og) => {
              // if the group has at least one item with a price, it is an add-on group
              const groupAdmissible = og.items.some((o) => o.price > 0);
              if (!groupAdmissible) return;
              const modItemReferenceIds = og.items.map((o) => {
                const key = `${o.name}-${o.price}`;
                const existing = modifierOptions.get(key);
                if (existing) {
                  if (!existing.ingestedIds.includes(o.guid)) existing.ingestedIds.push(o.guid);
                  return existing.refId;
                } else {
                  const refId = `${randomUUID()}`;
                  modifierOptions.set(key, {
                    refId,
                    name: o.name,
                    price: Math.round(o.price * 100),
                    plu: o.plu ?? '',
                    ingestedIds: [o.guid],
                    ingestedIdsLabel: 'toastModifierOptionGuid',
                  });
                  return refId;
                }
              });
              const existing = modifierGroups.get(og.guid);
              if (existing) {
                return existing.refId;
              }

              const newGroup = {
                refId: `${randomUUID()}`,
                name: og.name,
                modifierOptionRefIds: modItemReferenceIds,
                ingestedId: og.guid,
                ingestedIdLabel: 'toastModifierGroupGuid',
                min: og.minSelections,
                max: og.maxSelections ?? 0,
              };
              modifierGroups.set(og.guid, newGroup);
              return newGroup.refId;
            })
            .filter((refId) => !!refId);

          const existing = products.get(i.guid);
          if (existing) return existing.refId;

          const product: IngestedProduct = {
            refId: `${randomUUID()}`,
            name: i.name,
            price: Math.round(i.price * 100),
            description: i.description,
            ingestedId: i.guid,
            ingestedIdLabel: 'toastItemGuid',
            modifierGroupsRefIds: groupRefId,
            plu: i.plu ?? '',
          };

          products.set(i.guid, product);

          return product.refId;
        });

        const existing = categories.get(g.guid);
        if (existing) return existing.refId;

        const category: IngestedMenuCategory = {
          refId: `${randomUUID()}`,
          name: g.name,
          ingestedId: g.guid,
          ingestedIdLabel: 'toastMenuCategoryGuid',
          productsRefIds: productRefIds,
        };

        categories.set(g.guid, category);

        return category.refId;
      });

      const existing = ingestedMenus.get(m.guid);
      if (existing) return existing.refId;

      const menu: IngestedMenu = {
        refId: `${randomUUID()}`,
        name: m.name,
        ingestedId: m.guid,
        ingestedIdLabel: 'toastMenuGuid',
        categoriesRefIds: categoryRefIds,
      };

      ingestedMenus.set(m.guid, menu);
    });

    const savedModOptsByRefId = await this.searcherService.findOrCreateModifierOptions(enterprise, Array.from(modifierOptions.values()));
    const savedModGroupsByRefId = await this.searcherService.findOrCreateModifierGroups(enterprise, Array.from(modifierGroups.values()).flat(), savedModOptsByRefId);
    const savedProductsByRefId = await this.searcherService.findOrCreateProduct(enterprise, Array.from(products.values()), savedModGroupsByRefId);
    const savedCategoriesByRefId = await this.searcherService.findOrCreateMenuCategories(enterprise, Array.from(categories.values()), savedProductsByRefId);
    const savedMenusByRefId = await this.searcherService.findOrCreateMenu(enterprise, Array.from(ingestedMenus.values()), savedCategoriesByRefId);

    return {
      status: HttpStatus.OK,
      error: null,
    };
  }

  private itemSelectionDetailToProductInOrderPartial(d: ItemSelectionDetails, menuTree: MenuTree, debug = false): DeepPartial<Product> {
    const menu = menuTree.get(d.menu);
    const joinedCategoryName = d.menuSubgroupName === '' ? d.menuGroupName : `${d.menuGroupName} - ${d.menuSubgroupName}`;
    const category = menu?.categories.get(joinedCategoryName);
    const product = category?.products.get(d.menuItemName);

    const quantity = d.quantity > 0 ? d.quantity : 1;

    const discount = d.grossPrice - d.netPrice;

    return {
      id: d.refId,
      discountsAppliedToProduct:
        discount > 0
          ? [
              {
                amount: discount,
                discountId: '********-0000-0000-0000-************',
                discountName: 'Toast Discount',
              },
            ]
          : [],
      menuCategory: joinedCategoryName,
      categoryId: category?.id ?? '02020202-0000-0000-0000-************',
      menuName: d.menu ?? 'Unknown',
      menuId: menu?.id ?? '03030303-0000-0000-0000-************',
      name: d.menuItemName ?? d.menuItemName,
      nameExternal: d.menuItemName ?? d.menuItemName,
      productId: product?.id ?? '04040404-0000-0000-0000-************',
      productOrderId: `${quantity}-${d.refId}`,
      quantity,
      selectedModifiers: [],
      sentQuantity: quantity,
      status: 'completed',
      type: d.salesCategory,
      label: d.salesCategory as any,
      taxBreakdown: [
        {
          amount: d.tax,
          id: '********-0000-0000-0000-************',
          name: 'Toast Tax',
        },
      ],
      serviceFeeAmount: 0,
      serviceFeeTaxAmount: 0,
      serviceFeeBreakdown: [],
      ingested: true,
      _fPrice: d.grossPrice / quantity,
      _fDiscountedPrice: d.netPrice / quantity,
      _fSubTotalAmount: d.netPrice,
      _fTaxAmount: d.tax,
    };
  }

  private async treeShakeItemSelectionDetails(enterprise: Enterprise, itemSelectionDetails: ItemSelectionDetails[]): Promise<MenuTree> {
    const menuTree: MenuTree = new Map();

    for (const d of itemSelectionDetails) {
      if (d.menu === '') {
        continue;
      }
      let menu = menuTree.get(d.menu);

      if (!menu) {
        const existingInDB = await this.searcherService.findMenuByName(enterprise, d.menu);
        if (existingInDB) {
          menu = {
            id: existingInDB.id,
            name: existingInDB.nameExternal,
            needsToBeSaved: false,
            categories: new Map(),
          };
        } else {
          menu = {
            id: randomUUID(),
            name: d.menu,
            needsToBeSaved: true,
            categories: new Map(),
          };
        }
        menuTree.set(d.menu, menu);
      }
      const joinedCategoryName = d.menuSubgroupName === '' ? d.menuGroupName : `${d.menuGroupName} - ${d.menuSubgroupName}`;

      let category = menu.categories.get(joinedCategoryName);
      if (!category) {
        const existingInDB = (await this.searcherService.findMenuCategoryByName(menu.id, joinedCategoryName))?.menuCategories.find(
          (c) => c.menuCategory.name === joinedCategoryName,
        )?.menuCategory;
        if (existingInDB) {
          category = {
            id: existingInDB.id,
            name: existingInDB.name,
            needsToBeSaved: false,
            products: new Map(),
          };
        } else {
          category = {
            id: randomUUID(),
            name: joinedCategoryName,
            needsToBeSaved: true,
            products: new Map(),
          };
        }
        menu.categories.set(joinedCategoryName, category);
      }

      let product = category.products.get(d.menuItemName);
      if (!product) {
        const existingInDB = await this.searcherService.findProductByName(enterprise, d.menuItemName);
        if (existingInDB) {
          product = {
            id: existingInDB.id,
            name: existingInDB.nameExternal,
            prices: [existingInDB.price],
            savedPrice: existingInDB.price,
            salesCategory: d.salesCategory,
            needsToBeSaved: false,
          };
        } else {
          product = {
            id: randomUUID(),
            name: d.menuItemName,
            prices: [],
            salesCategory: d.salesCategory,
            needsToBeSaved: true,
          };
        }
        category.products.set(d.menuItemName, product);

        const price = d.grossPrice / d.quantity;
        product.prices.push(price);
      }
    }

    return menuTree;
  }

  private async saveMenuTree(enterprise: Enterprise, menuTree: MenuTree) {
    for (const menu of menuTree.values()) {
      let categoryIds: string[] = [];
      let createdCategoryIds: string[] = [];
      for (const category of menu.categories.values()) {
        let productIds: string[] = [];
        let createdProductIds: string[] = [];
        for (const product of category.products.values()) {
          if (!product.needsToBeSaved) {
            productIds.push(product.id);
          } else {
            const largestPrice = Math.max(...product.prices);
            await this.searcherService.createProduct(enterprise, {
              id: product.id,
              ingestedId: product.name,
              ingestedIdLabel: 'toastProductGuid',
              modifierGroupsRefIds: [],
              name: product.name,
              price: largestPrice,
              type: 'product',
            });
            createdProductIds.push(product.id);
            productIds.push(product.id);
          }
        }
        if (!category.needsToBeSaved) {
          if (createdProductIds.length > 0) {
            await this.searcherService.linkProductsToMenuCategory(category.id, createdProductIds);
          }
          categoryIds.push(category.id);
        } else {
          await this.searcherService.createMenuCategory(enterprise, {
            id: category.id,
            ingestedId: category.name,
            productsRefIds: productIds,
            name: category.name,
          });
          createdCategoryIds.push(category.id);
          categoryIds.push(category.id);
        }
      }

      if (menu.needsToBeSaved) {
        await this.searcherService.createMenu(enterprise, {
          id: menu.id,
          ingestedId: menu.name,
          categoriesIds: categoryIds,
          name: menu.name,
        });
      } else if (createdCategoryIds.length > 0) {
        await this.searcherService.linkCategoriesToMenu(menu.id, createdCategoryIds);
      }
    }
  }

  public async ingestItemSelectionDetails(csv: string, business: Business): Promise<GenericResponse> {
    const itemSelectionDetails = parseItemSelectionCSV(csv);
    if (itemSelectionDetails.length === 0) {
      return {
        status: HttpStatus.OK,
        error: null,
      };
    }

    // const grossTotal = itemSelectionDetails.filter((d) => !d.voided).reduce((acc, curr) => acc + curr.grossPrice, 0);
    // const netTotal = itemSelectionDetails.filter((d) => !d.voided).reduce((acc, curr) => acc + curr.netPrice, 0);
    // const taxTotal = itemSelectionDetails.filter((d) => !d.voided).reduce((acc, curr) => acc + curr.tax, 0);
    // const totalDiscount = grossTotal - netTotal;

    // this.logger.log(`Gross total: ${grossTotal}, Net total: ${netTotal}, Tax total: ${taxTotal}, Total discount: ${totalDiscount}`);

    const [earliestDateUnadjusted, latestDateUnadjusted] = itemSelectionDetails.reduce(
      (acc, curr) => {
        const sentAt = dayjs.tz(curr.sentAt, 'M/D/YY h:mm A', business.timezone).utc();
        const min = acc[0] ? (sentAt.isBefore(acc[0]) ? sentAt : acc[0]) : sentAt;
        const max = acc[1] ? (sentAt.isAfter(acc[1]) ? sentAt : acc[1]) : sentAt;
        return [min, max];
      },
      [null, null],
    );

    const earliestDate = earliestDateUnadjusted.subtract(1, 'day');
    const latestDate = latestDateUnadjusted.add(1, 'day');

    const foodAndBevEnterprise = business.enterprises.find((e) => e.foodAndBev);

    const menuTree = await this.treeShakeItemSelectionDetails(foodAndBevEnterprise, itemSelectionDetails);

    await this.saveMenuTree(foodAndBevEnterprise, menuTree);

    // group products by orderId/SentDate
    const orderIdSentDateMap = new Map<string, ItemSelectionDetails[]>();

    // group products by check ID
    const checkIdMap = new Map<string, ItemSelectionDetails[]>();

    const orderIds = new Set<string>();
    for (const d of itemSelectionDetails) {
      orderIdSentDateMap.set(`${d.orderId}-${d.sentAt}`, [...(orderIdSentDateMap.get(`${d.orderId}-${d.sentAt}`) ?? []), d]);
      checkIdMap.set(d.checkId, [...(checkIdMap.get(d.checkId) ?? []), d]);
      orderIds.add(d.orderId);
    }

    const tabs = await this.searcherService.getTabsInDateRange(business, earliestDate.toDate(), latestDate.toDate());

    const orderMap = new Map<string, Order>([...tabs.values()].flatMap((t) => t.orders.map((o) => [o.ingestedId, o])));

    // this.searcherService.findTabsByIngestedIds(business, Array.from(orderIds));

    const ignoredProducts: string[] = [];

    const tabsByTabId = new Map<string, Tab>();
    const productsByTabId = new Map<string, (DeepPartial<Product> & { voided: boolean; orderedAt: Date })[]>();

    const ordersByOrderIdSentDateMap = new Map<string, Order>();

    const ordersToSave = [...orderIdSentDateMap.keys()].map((orderIngestedId): DeepPartial<Order> | null => {
      const orderedProducts = orderIdSentDateMap.get(orderIngestedId);
      const amounts = orderedProducts.reduce(
        (acc, curr) => {
          acc.netTotal += curr.tax + curr.netPrice;
          acc.tax += curr.tax;
          return acc;
        },
        {
          currency: business.currency,
          netTotal: 0,
          tax: 0,
          tip: 0,
          refundedAmount: 0,
          processingFee: 0,
        },
      );
      const orderId = orderedProducts[0].orderId;
      const tab = tabs.get(orderId);

      if (!tab) {
        this.logger.warn(`Tab ${orderId} not found for order ${orderIngestedId}`);
        return null;
      }

      const orderType = orderedProducts[0].diningOption.includes('Dine In') ? 'dineIn' : orderedProducts[0].diningOption.includes('Take') ? 'carryOut' : 'delivery';
      const sentAt = dayjs.tz(orderedProducts[0].sentAt, 'M/D/YY h:mm A', business.timezone).utc();
      return {
        ingested: true,
        createdAt: sentAt.toDate(),
        updatedAt: sentAt.toDate(),
        ingestionData: {
          toastTangoOrderId: orderIngestedId,
        },
        amounts,
        orderApp: 'toast',
        orderChannel: 'toast',
        orderType,
        ingestedId: orderIngestedId,
        tab: {
          id: tab.id,
        },
      };
    });

    const savedOrders = await this.searcherService.findOrCreateOrders(
      business,
      ordersToSave.filter((o) => !!o),
      orderMap,
    );

    let orderIdsToDeleteProductsFor = new Set<string>();
    let tabIdsToDeleteVoidedProductsFor = new Set<string>();
    for (const order of savedOrders) {
      ordersByOrderIdSentDateMap.set(order.ingestedId, order);
      orderIdsToDeleteProductsFor.add(order.id);
      tabIdsToDeleteVoidedProductsFor.add(order.tab.id);
    }

    const tabIdsToDelete = Array.from(tabIdsToDeleteVoidedProductsFor.values());
    await this.searcherService.deleteAllProductsInOrderForOrderIds(tabIdsToDelete, Array.from(orderIdsToDeleteProductsFor.values()));
    await this.searcherService.deleteAllVoidedProductsForTab(tabIdsToDelete);

    await Promise.all(
      [...orderIdSentDateMap.keys()].map(async (orderIngestedId) => {
        const orderedProducts = orderIdSentDateMap.get(orderIngestedId);
        const amounts = orderedProducts.reduce(
          (acc, curr) => {
            acc.netTotal += curr.tax + curr.netPrice;
            acc.tax += curr.tax;
            return acc;
          },
          {
            currency: business.currency,
            netTotal: 0,
            tax: 0,
            tip: 0,
            refundedAmount: 0,
            processingFee: 0,
          },
        );
        const orderId = orderedProducts[0].orderId;
        const tab = tabs.get(orderId);

        if (!tab) {
          this.logger.warn(`Tab ${orderId} not found for order ${orderIngestedId}`);
          return null;
        }

        const upsertedOrder = ordersByOrderIdSentDateMap.get(orderIngestedId);

        if (!upsertedOrder) {
          this.logger.warn(`Order ${orderIngestedId} not found for order ${orderIngestedId}`);
          return null;
        }

        const sentAt = dayjs.tz(orderedProducts[0].sentAt, 'M/D/YY h:mm A', business.timezone).utc();

        const [ordered, voided] = orderedProducts.reduce(
          (acc, curr) => {
            if (curr.deferred) {
              this.logger.warn(`Deferred product ${curr.refId} found for order ${orderIngestedId}. Skipping deferred products.`);
              return acc;
            }
            const productPartial = this.itemSelectionDetailToProductInOrderPartial(curr, menuTree);
            if (!productPartial) ignoredProducts.push(curr.refId);
            else {
              if (curr.voided) acc[1].push(productPartial);
              else acc[0].push(productPartial);
            }
            return acc;
          },
          [[], []],
        );

        const upsertedProducts = await this.searcherService.createProductsInOrder(
          business,
          ordered.filter((p) => !!p),
          upsertedOrder,
        );
        const upsertedVoidedProducts = await this.searcherService.createVoidedProducts(
          voided.filter((p) => !!p),
          tab,
        );

        productsByTabId.set(tab.id, [
          ...(productsByTabId.get(tab.id) ?? []),
          ...ordered.map((o) => ({ ...o, voided: false, orderedAt: sentAt.toDate() })),
          ...upsertedVoidedProducts.map((v) => ({ ...v, voided: true, orderedAt: sentAt.toDate() })),
        ]);

        tabsByTabId.set(tab.id, tab);
      }),
    );

    await this.searcherService.deleteAllReportingProductsForTabs(Array.from(tabIdsToDeleteVoidedProductsFor.values()));

    // let totalDiscountFromReportingProducts = 0;
    // Construct reporting products
    await Promise.all(
      [...productsByTabId.keys()].map(async (tabId) => {
        const products = productsByTabId.get(tabId);
        const tab = tabsByTabId.get(tabId);
        // products.forEach((p) => {
        //   if (!p.voided && p.discountsAppliedToProduct.length > 0) totalDiscountFromReportingProducts += p.discountsAppliedToProduct[0].amount;
        // });
        await this.searcherService.findOrCreateReportingProduct(products, tab, business, tab.staff);
      }),
    );

    // console.log('total discount from reporting products', totalDiscountFromReportingProducts);

    const paymentsByCheckId = await this.searcherService.findPaymentsInDateRange(business, earliestDate.toDate(), latestDate.toDate());

    const paysFor = await Promise.all(
      [...checkIdMap.keys()].map(async (checkId) => {
        const checkDetails = checkIdMap.get(checkId);
        const unvoidedProducts = checkDetails.filter((d) => !d.voided && !d.deferred);
        const totalOnProducts = unvoidedProducts.reduce((acc, curr) => acc + curr.netPrice, 0);
        const totalTaxOnProducts = unvoidedProducts.reduce((acc, curr) => acc + curr.tax, 0);

        // if the total on products and the total tax on products is 0, we can skip this check
        if (totalOnProducts + totalTaxOnProducts === 0) {
          console.log('total on products and total tax on products is 0 skipping check', checkId);
          return null;
        }

        const payments = paymentsByCheckId.get(checkId);
        if (!payments) {
          this.logger.warn(`No payments found for check ${checkId}. Unexpected null result.`);
          return null;
        }
        if (payments.length === 0) {
          this.logger.warn(
            `No payments found for check ${checkId}. For catering orders placed in the past, payments are sometimes registered in the past of the order/check.`,
          );
          return null;
        }

        const paymentsTotal = payments.reduce((acc, curr) => acc + curr.subTotalAmount + curr.taxAmount, 0);

        const totalOnAllPayments = paymentsTotal;

        if (totalOnProducts + totalTaxOnProducts !== totalOnAllPayments) {
          if (checkDetails[0].diningOption.toLocaleLowerCase().includes('delivery') && payments.length === 1) {
            const payment = payments[0];
            const delta = totalOnAllPayments - (totalOnProducts + totalTaxOnProducts);
            // if the delta is positive and less than $10, we can assume the difference is a delivery fee
            if (delta > 0 && delta < 1000) {
              this.logger.log(
                `Updating delivery fee for order ${checkDetails[0].orderId} to ${delta} on basis its a delivery order with a price discrepancy of ${delta}`,
              );
              await this.searcherService.updatePayment(payment, {
                amount: {
                  ...payment.amount,
                  deliveryFee: delta,
                },
              });
            }
          } else {
            const delta = totalOnAllPayments - (totalOnProducts + totalTaxOnProducts);
            if (Math.abs(delta) > 10) {
              this.logger.warn(`Total on products and total on payments mismatch for check ${checkId} by ${delta}.`);
              // this.logger.debug(checkDetails[0].orderId);
              // this.logger.debug('Total of Products', totalOnProducts + totalTaxOnProducts);
              // this.logger.debug('Total of Payment', totalOnAllPayments);
              // this.logger.debug(checkDetails);
              // this.logger.debug(payments);
            }
          }
        }

        await Promise.all(
          payments.map(async (p) => {
            const totalForPayment = p.amount.netTotal;
            // get the fraction of the total that this payment represents
            // This is better than fraction of total products, because it accounts for the fact that some products are ignored
            const ratio = totalForPayment / totalOnAllPayments;

            const productsPaysFor = unvoidedProducts
              .filter((p) => !ignoredProducts.includes(p.refId))
              .map(
                (p): DeepPartial<PaysFor> => ({
                  orderedProduct: {
                    id: p.refId,
                  },
                  subTotalPaid: (p.netPrice - p.tax) * ratio,
                  taxTotalPaid: p.tax * ratio,
                  taxTotalExempt: 0,
                  taxBreakdown: [
                    {
                      amount: p.tax * ratio,
                      id: '********-0000-0000-0000-************',
                      name: 'Toast Tax',
                    },
                  ],
                  exemptTaxBreakdown: [],
                  serviceFeeTotalPaid: 0,
                  serviceFeeTotalExempt: 0,
                  serviceFeeBreakdown: [],
                  totalPaid: p.netPrice * ratio,
                  quantity: p.quantity,
                  ingested: true,
                }),
              );

            try {
              return await this.searcherService.findOrCreatePaysFor(p, productsPaysFor);
            } catch (error) {
              this.logger.error(error);
              console.log('failed to ingest pays for', unvoidedProducts);
              return null;
            }
          }),
        );
      }),
    );

    this.logger.log(`Ingested all items for business ${business.id}`);

    return {
      status: HttpStatus.OK,
      error: null,
    };
  }

  public async ingestClockPunches(csv: string, business: Business): Promise<GenericResponse> {
    const clockPunches = parseClockPunchesCSV(csv);

    const enterprise = business.enterprises.find((e) => e.staffing) ?? business.enterprises.find((e) => e.foodAndBev);

    const roles = await this.searcherService.matchRoles(enterprise, [...new Set(clockPunches.map((c) => c.jobTitle))]);

    const uniqueStaffMembers = new Map<string, MatchableStaffMember>();

    for (const c of clockPunches) {
      if (uniqueStaffMembers.has(c.employeeId)) continue;
      uniqueStaffMembers.set(c.employeeId, {
        name: c.employeeName,
        externalId: c.employeeId,
        externalIdLabel: 'toastEmployeeId',
      });
    }

    const staffMembers = await this.searcherService.matchStaffMembers(business, Array.from(uniqueStaffMembers.values()));

    const clockPunchesByEmployeeId = new Map<string, ClockPunch[]>();

    for (const c of clockPunches) {
      clockPunchesByEmployeeId.set(c.employeeId, [...(clockPunchesByEmployeeId.get(c.employeeId) ?? []), c]);
    }

    for (const employeeId of clockPunchesByEmployeeId.keys()) {
      const punches = clockPunchesByEmployeeId.get(employeeId);
      const staffMember = staffMembers.find((s) => s.ingestionData?.toastEmployeeId === employeeId);
      if (!staffMember) {
        this.logger.warn(`Staff member ${employeeId} not found for employee ${employeeId}`);
        continue;
      }
      for (const punch of punches) {
        const role = roles.find((r) => r.ingestedId === punch.jobTitle);
        if (!role) {
          this.logger.warn(`Role ${punch.jobTitle} not found for employee ${employeeId}`);
          continue;
        }

        if (punch.totalHours === 0) {
          this.logger.warn(`Total hours for punch ${punch.id} is 0 for employee ${employeeId}`);
          continue;
        }

        const activeShift = punch.outDate === '';

        const startTime = dayjs.tz(punch.inDate, 'M/D/YY h:mm A', business.timezone).utc();
        // If the out date is empty, we need to set the end time to the current time
        const endTime = !activeShift ? dayjs.tz(punch.outDate, 'M/D/YY h:mm A', business.timezone).utc() : dayjs();

        const regularMinutes = activeShift ? Math.round(punch.regularHours * 60) : endTime.diff(startTime, 'minutes');
        const overtimeMinutes = activeShift ? Math.round(punch.overtimeHours * 60) : 0;
        const unpaidMinutes = activeShift ? Math.round(punch.unpaidBreakHours * 60) : 0;

        const rate = punch.wage;
        const adjustedRate = activeShift ? Math.round(punch.totalPay / punch.totalHours) : rate;

        const overtimeRate = punch.overtimeHours > 0 ? Math.round(punch.overtimePay / punch.overtimeHours) : undefined;

        if (isNaN(rate)) {
          this.logger.warn(`Pay rate for punch ${punch.id} is NaN for employee ${employeeId}`);
          continue;
        }

        const payRate = await this.searcherService.matchPayRates(staffMember, role, rate, adjustedRate, overtimeRate);

        await this.searcherService.findOrCreateWorkEvent(
          business,
          staffMember,
          payRate,
          {
            startTime: startTime.toDate(),
            endTime: endTime.toDate(),
            regularMinutes,
            overtimeMinutes,
            unpaidMinutes,
          },
          punch.shiftGuid,
        );
      }
    }

    return {
      status: HttpStatus.OK,
      error: null,
    };
  }
}
