import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { Role } from 'apps/business/src/entities/enterprise/role.entity';
import { Business, Enterprise } from 'apps/business/src/proto/proto/business/business.pb';
import { StaffMember } from 'apps/staffing/src/entities/staff/staff-member.entity';
import { Between, DataSource, IsNull, In, LessThan, MoreThan, Not, Repository, DeepPartial } from 'typeorm';
import * as natural from 'natural';
import { Department } from 'apps/business/src/entities/enterprise/department.entity';
import { PayRate } from 'apps/staffing/src/entities/payroll/pay-rate.entity';
import { IngestedLabourReportPunch } from './maitred/labour_report';
import { WorkEvent } from 'apps/staffing/src/entities/payroll/work-event.entity';
import { DeliveryCustomer } from 'apps/business/src/entities/business/customer.entity';
import { Inject } from '@nestjs/common';
import { BusinessClient } from '@tangopay/shared/clients/businessClient.client';
import { Tab } from 'apps/ordering/src/entities/tabs/tabs.entity';
import { Customer } from 'apps/business/src/proto/proto/business/customers.pb';
import { Payment } from 'apps/ordering/src/entities/payments/payments.entity';
import { ModifierOption } from 'apps/menu/src/entities/modifier-option.entity';
import { randomUUID } from 'crypto';
import { Modifier, ModifierModifierOption } from 'apps/menu/src/entities/modifier.entity';
import { Product, ProductModifier } from 'apps/menu/src/entities/product.entity';
import { MenuCategory, MenuCategoryProduct } from 'apps/menu/src/entities/menu-category.entity';
import { Menu, MenuCategoryLink } from 'apps/menu/src/entities/menu.entity';
import { FoodAndBevEnterpriseNotFoundException, TabNotFoundException } from '@tangopay/shared/errors';
import { Order } from 'apps/ordering/src/entities/orders/orders.entity';
import { ProductInOrder } from 'apps/ordering/src/entities/orders/products-in-order.entity';
import { VoidedProduct } from 'apps/ordering/src/entities/payments/voided-products.entity';
import { PaysFor } from 'apps/ordering/src/entities/payments/pays-for.entity';
import { ReportingProduct } from 'apps/ordering/src/entities/reporting/reporting-product.entity';
import { ReportingProductEvent } from 'apps/ordering/src/entities/reporting/reporting-product-event.entity';
import { InventoryPricedItem, PricedItemBusinessInfo } from 'apps/inventory/src/entities/lite/priced-item.entity';
import { PricedRecipe } from 'apps/inventory/src/entities/lite/priced-recipe.entity';
import { PricedIngredient } from 'apps/inventory/src/entities/lite/priced-ingredient.entity';
import { Logger } from '@tangopay/shared/logger/logger';
import { PricedItemEvent } from 'apps/inventory/src/entities/lite/priced-item-event.entity';
import { chunk, groupBy } from 'lodash';
import { VendorItemPackaging } from '../../entities/VendorItemPackaging.entity';

import { SearchTable, Match, getNGramWeights } from '../../utils/comparison';
import { StripeSource } from 'apps/ordering/src/entities/payments/source.stripe.entity';
import { CashSource } from 'apps/ordering/src/entities/payments/source.cash.entity';
import { GiftCardSource } from 'apps/ordering/src/entities/payments/source.gift-card.entity';
import { LossSource } from 'apps/ordering/src/entities/payments/source.loss.entity';
import { ExternalSource } from 'apps/ordering/src/entities/payments/source.external.entity';
import { getEquivalenceRatio, getRatioForSale, normalizeInventoryItemName, normalizeItemName } from '@tangopay/shared/utils/unit-conversion';
import { Reservation } from 'apps/booking/src/entities/reservation.entity';
import { Table } from 'apps/booking/src/entities/table.entity';
import { Shift } from 'apps/scheduling/src/entities/scheduling/shift.entity';
import { AvailabilityLite } from 'apps/scheduling/src/entities/scheduling/availability-lite.entity';
import { StringMatchingService } from './string-matching.service';

export interface MatchableStaffMember {
  // The name string to attempt to match against
  name: string;
  // the ID to match in the metadata
  externalId: string;
  // The label to look for in the metadata
  externalIdLabel: string;
}

export type IngestedModifierOption = {
  refId: string;
  name: string;
  price: number;
  plu: string;
  ingestedIds: string[];
  ingestedIdsLabel: string;
};

export type IngestedModifierGroup = {
  refId: string;
  name: string;
  ingestedId: string;
  ingestedIdLabel: string;
  modifierOptionRefIds: string[];
  min: number;
  max: number;
};

export type IngestedProduct = {
  refId: string;
  name: string;
  price: number;
  description: string;
  ingestedId: string;
  ingestedIdLabel: string;
  modifierGroupsRefIds: string[];
  plu: string;
};

export type IngestedMenuCategory = {
  refId: string;
  name: string;
  ingestedId: string;
  ingestedIdLabel: string;
  productsRefIds: string[];
};

export type IngestedMenu = {
  refId: string;
  name: string;
  ingestedId: string;
  ingestedIdLabel: string;
  categoriesRefIds: string[];
};

export type MenuTreeProduct = {
  id: string;
  ingestedId: string;
  name: string;
};

export type MenuTreeCategory = {
  id: string;
  ingestedId: string;
  name: string;
  products: Map<string, MenuTreeProduct>;
};

export type MenuTreeData = {
  id: string;
  ingestedId: string;
  name: string;
  categories: Map<string, MenuTreeCategory>;
};

export class ReportIngestionSearcherService {
  @InjectDataSource()
  private dataSource: DataSource;

  @InjectRepository(StaffMember)
  private staffMemberRepository: Repository<StaffMember>;

  @InjectRepository(Role)
  private roleRepository: Repository<Role>;

  @InjectRepository(Department)
  private departmentRepository: Repository<Department>;

  @InjectRepository(PayRate)
  private payRateRepository: Repository<PayRate>;

  @InjectRepository(WorkEvent)
  private workEventRepository: Repository<WorkEvent>;

  @Inject(BusinessClient)
  private businessClient: BusinessClient;

  @InjectRepository(Tab)
  private tabRepository: Repository<Tab>;

  @InjectRepository(Payment)
  private paymentRepository: Repository<Payment>;

  @InjectRepository(StripeSource)
  private stripeSourceRepository: Repository<StripeSource>;

  @InjectRepository(CashSource)
  private cashSourceRepository: Repository<CashSource>;

  @InjectRepository(GiftCardSource)
  private giftCardSourceRepository: Repository<GiftCardSource>;

  @InjectRepository(LossSource)
  private lossSourceRepository: Repository<LossSource>;

  @InjectRepository(ExternalSource)
  private externalSourceRepository: Repository<ExternalSource>;

  @InjectRepository(ModifierOption)
  private modifierOptionRepository: Repository<ModifierOption>;

  @InjectRepository(Modifier)
  private modifierRepository: Repository<Modifier>;

  @InjectRepository(Product)
  private productRepository: Repository<Product>;

  @InjectRepository(MenuCategory)
  private menuCategoryRepository: Repository<MenuCategory>;

  @InjectRepository(MenuCategoryProduct)
  private menuCategoryProductRepository: Repository<MenuCategoryProduct>;

  @InjectRepository(Menu)
  private menuRepository: Repository<Menu>;

  @InjectRepository(MenuCategoryLink)
  private menuCategoryLinkRepository: Repository<MenuCategoryLink>;

  @InjectRepository(Order)
  private orderRepository: Repository<Order>;

  @InjectRepository(ProductInOrder)
  private productInOrderRepository: Repository<ProductInOrder>;

  @InjectRepository(ReportingProduct)
  private reportingProductRepository: Repository<ReportingProduct>;

  @InjectRepository(ReportingProductEvent)
  private reportingProductEventRepository: Repository<ReportingProductEvent>;

  @InjectRepository(VoidedProduct)
  private voidedProductRepository: Repository<VoidedProduct>;

  @InjectRepository(PaysFor)
  private paysForRepository: Repository<PaysFor>;

  @InjectRepository(InventoryPricedItem)
  private inventoryPricedItemRepository: Repository<InventoryPricedItem>;

  @InjectRepository(PricedItemBusinessInfo)
  private pricedItemBusinessInfoRepository: Repository<PricedItemBusinessInfo>;

  @InjectRepository(PricedItemEvent)
  private pricedItemEventRepository: Repository<PricedItemEvent>;

  @InjectRepository(PricedRecipe)
  private inventoryPricedRecipeRepository: Repository<PricedRecipe>;

  @InjectRepository(PricedIngredient)
  private inventoryPricedIngredientRepository: Repository<PricedIngredient>;

  @InjectRepository(VendorItemPackaging)
  private vendorItemPackagingRepository: Repository<VendorItemPackaging>;

  @InjectRepository(Reservation)
  private reservationRepository: Repository<Reservation>;

  @InjectRepository(Table)
  private tableRepository: Repository<Table>;

  @InjectRepository(Shift)
  private shiftRepository: Repository<Shift>;

  @InjectRepository(AvailabilityLite)
  private availabilityLiteRepository: Repository<AvailabilityLite>;

  @Inject(StringMatchingService)
  private stringMatchingService: StringMatchingService;

  private logger: Logger = new Logger(ReportIngestionSearcherService.name);
  constructor() {}

  private async findOrCreateStaffMember(business: Business, allBusinessStaffMembers: StaffMember[], toIdentify: MatchableStaffMember): Promise<StaffMember> {
    // If matched by external ID, return the staff member
    const findByExternalId =
      toIdentify.externalIdLabel &&
      toIdentify.externalId &&
      allBusinessStaffMembers.find((staffMember) => staffMember.ingestionData?.[toIdentify.externalIdLabel] === toIdentify.externalId);
    if (findByExternalId) {
      return findByExternalId;
    }

    const findByEmployeeName = allBusinessStaffMembers.find((staffMember) => {
      // don't match to a non-ingested staff member
      if (!staffMember.ingestionData) return false;

      if (!staffMember.ingestionData.employeeName) {
        console.warn('Ingestion data is missing employee name', staffMember.id);
        return false;
      }
      return (
        natural.JaroWinklerDistance(staffMember.ingestionData.employeeName, toIdentify.name, {
          ignoreCase: true,
        }) > 0.95
      );
    });
    if (findByEmployeeName) {
      // updates the returned data
      findByEmployeeName.ingestionData = {
        ...(findByEmployeeName.ingestionData ?? {}),
        employeeName: toIdentify.name,
        // if we don't have an external ID, don't overwrite the existing one
        [toIdentify.externalIdLabel]: toIdentify.externalId || findByEmployeeName.ingestionData[toIdentify.externalIdLabel],
      };
      // saves to DB
      this.staffMemberRepository.update(findByEmployeeName.id, {
        ingestionData: findByEmployeeName.ingestionData,
      });

      // returns the staff member
      return findByEmployeeName;
    }

    const newStaffMember = this.staffMemberRepository.create({
      business: {
        id: business.id,
      },
      ingestionData: {
        [toIdentify.externalIdLabel]: toIdentify.externalId,
        employeeName: toIdentify.name,
      },
      ingested: true,
    });

    return await this.staffMemberRepository.save(newStaffMember);
  }

  async matchStaffMembers(business: Business, toIdentify: MatchableStaffMember[], upsert = true): Promise<StaffMember[]> {
    const allBusinessStaffMembers = await this.staffMemberRepository.find({
      where: {
        business: {
          id: business.id,
        },
      },
      relations: {
        user: true,
        payRates: {
          role: true,
        },
      },
    });

    return await Promise.all(toIdentify.map((toIdentify) => this.findOrCreateStaffMember(business, allBusinessStaffMembers, toIdentify)));
  }

  private async findOrCreateDepartment(enterprise: Enterprise, allEnterpriseDepartments: Department[]) {
    const fohDepartment = allEnterpriseDepartments.find((department) => department.name === 'FOH');
    if (fohDepartment) return fohDepartment;

    const firstDepartment = allEnterpriseDepartments[0];

    if (firstDepartment) return firstDepartment;

    const newDepartment = this.departmentRepository.create({
      name: 'FOH',
      enterprise: {
        id: enterprise.id,
      },
      ingested: true,
      ingestedId: 'foh',
      schedulable: true,
    });

    return await this.departmentRepository.save(newDepartment);
  }

  private async findOrCreateRole(enterprise: Enterprise, allEnterpriseRoles: Role[], departments: Department[], roleToMatch: string) {
    const match = allEnterpriseRoles.find((role) => role.ingestedId === roleToMatch);

    if (match) return match;

    const department = await this.findOrCreateDepartment(enterprise, departments);

    const newRole = this.roleRepository.create({
      name: roleToMatch,
      enterprise: {
        id: enterprise.id,
      },
      department: {
        id: department.id,
      },
      ingested: true,
      ingestedId: roleToMatch,
      permissions: [],
    });

    return await this.roleRepository.save(newRole);
  }

  async matchRoles(enterprise: Enterprise, rolesToMatch: string[]) {
    const allEnterpriseRoles = await this.roleRepository.find({
      where: {
        enterprise: {
          id: enterprise.id,
        },
        ingested: true,
      },
    });

    const departments = await this.departmentRepository.find({
      where: {
        enterprise: {
          id: enterprise.id,
        },
      },
    });

    if (!departments.some((d) => d.name === 'FOH')) {
      const fohDepartment = await this.findOrCreateDepartment(enterprise, departments);
      departments.push(fohDepartment);
    }

    return await Promise.all(rolesToMatch.map((roleToMatch) => this.findOrCreateRole(enterprise, allEnterpriseRoles, departments, roleToMatch)));
  }

  async matchPayRates(staffMember: StaffMember, role: Role, rate: number, adjustedRate: number, overtimeRate?: number, doNotUpdate = false) {
    // Attempt to find a role match that is not deleted
    const roleMatch = (staffMember.payRates ?? []).find((pr) => pr.role.id === role.id && pr.deletedAt === null);

    // If the role match is found, check if the rate is the same
    if (roleMatch) {
      if (doNotUpdate) return roleMatch;
      // If the rate is the same, return the pay rate
      if (roleMatch.hourlyRate === rate && roleMatch.adjustedHourlyRate === adjustedRate) {
        if ((overtimeRate && roleMatch.overtimeRate === overtimeRate) || (!overtimeRate && roleMatch.overtimeRate === null)) {
          return roleMatch;
        }
      }

      // If the rate is different, remove the existing pay rate and create a new one
      await this.payRateRepository.softRemove(roleMatch);
      // mark the role as deleted
      roleMatch.deletedAt = new Date();
    }

    // Create a new pay rate
    const newPayRate = this.payRateRepository.create({
      primaryRole: false,
      role: {
        id: role.id,
      },
      staffMember: {
        id: staffMember.id,
      },
      ingested: true,
      hourlyRate: rate,
      adjustedHourlyRate: adjustedRate,
      overtimeRate: overtimeRate,
    });
    const resultingRate = await this.payRateRepository.save(newPayRate);
    staffMember.payRates = [...(staffMember.payRates ?? []), resultingRate];
    return resultingRate;
  }

  async upsertWorkEvent(business: Business, staffMember: StaffMember, payRate: PayRate, punch: IngestedLabourReportPunch) {
    const start = dayjs.tz(`${punch.date}T${punch.timeIn}:00Z`, business.timezone);
    const unadjustedEnd = dayjs.tz(`${punch.date}T${punch.timeOut}:00Z`, business.timezone);
    const end = unadjustedEnd.isBefore(start) ? unadjustedEnd.add(1, 'day') : unadjustedEnd;

    if (start.isAfter(end)) {
      console.log('start is after end', punch, start.toISOString(), end.toISOString());
      return;
    }

    const existing = await this.workEventRepository.find({
      where: {
        staffMember: {
          id: staffMember.id,
        },
        startTime: LessThan(end.toDate()),
        endTime: MoreThan(start.toDate()),
        ingested: true,
      },
    });

    if (existing.length > 0) await this.workEventRepository.softRemove(existing);

    const newWorkEvent = this.workEventRepository.create({
      staffMember: {
        id: staffMember.id,
      },
      payRate: {
        id: payRate.id,
      },
      businessId: business.id,
      startTime: start.toDate(),
      endTime: end.toDate(),
      ingested: true,
    });

    return await this.workEventRepository.save(newWorkEvent);
  }

  async findOrCreateWorkEvent(business: Business, staffMember: StaffMember, payRate: PayRate, workEvent: DeepPartial<WorkEvent>, ingestedId: string) {
    const existing = await this.workEventRepository.findOne({
      where: {
        businessId: business.id,
        ingestedId: ingestedId,
      },
    });

    const toSave = existing
      ? this.workEventRepository.merge(existing, workEvent)
      : this.workEventRepository.create({
          ...workEvent,
          staffMember: { id: staffMember.id },
          payRate: { id: payRate.id },
          businessId: business.id,
          ingested: true,
          ingestedId: ingestedId,
        });
    // payrate is not being set in the merge
    toSave.payRate = {
      id: payRate.id,
    } as PayRate;

    return await this.workEventRepository.save(toSave);
  }

  async findOrCreateCustomer(
    business: Business,
    customer: {
      phoneNumber: string;
      email: string;
      name: string;
    },
  ) {
    const [firstName, ...lastName] = customer.name.split(' ');
    const savedCustomer = await this.businessClient.saveCustomer({
      businessId: business.id,
      firstName,
      lastName: lastName.join(' '),
      phoneNumber: customer.phoneNumber,
      email: customer.email,
      paymentMethods: [],
      paymentMethodsToRemove: [],
    });

    return savedCustomer;
  }

  async findTabByIngestedId(business: Business, ingestedId: string) {
    return await this.tabRepository.findOne({
      where: {
        ingestedId: ingestedId,
        business: {
          id: business.id,
        },
      },
      relations: {
        staff: true,
      },
    });
  }

  async findTabsByIngestedIds(business: Business, ingestedIds: string[]): Promise<Map<string, Tab>> {
    const tabs = await this.tabRepository.find({
      where: {
        ingestedId: In(ingestedIds),
        business: {
          id: business.id,
        },
      },
    });

    return new Map(tabs.map((tab) => [tab.ingestedId, tab]));
  }

  async getTabsInDateRange(business: Business, start: Date, end: Date) {
    const tabs = await this.tabRepository.find({
      where: {
        business: { id: business.id },
        createdAt: Between(start, end),
        ingested: true,
      },
      relations: {
        orders: true,
      },
    });

    return new Map(tabs.map((tab) => [tab.ingestedId, tab]));
  }

  async findTabByIngestionData(business: Business, value: string, label: string) {
    return await this.tabRepository
      .createQueryBuilder('tab')
      .where('tab.business = :businessId', { businessId: business.id })
      .andWhere(`tab.ingestion_data->:label ? :value`, { label, value })
      .getOne();
  }

  async findOrCreateTab(business: Business, ingestedId: string, data: Partial<Tab>, staffMember?: StaffMember, customer?: Customer) {
    try {
      const existing = await this.findTabByIngestedId(business, ingestedId);
      const updatedTab = existing
        ? this.tabRepository.merge(existing, data)
        : this.tabRepository.create({
            ...data,
            ingestedId,
            ingested: true,
            staff: staffMember
              ? {
                  id: staffMember.id,
                }
              : null,
            business: {
              id: business.id,
            },
            customer: customer
              ? {
                  name: `${customer.firstName} ${customer.lastName}`,
                  phoneNumber: customer.phoneNumber,
                  email: customer.email,
                }
              : null,
          });

      return await this.tabRepository.save(updatedTab);
    } catch (error) {
      console.log('error', error);
      throw error;
    }
  }

  async updateTab(tab: Tab, data: Partial<Tab>) {
    return await this.tabRepository.update(tab.id, data);
  }

  async findPaymentsByIngestData(business: Business, ingestedId: string, ingestedIdLabel: string) {
    return await this.paymentRepository
      .createQueryBuilder('payment')
      .where('payment.business = :businessId', { businessId: business.id })
      .andWhere('payment.ingestion_data->>:label = :value', { label: ingestedIdLabel, value: ingestedId })
      .getMany();
  }

  async deletePaymentsForTabs(tabIds: string[]) {
    const payments = await this.paymentRepository.createQueryBuilder('payment').select('id', 'payment_id').where('tab_id IN (:...tabIds)', { tabIds }).getRawMany<{
      payment_id: string;
    }>();

    if (payments.length === 0) return;

    const paymentIds = payments.map((p) => p.payment_id);
    await this.paysForRepository.createQueryBuilder('pays_for').delete().where('payment_id IN (:...paymentIds)', { paymentIds }).execute();
    await this.stripeSourceRepository.createQueryBuilder('stripe_source').delete().where('payment_id IN (:...paymentIds)', { paymentIds }).execute();
    await this.cashSourceRepository.createQueryBuilder('cash_source').delete().where('payment_id IN (:...paymentIds)', { paymentIds }).execute();
    await this.giftCardSourceRepository.createQueryBuilder('gift_card_source').delete().where('payment_id IN (:...paymentIds)', { paymentIds }).execute();
    await this.lossSourceRepository.createQueryBuilder('loss_source').delete().where('payment_id IN (:...paymentIds)', { paymentIds }).execute();
    await this.externalSourceRepository.createQueryBuilder('external_source').delete().where('payment_id IN (:...paymentIds)', { paymentIds }).execute();
    await this.paymentRepository.createQueryBuilder('payment').delete().where('tab_id IN (:...tabIds)', { tabIds }).execute();
  }

  async findPaymentsInDateRange(business: Business, start: Date, end: Date) {
    const payments = await this.paymentRepository.find({
      where: {
        business: { id: business.id },
        createdAt: Between(start, end),
        ingested: true,
      },
    });

    const paymentsByCheckId = new Map<string, Payment[]>();

    for (const payment of payments) {
      const checkId = payment.ingestionData.toastCheckId;
      if (!paymentsByCheckId.has(checkId)) {
        paymentsByCheckId.set(checkId, []);
      }
      paymentsByCheckId.get(checkId)?.push(payment);
    }

    return paymentsByCheckId;
  }

  async findOrCreatePayment(business: Business, ingestedId: string, data: DeepPartial<Payment>, tab: Tab, staffMember?: StaffMember) {
    try {
      const existing = await this.paymentRepository.findOne({
        where: {
          ingestedId: ingestedId,
          business: {
            id: business.id,
          },
        },
      });

      const updatedPayment = existing
        ? this.paymentRepository.merge(existing, data)
        : this.paymentRepository.create({
            ...data,
            ingestedId,
            ingested: true,
            staff: staffMember ? { id: staffMember.id } : null,
            tab: {
              id: tab.id,
            },
            business: {
              id: business.id,
            },
          });
      updatedPayment.createdAt = data.createdAt as Date;

      return await this.paymentRepository.save(updatedPayment);
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async updatePayment(payment: Payment, data: DeepPartial<Payment>) {
    return await this.paymentRepository.update(payment.id, data);
  }

  async findOrCreateModifierOptions(enterprise: Enterprise, options: IngestedModifierOption[]) {
    const existingModifierOptions = await this.modifierOptionRepository.find({
      where: {
        enterprise: {
          id: enterprise.id,
        },
      },
    });

    const modOptsByRefId = options.reduce((acc, opt): Map<string, ModifierOption> => {
      const existingById = existingModifierOptions.find((o) => opt.ingestedIds.some((id) => o.ingestedIds?.includes(id)));

      if (existingById) {
        const idsSet = [...new Set([...(existingById.ingestedIds ?? []), ...opt.ingestedIds])];
        existingById.ingestedIds = idsSet;
        existingById.ingestionData = {
          toastModifierOptionIds: idsSet,
        };
        existingById.name = opt.name;
        existingById.plu = opt.plu ?? '';
        existingById.additionalCost = opt.price;
        acc.set(opt.refId, existingById);
        return acc;
      }

      const existingByName = existingModifierOptions.find((o) => natural.JaroWinklerDistance(o.name, opt.name, { ignoreCase: true }) > 0.8);

      if (existingByName) {
        existingByName.ingestedIds = [...new Set([...(existingByName.ingestedIds ?? []), ...opt.ingestedIds])];
        existingByName.ingestionData = {
          toastModifierOptionIds: existingByName.ingestedIds,
        };
        existingByName.name = opt.name;
        existingByName.plu = opt.plu ?? '';
        existingByName.additionalCost = opt.price;
        acc.set(opt.refId, existingByName);
        return acc;
      }

      const ids = [...new Set(opt.ingestedIds)];

      const newOpt = this.modifierOptionRepository.create({
        id: `${randomUUID()}`,
        ingested: true,
        ingestedIds: ids,
        ingestionData: {
          toastModifierOptionIds: ids,
        },
        name: opt.name,
        plu: opt.plu ?? '',
        additionalCost: opt.price,
        enabled: true,
        isSize: false,
        sizeMultiplier: null,
        priceMultiplier: null,
        tags: [],
        enterprise: {
          id: enterprise.id,
        },
      });
      acc.set(opt.refId, newOpt);
      return acc;
    }, new Map<string, ModifierOption>());

    await this.modifierOptionRepository.save(Array.from(modOptsByRefId.values()));

    return modOptsByRefId;
  }

  async findOrCreateModifierGroups(enterprise: Enterprise, groups: IngestedModifierGroup[], modifierOptions: Map<string, ModifierOption>) {
    const existingModifierGroups = await this.modifierRepository.find({
      where: {
        enterprise: {
          id: enterprise.id,
        },
      },
    });

    const modGroupsByRefId = groups.reduce((acc, group): Map<string, Modifier> => {
      const existingById = existingModifierGroups.find((g) => group.ingestedId === g.ingestedId);

      if (existingById) {
        existingById.nameExternal = group.name;
        existingById.nameInternal = group.name;
        existingById.ingestionData = {
          toastModifierId: group.ingestedId,
        };
        existingById.availableOptions = group.modifierOptionRefIds.map(
          (refId): DeepPartial<ModifierModifierOption> => ({
            modifierId: existingById.id,
            optionId: modifierOptions.get(refId)?.id,
          }),
        ) as ModifierModifierOption[];
        existingById.min = group.min;
        existingById.max = group.max;
        acc.set(group.refId, existingById);
        return acc;
      }

      const newId = `${randomUUID()}`;
      const newGroup = this.modifierRepository.create({
        id: newId,
        ingested: true,
        ingestedId: group.ingestedId,
        nameExternal: group.name,
        nameInternal: group.name,
        min: group.min,
        max: group.max,
        enabled: true,
        description: '',
        plu: '',
        ingestionData: {
          toastModifierId: group.ingestedId,
        },
        availableOptions: group.modifierOptionRefIds.map(
          (refId): DeepPartial<ModifierModifierOption> => ({
            modifierId: newId,
            optionId: modifierOptions.get(refId)?.id,
          }),
        ),
        enterprise: {
          id: enterprise.id,
        },
      });
      acc.set(group.refId, newGroup);
      return acc;
    }, new Map<string, Modifier>());

    await this.modifierRepository.save(Array.from(modGroupsByRefId.values()));

    return modGroupsByRefId;
  }

  async findOrCreateProduct(enterprise: Enterprise, products: IngestedProduct[], modifierGroups: Map<string, Modifier>) {
    const existingProducts = await this.productRepository.find({
      where: {
        enterprise: {
          id: enterprise.id,
        },
      },
    });

    const prodByRefId = products.reduce((acc, product): Map<string, Product> => {
      const existingById = existingProducts.find((p) => product.ingestedId === p.ingestedId);

      const notMatched = product.modifierGroupsRefIds.filter((refId) => !modifierGroups.has(refId));

      if (notMatched.length > 0) {
        console.log('not matched', product);
      }

      if (existingById) {
        existingById.nameExternal = product.name;
        existingById.nameInternal = product.name;
        existingById.ingestionData = {
          toastProductId: product.ingestedId,
        };
        existingById.price = product.price;
        existingById.description = product.description;

        existingById.modifiedBy = product.modifierGroupsRefIds.map(
          (refId): DeepPartial<ProductModifier> => ({
            modifierId: modifierGroups.get(refId)?.id,
          }),
        ) as ProductModifier[];

        acc.set(product.refId, existingById);
        return acc;
      }

      const newProduct = this.productRepository.create({
        id: `${randomUUID()}`,
        ingested: true,
        ingestedId: product.ingestedId,
        nameExternal: product.name,
        nameInternal: product.name,
        ingestionData: {
          toastProductId: product.ingestedId,
        },
        price: product.price,
        description: product.description,
        plu: product.plu,
        tags: [],
        alcohol: false,
        isMeal: false,
        type: 'product',
        subType: [],
        enabled: true,
        modifiedBy: product.modifierGroupsRefIds.map(
          (refId): DeepPartial<ProductModifier> => ({
            modifierId: modifierGroups.get(refId)?.id,
          }),
        ),
        enterprise: {
          id: enterprise.id,
        },
      });

      acc.set(product.refId, newProduct);
      return acc;
    }, new Map<string, Product>());

    await this.productRepository.save(Array.from(prodByRefId.values()));

    return prodByRefId;
  }

  async createProduct(
    enterprise: Enterprise,
    product: {
      name: string;
      id: string;
      ingestedId: string;
      ingestedIdLabel: string;
      modifierGroupsRefIds: string[];
      price: number;
      type: string;
    },
  ) {
    const newProduct = this.productRepository.create({
      id: product.id,
      ingested: true,
      ingestedId: product.ingestedId,
      nameExternal: product.name,
      nameInternal: product.name,
      ingestionData: {
        toastProductName: product.name,
      },
      price: product.price,
      description: '',
      plu: '',
      tags: [],
      alcohol: false,
      isMeal: false,
      type: product.type,
      subType: [],
      enabled: true,
      modifiedBy: [],
      enterprise: { id: enterprise.id },
    });

    await this.productRepository.save(newProduct);

    return newProduct;
  }

  async findProductByName(enterprise: Enterprise, name: string) {
    return await this.productRepository.findOne({
      where: {
        enterprise: { id: enterprise.id },
        nameExternal: name,
      },
    });
  }

  async findOrCreateMenuCategories(enterprise: Enterprise, categories: IngestedMenuCategory[], products: Map<string, Product>) {
    const existingCategories = await this.menuCategoryRepository.find({
      where: {
        enterprise: {
          id: enterprise.id,
        },
      },
    });

    const catByRefId = categories.reduce((acc, category): Map<string, MenuCategory> => {
      const existingById = existingCategories.find((c) => category.ingestedId === c.ingestedId);

      if (existingById) {
        existingById.ingestionData = {
          toastCategoryId: category.ingestedId,
        };
        existingById.name = category.name;
        existingById.contents = category.productsRefIds.map(
          (refId): DeepPartial<MenuCategoryProduct> => ({
            productId: products.get(refId)?.id,
          }),
        ) as MenuCategoryProduct[];
        acc.set(category.refId, existingById);
        return acc;
      }

      const newCategory = this.menuCategoryRepository.create({
        id: `${randomUUID()}`,
        ingestionData: {
          toastCategoryId: category.ingestedId,
        },
        ingested: true,
        ingestedId: category.ingestedId,
        name: category.name,
        contents: category.productsRefIds.map(
          (refId): DeepPartial<MenuCategoryProduct> => ({
            productId: products.get(refId)?.id,
          }),
        ),
        enabled: true,
        enterprise: {
          id: enterprise.id,
        },
      });

      acc.set(category.refId, newCategory);
      return acc;
    }, new Map<string, MenuCategory>());

    await this.menuCategoryRepository.save(Array.from(catByRefId.values()));

    return catByRefId;
  }

  async linkProductsToMenuCategory(categoryId: string, productIds: string[]) {
    const links = productIds.map(
      (refId): DeepPartial<MenuCategoryProduct> => ({
        productId: refId,
        categoryId,
      }),
    );
    await this.menuCategoryProductRepository.save(links);
  }

  async createMenuCategory(
    enterprise: Enterprise,
    category: {
      name: string;
      id: string;
      ingestedId: string;
      productsRefIds: string[];
    },
  ) {
    const newCategory = this.menuCategoryRepository.create({
      id: category.id,
      ingested: true,
      ingestedId: category.ingestedId,
      name: category.name,
      contents: category.productsRefIds.map(
        (refId): DeepPartial<MenuCategoryProduct> => ({
          productId: refId,
        }),
      ),
      enabled: true,
      enterprise: { id: enterprise.id },
    });

    await this.menuCategoryRepository.save(newCategory);

    return newCategory;
  }

  async findMenuCategoryByName(menuId: string, categoryName: string) {
    return await this.menuRepository.findOne({
      where: {
        id: menuId,
        menuCategories: {
          menuCategory: {
            name: categoryName,
          },
        },
      },
      relations: {
        menuCategories: {
          menuCategory: true,
        },
      },
    });
  }

  async findOrCreateMenu(enterprise: Enterprise, menus: IngestedMenu[], categories: Map<string, MenuCategory>) {
    const existingMenus = await this.menuRepository.find({
      where: {
        enterprise: {
          id: enterprise.id,
        },
      },
    });

    const menusToDeleteCategoryLinksFor = new Set<string>();
    const newCategoryLinks: MenuCategoryLink[] = [];

    const menuByRefId = menus.reduce((acc, menu): Map<string, Menu> => {
      const existingById = existingMenus.find((m) => menu.ingestedId === m.ingestedId);

      if (existingById) {
        existingById.ingestionData = {
          toastMenuId: menu.ingestedId,
        };
        existingById.nameExternal = menu.name;
        existingById.nameInternal = menu.name;
        menusToDeleteCategoryLinksFor.add(existingById.id);
        newCategoryLinks.push(
          ...menu.categoriesRefIds.map((refId) =>
            this.menuCategoryLinkRepository.create({
              menuCategoryId: categories.get(refId)?.id,
              menuId: existingById.id,
            }),
          ),
        );
        acc.set(menu.refId, existingById);
        return acc;
      }

      const newMenu = this.menuRepository.create({
        id: `${randomUUID()}`,
        ingested: true,
        ingestedId: menu.ingestedId,
        nameExternal: menu.name,
        nameInternal: menu.name,
        tags: [],
        enabled: true,
        ingestionData: {
          toastMenuId: menu.ingestedId,
        },
        enterprise: {
          id: enterprise.id,
        },
      });

      newCategoryLinks.push(
        ...menu.categoriesRefIds.map((refId) =>
          this.menuCategoryLinkRepository.create({
            menuCategoryId: categories.get(refId)?.id,
            menuId: newMenu.id,
          }),
        ),
      );

      acc.set(menu.refId, newMenu);
      return acc;
    }, new Map<string, Menu>());

    await this.menuRepository.save(Array.from(menuByRefId.values()));

    // delete the category links for the menus we are updating
    await this.menuCategoryLinkRepository.delete({
      menuId: In([...menusToDeleteCategoryLinksFor]),
    });

    // save the new category links
    await this.menuCategoryLinkRepository.save(newCategoryLinks);

    return menuByRefId;
  }

  async linkCategoriesToMenu(menuId: string, categoryIds: string[]) {
    const newLinks = categoryIds.map((refId) =>
      this.menuCategoryLinkRepository.create({
        menuCategoryId: refId,
        menuId,
      }),
    );
    await this.menuCategoryLinkRepository.save(newLinks);
  }

  async createMenu(
    enterprise: Enterprise,
    menu: {
      name: string;
      id: string;
      ingestedId: string;
      categoriesIds: string[];
    },
  ) {
    const newMenu = this.menuRepository.create({
      id: menu.id,
      ingested: true,
      ingestedId: menu.ingestedId,
      nameExternal: menu.name,
      nameInternal: menu.name,
      tags: [],
      enabled: true,
      ingestionData: {
        toastMenuName: menu.name,
      },
      enterprise: {
        id: enterprise.id,
      },
    });

    await this.menuRepository.save(newMenu);

    const newCategoryLinks = menu.categoriesIds.map((refId) =>
      this.menuCategoryLinkRepository.create({
        menuCategoryId: refId,
        menuId: newMenu.id,
      }),
    );

    await this.menuCategoryLinkRepository.save(newCategoryLinks);

    return newMenu;
  }

  async findMenuByName(enterprise: Enterprise, name: string) {
    return await this.menuRepository.findOne({
      where: {
        enterprise: { id: enterprise.id },
        nameExternal: name,
      },
    });
  }

  async getMenuDataForBusiness(business: Business): Promise<Map<string, MenuTreeData>> {
    const foodAndBevEnterprise = business.enterprises.find((e) => e.foodAndBev);

    if (!foodAndBevEnterprise) throw new FoodAndBevEnterpriseNotFoundException(business.id);

    const menuData = await this.menuRepository
      .createQueryBuilder('menu')
      .select(['menu.id', 'menu.nameExternal', 'menu.nameInternal', 'menu.ingestedId'])
      .leftJoin('menu.menuCategories', 'category_link')
      .leftJoin('category_link.menuCategory', 'category')
      .addSelect(['category.id', 'category.name', 'category.ingestedId'])
      .leftJoin('category.contents', 'product_link')
      .leftJoin('product_link.product', 'product')
      .addSelect(['product.id', 'product.nameExternal', 'product.nameInternal', 'product.ingestedId'])
      .where('menu.enterprise = :enterpriseId', { enterpriseId: foodAndBevEnterprise.id })
      .getRawMany<{
        menu_id: string;
        menu_name_external: string;
        menu_name_internal: string;
        menu_ingested_id: string;
        category_id: string;
        category_name: string;
        category_ingested_id: string;
        product_id: string;
        product_name_external: string;
        product_name_internal: string;
        product_ingested_id: string;
      }>();

    const menuDataMap = new Map<string, MenuTreeData>();

    for (const menu of menuData) {
      const menuData = menuDataMap.get(menu.menu_name_external) ?? {
        id: menu.menu_id,
        ingestedId: menu.menu_ingested_id,
        name: menu.menu_name_external,
        categories: new Map<string, MenuTreeCategory>(),
      };
      menuDataMap.set(menu.menu_name_external, menuData);

      if (menu.category_name === null) continue;
      const categoryData = menuData.categories.get(menu.category_name) ?? {
        id: menu.category_id,
        ingestedId: menu.category_ingested_id,
        name: menu.category_name,
        products: new Map<string, MenuTreeProduct>(),
      };
      menuData.categories.set(menu.category_name, categoryData);

      if (menu.product_name_external === null) continue;
      const productData = categoryData.products.get(menu.product_name_external) ?? {
        id: menu.product_id,
        ingestedId: menu.product_ingested_id,
        name: menu.product_name_external,
      };
      categoryData.products.set(menu.product_name_external, productData);
    }

    return menuDataMap;
  }

  async findOrCreateOrder(business: Business, ingestedId: string, tab: Tab, data: DeepPartial<Order>) {
    const existingOrder = await this.orderRepository.findOne({
      where: {
        business: { id: business.id },
        ingestedId,
      },
    });

    if (existingOrder) {
      const merged = this.orderRepository.merge(existingOrder, data);
      return await this.orderRepository.save(merged);
    }

    const newOrder = this.orderRepository.create({
      ...data,
      business: { id: business.id },
      tab: { id: tab.id },
      ingestedId,
    });
    return await this.orderRepository.save(newOrder);
  }

  private async findOrdersByIngestedIds(business: Business, ingestedIds: string[], existingOrders: Map<string, Order>): Promise<Map<string, Order>> {
    const missingOrders = ingestedIds.filter((id) => !existingOrders.has(id));

    if (missingOrders.length === 0) {
      return existingOrders;
    }

    const orders = await this.orderRepository.find({
      where: {
        business: { id: business.id },
        ingestedId: In(missingOrders),
      },
    });

    orders.forEach((o) => existingOrders.set(o.ingestedId, o));

    return existingOrders;
  }

  async findOrCreateOrders(business: Business, orders: DeepPartial<Order>[], knownExistingOrders: Map<string, Order>) {
    const existingOrders = await this.findOrdersByIngestedIds(
      business,
      orders.map((o) => o.ingestedId),
      knownExistingOrders,
    );

    const mergeOrCreate = orders.map((o) => {
      const existing = existingOrders.get(o.ingestedId);
      if (existing) {
        return this.orderRepository.merge(existing, o);
      }
      return this.orderRepository.create({ ...o, business: { id: business.id } });
    });

    if (mergeOrCreate.length > 2000) {
      const results: Order[] = [];
      for (let i = 0; i < mergeOrCreate.length; i += 2000) {
        const chunk = mergeOrCreate.slice(i, i + 2000);
        results.push(...(await this.orderRepository.save(chunk)));
      }
      return results;
    } else {
      return await this.orderRepository.save(mergeOrCreate);
    }
  }

  async deleteAllProductsInOrderForOrderIds(tabIds: string[], orderIds: string[]) {
    const paymentIds = (
      await this.paymentRepository
        .createQueryBuilder('payment')
        .distinct()
        .select('payment.id', 'payment_id')
        .where('payment.tab_id IN (:...tabIds)', { tabIds })
        .getRawMany<{
          payment_id: string;
        }>()
    ).map((p) => p.payment_id);

    await this.paysForRepository.createQueryBuilder('pays_for').delete().where('payment_id = ANY(:paymentIds)', { paymentIds }).execute();
    await this.productInOrderRepository.createQueryBuilder('product_in_order').delete().where('order_id = ANY(:orderIds)', { orderIds }).execute();
  }

  async createProductsInOrder(business: Business, products: DeepPartial<ProductInOrder>[], order: Order) {
    // create new products in order
    const newProducts = products.map((p) =>
      this.productInOrderRepository.create({
        ...p,
        order: { id: order.id },
      }),
    );
    return await this.productInOrderRepository.save(newProducts);
  }

  async deleteAllReportingProductsForTabs(tabIds: string[]) {
    await this.reportingProductRepository.createQueryBuilder('reporting_product').delete().where('tab_id IN (:...tabIds)', { tabIds }).execute();
  }

  async findOrCreateReportingProduct(products: (DeepPartial<ReportingProduct> & { voided: boolean })[], tab: Tab, business: Business, staffMember?: StaffMember) {
    const newProducts = products.map((p) =>
      this.reportingProductRepository.create({
        ...p,
        discountsAppliedToProduct: [],
        // In a reporting product the price is the price before discount
        _fDiscountedPrice: 0,
        _fSubTotalAmount: p._fPrice * p.quantity,
        _fTaxAmount: p._fTaxAmount * (p._fPrice / p._fDiscountedPrice),
        tab: { id: tab.id },
        businessId: business.id,
        staffId: staffMember?.id,
        costOfGoods: p.costOfGoods ?? 0,
        orderedAt: p.orderedAt ?? new Date(),
        ingested: true,
      }),
    );
    await this.reportingProductRepository.save(newProducts);

    const newEvents = products.map((p): ReportingProductEvent[] => {
      if (p.voided) {
        return [
          this.reportingProductEventRepository.create({
            product: { id: p.id },
            eventAt: p.orderedAt,
            eventType: 'voided',
            quantity: p.quantity,
            ingested: true,
          }),
        ];
      }
      if (p.discountsAppliedToProduct.length > 0) {
        return [
          this.reportingProductEventRepository.create({
            product: { id: p.id },
            eventAt: p.orderedAt,
            eventType: 'discount_changed',
            quantity: p.quantity,
            ingested: true,
            data: p.discountsAppliedToProduct,
          }),
        ];
      }
      return [];
    });

    await this.reportingProductEventRepository.save(newEvents.flat());
  }

  async deleteAllVoidedProductsForTab(tabIds: string[]) {
    await this.voidedProductRepository.createQueryBuilder('voided_product').delete().where('tab_id IN (:...tabIds)', { tabIds }).execute();
  }

  async createVoidedProducts(products: DeepPartial<VoidedProduct>[], tab: Tab) {
    const newProducts = products.map((p) =>
      this.voidedProductRepository.create({
        ...p,
        tab: { id: tab.id },
      }),
    );
    const txn = await this.voidedProductRepository.manager.transaction(async (mgr) => {
      await mgr.delete(VoidedProduct, {
        tab: { id: tab.id },
      });
      const saved = await mgr.save(newProducts);
      return saved;
    });

    return txn;
  }

  async findOrCreatePaysFor(payment: Payment, partials: DeepPartial<PaysFor>[]) {
    await this.paysForRepository.delete({
      payment: { id: payment.id },
    });

    const newPaysFor = partials.map((p) =>
      this.paysForRepository.create({
        ...p,
        payment: { id: payment.id },
      }),
    );

    return await this.paysForRepository.save(newPaysFor);
  }

  /**
   * Finds or creates inventory priced items.
   * DO NOT include unitValue in the data, this should not be overwritten by ingest.
   */
  async findOrCreateInventoryPricedItems(business: Business, data: DeepPartial<InventoryPricedItem>[]) {
    if (data.length === 0) {
      return [];
    }
    const enterprise = business.enterprises.find((e) => e.foodAndBev);
    const existingItems = await this.inventoryPricedItemRepository
      .createQueryBuilder('pi')
      .where('pi.enterprise_id = :enterpriseId', { enterpriseId: enterprise.id })
      .andWhere('pi.ingested_id IN (:...ingestedIds)', { ingestedIds: data.map((d) => d.ingestedId) })
      .leftJoinAndMapOne('pi.thisBusinessInfo', 'pi.businessInfo', 'bi', 'bi.business.id = :businessId', { businessId: business.id })
      .getMany();

    const bizInfoToSave: PricedItemBusinessInfo[] = [];
    const itemsMapped = data.map((raw) => {
      const { defaultPrice, ...d } = raw;
      const existingItem = existingItems.find((i) => i.ingestedId === d.ingestedId);
      if (existingItem) {
        const result = this.inventoryPricedItemRepository.merge(existingItem, d);
        if (!result.thisBusinessInfo) {
          const info = this.pricedItemBusinessInfoRepository.create({
            pricedItem: { id: result.id },
            business: { id: business.id },
            price: defaultPrice,
            unitValue: defaultPrice,
          });
          result.thisBusinessInfo = info;
          bizInfoToSave.push(info);
        }
        return result;
      }
      const info = this.pricedItemBusinessInfoRepository.create({
        business: { id: business.id },
        price: defaultPrice,
        unitValue: defaultPrice,
      });
      return this.inventoryPricedItemRepository.create({
        ...d,
        enterprise: { id: enterprise.id },
        inventoryGroup: d.inventoryGroup ?? 'Unknown',
        businessInfo: [info],
        thisBusinessInfo: info,
      });
    });

    const r = await this.inventoryPricedItemRepository.save(itemsMapped);
    await this.pricedItemBusinessInfoRepository.save(bizInfoToSave);
    return r;
  }

  async findInventoryPricedItem(business: Business, data: DeepPartial<InventoryPricedItem>) {
    const enterprise = business.enterprises.find((e) => e.foodAndBev);
    const existingItem = await this.inventoryPricedItemRepository
      .createQueryBuilder('ipi')
      .where('ipi.enterprise_id = :enterpriseId', { enterpriseId: enterprise.id })
      .andWhere('ipi.ingested_id = :ingestedId', { ingestedId: data.ingestedId })
      .leftJoinAndMapOne('ipi.thisBusinessInfo', 'ipi.businessInfo', 'bi', 'bi.business.id = :businessId', { businessId: business.id })
      .leftJoinAndMapOne('ipi.variantOf', 'ipi.variantOf', 'vo')
      .leftJoinAndMapOne('vo.thisBusinessInfo', 'vo.businessInfo', 'vo_bi', 'vo_bi.business.id = :businessId', { businessId: business.id })
      .getOne();
    if (existingItem) return existingItem;
    // Do not create a new item, these are probably irrelevant
    return null;
  }

  async findInventoryPricedItemByIngestedId(business: Business, ingestedId: string) {
    const enterprise = business.enterprises.find((e) => e.foodAndBev);
    const item = await this.inventoryPricedItemRepository
      .createQueryBuilder('ipi')
      .where('ipi.enterprise_id = :enterpriseId', { enterpriseId: enterprise.id })
      .andWhere('ipi.ingested_id = :ingestedId', { ingestedId })
      .leftJoinAndMapOne('ipi.thisBusinessInfo', 'ipi.businessInfo', 'bi', 'bi.business.id = :businessId', { businessId: business.id })
      .getOne();
    if (item) return item;
    return null;
  }

  async findInventoryPricedItemsByIngestedIds(business: Business, ingestedIds: Iterable<string>) {
    const enterprise = business.enterprises.find((e) => e.foodAndBev);

    const items = await this.inventoryPricedItemRepository
      .createQueryBuilder('ipi')
      .where('ipi.enterprise_id = :enterpriseId', { enterpriseId: enterprise.id })
      .andWhere('ipi.ingested_id IN (:...ingestedIds)', { ingestedIds: Array.from(ingestedIds) })
      .leftJoinAndMapOne('ipi.thisBusinessInfo', 'ipi.businessInfo', 'bi', 'bi.business.id = :businessId', { businessId: business.id })
      .getMany();

    return new Map(items.map((i) => [i.ingestedId, i]));
  }

  async findOrCreateInventoryPricedRecipe(business: Business, data: DeepPartial<PricedRecipe>) {
    const enterprise = business.enterprises.find((e) => e.foodAndBev);
    const existing = await this.inventoryPricedRecipeRepository.findOne({
      where: {
        enterprise: { id: enterprise.id },
        ingestedId: data.ingestedId,
      },
    });

    const updated = existing
      ? this.inventoryPricedRecipeRepository.merge(existing, data)
      : this.inventoryPricedRecipeRepository.create({
          ...data,
          enterprise: { id: enterprise.id },
        });

    return await this.inventoryPricedRecipeRepository.save(updated);
  }

  async overwriteInventoryPricedIngredients(business: Business, ingestedRecipeId: string, data: DeepPartial<PricedIngredient>[]) {
    // find existing ingredients for the recipe
    const existing = await this.inventoryPricedIngredientRepository.find({
      where: {
        recipe: {
          ingestedId: ingestedRecipeId,
        },
      },
    });

    const txn = await this.inventoryPricedIngredientRepository.manager.transaction(async (mgr) => {
      // delete the existing ingredients
      await mgr.remove(existing);
      const saved = await mgr.save(PricedIngredient, data);
      return saved;
    });

    return txn;
  }

  async findPackaging(businessId: string, vendorId: string, itemId: string, packagingId: string) {
    return await this.vendorItemPackagingRepository.findOne({
      where: {
        business: { id: businessId },
        vendorId,
        packagingId,
      },
    });
  }

  async findOrCreateInventoryOrderEvent(businessId: string, pricedItem: InventoryPricedItem, data: DeepPartial<PricedItemEvent>) {
    const existing = await this.pricedItemEventRepository.findOne({
      where: {
        business: { id: businessId },
        ingestedId: data.ingestedId,
      },
    });
    if (existing) {
      // assume that the priced item is the same
      return this.pricedItemEventRepository.merge(existing, data);
    }
    return this.pricedItemEventRepository.create({
      ...data,
      business: { id: businessId },
      pricedItem,
    });
  }

  async findOrCreateInventoryOrderEvents(businessId: string, data: DeepPartial<PricedItemEvent>[]) {
    if (data.length === 0) {
      return [];
    }
    const existing = await this.pricedItemEventRepository.find({
      where: {
        ingestedId: In(data.map((d) => d.ingestedId)),
      },
    });
    const existingByIngestedId = new Map(existing.map((e) => [e.ingestedId, e]));
    const toSave = data.map((d) => {
      const existing = existingByIngestedId.get(d.ingestedId);
      if (existing) {
        return this.pricedItemEventRepository.merge(existing, d);
      }
      const pricedItem = { id: d.pricedItem?.id };
      if (!pricedItem.id) {
        this.logger.warn(`Priced item not included for ingested id ${d.ingestedId}`);
        return null;
      }
      return this.pricedItemEventRepository.create({ ...d, business: { id: businessId }, pricedItem });
    });
    return await this.pricedItemEventRepository.save(toSave.filter((d) => d !== null));
  }

  async saveInventoryOrderEvents(events: PricedItemEvent[], chunkSize = 100) {
    const chunks = chunk(events, chunkSize);
    for (const chunk of chunks) {
      await this.pricedItemEventRepository.save(chunk);
    }
  }

  async findOrCreateReservations(data: DeepPartial<Reservation>[], business: Business, startDate: string, endDate: string) {
    const [allTabs, allTables, allReservations] = await Promise.all([
      this.tabRepository.find({
        where: {
          business: { id: business.id },
          createdAt: Between(new Date(startDate), new Date(endDate)),
        },
      }),
      this.tableRepository.find({
        where: {
          business: { id: business.id },
        },
      }),
      this.reservationRepository.find({
        where: {
          business: { id: business.id },
          ingestId: In(data.map((d) => d.ingestId)),
        },
        relations: {
          tab: true,
        },
      }),
    ]);
    const tabsByTableNumber = new Map<string, Tab[]>();
    allTabs.forEach((t) => {
      const tn = `${t.tableNumber}`;
      if (!tabsByTableNumber.has(tn)) {
        tabsByTableNumber.set(tn, []);
      }
      tabsByTableNumber.get(tn).push(t);
    });
    const tablesByTableNumber = new Map(allTables.map((t) => [`${t.tableNumber}`, t]));
    const reservationsByIngestId = new Map(allReservations.map((r) => [`${r.ingestId}`, r]));
    const alreadyReservedTabIds = new Set(allReservations.map((r) => r.tab?.id));
    const toSave = data.map((d) => {
      const allTableNumbers = d.ingestData?.table?.split(',').map((t) => parseInt(t));
      if (!allTableNumbers.length) {
        // unmatchable for now
        return null;
      }

      if (d.ingestData?.status === 'No Show') {
        return this.reservationRepository.create({
          ...d,
          business: { id: business.id },
          ingestId: d.ingestId,
          noShowAt: d.reservationTime,
          turnTime: d.turnTime || 90,
        });
      }
      const allTabCandidates = allTableNumbers.flatMap((t) => tabsByTableNumber.get(`${t}`) || []).filter((t) => !alreadyReservedTabIds.has(t.id));
      if (!allTabCandidates.length) {
        // this.logger.warn(`No tab candidates found for reservation ${d.ingestData?.Guest}, ${d.ingestData?.table}@${d.ingestData?.Time}`);
        return null;
      }
      if (!d.reservationTime) {
        // this.logger.warn(`No reservation time found for reservation ${d.ingestId}`);
        return null;
      }
      const closest = allTabCandidates.reduce((bestTab, currentTab) => {
        const currentDiff = dayjs(d.reservationTime as Date).diff(dayjs(currentTab.createdAt), 'minutes');
        // 90 minutes late is too late
        if (currentDiff < -90) return bestTab;
        // 30 minutes early is too early
        if (currentDiff > 30) return bestTab;
        if (!bestTab) return currentTab;

        const bestDiff = Math.abs(dayjs(d.reservationTime as Date).diff(dayjs(bestTab.createdAt), 'minutes'));
        if (Math.abs(currentDiff) > bestDiff) return bestTab;
        return currentTab;
      }, null);
      if (!closest) {
        return null;
      }
      // don't use this tab again
      alreadyReservedTabIds.add(closest.id);

      const existing = reservationsByIngestId.get(d.ingestId);
      if (existing) {
        existing.tab = closest;
        existing.seatedAt = closest.createdAt;
        existing.closedAt = closest.completedAt;
        existing.table = tablesByTableNumber.get(`${closest.tableNumber}`);
        existing.tableNumber = closest.tableNumber;
        existing.additionalTables = allTableNumbers
          .filter((t) => t !== closest.tableNumber)
          .map((t) => tablesByTableNumber.get(`${t}`))
          .filter(Boolean);
        return this.reservationRepository.merge(existing, d);
      }
      return this.reservationRepository.create({
        ...d,
        tab: closest,
        seatedAt: closest.createdAt,
        closedAt: closest.completedAt,
        business: { id: business.id },
        table: tablesByTableNumber.get(`${closest.tableNumber}`),
        tableNumber: closest.tableNumber,
        additionalTables: allTableNumbers
          .filter((t) => t !== closest.tableNumber)
          .map((t) => tablesByTableNumber.get(`${t}`))
          .filter(Boolean),
        turnTime: d.turnTime || 90,
      });
    });
    const result = await this.reservationRepository.save(toSave.filter((r) => r !== null));
    return result;
  }

  // TODO: this cannot yet be done effectively except by a human
  // TODO: doesn't currently fetch business info
  async _identifyItemVariants(business: Business) {
    const enterpriseId = business.enterprises.find((e) => e.foodAndBev).id;
    const allItems = await this.inventoryPricedItemRepository.find({
      where: {
        enterprise: { id: enterpriseId },
        producedBy: IsNull(),
      },
      relations: {
        variantOf: true,
        variants: true,
      },
    });
    const byGroup = groupBy(allItems, (i) => i.inventoryGroup);
    for (const [_group, items] of Object.entries(byGroup)) {
      // arbitrary but stable
      items.sort((a, b) => a.name.localeCompare(b.name));
      const keys = items.map((i) => normalizeInventoryItemName(i.name));
      const getNGramWeight = getNGramWeights(keys, { NGramLength: 3, maxNGramAppearances: 3 });
      // start empty
      const baseItemsSearchTable = new SearchTable<InventoryPricedItem>([], (i) => i.name, normalizeInventoryItemName, { name: 'base', getNGramWeight: getNGramWeight });

      for (const item of items) {
        const matches = baseItemsSearchTable.search(item.name, { topN: 1, threshold: 0.85 });
        if (matches.length === 0) {
          // this is a base item
          baseItemsSearchTable.add(item);
          item.variantOf = null;
          item.variantOfRatio = null;
          // add as we go
          item.variants = [];
        } else {
          const base = matches[0].match;
          item.variantOf = base;
          item.variantOfRatio = getEquivalenceRatio(item, base);
          base.variants.push(item);
        }
      }
    }
    allItems.forEach((i) => {
      if (!i.variants?.length) return;
      const weightSum = i.variants.reduce((sum, v) => sum + (v.variantWeight ?? 0) * (v.variantOfRatio ?? 1), i.variantWeight ?? 0);
      if (!weightSum) return;
      const weightedPrice = i.variants.reduce(
        (sum, v) => sum + ((v.thisBusinessInfo?.price ?? 0) * (v.variantWeight ?? 0) * (v.variantOfRatio ?? 1)) / weightSum,
        ((i.thisBusinessInfo?.price ?? 0) * (i.variantWeight ?? 0)) / weightSum,
      );
      i.thisBusinessInfo.price = weightedPrice;
    });
    await this.inventoryPricedItemRepository.save(allItems);
  }

  async findOrCreateScheduledShift(business: Business, data: DeepPartial<Shift>) {
    const existing = await this.shiftRepository.findOne({
      where: {
        business: { id: business.id },
        ingestionId: data.ingestionId,
      },
    });
    const updated = existing ? this.shiftRepository.merge(existing, data) : this.shiftRepository.create({ ...data, business: { id: business.id } });
    return await this.shiftRepository.save(updated);
  }

  async createOrUpdateAvailability(business: Business, data: DeepPartial<AvailabilityLite>) {
    const existing = await this.availabilityLiteRepository.findOne({
      where: {
        business: { id: business.id },
        ingestionId: data.ingestionId,
      },
    });

    if (existing) {
      const updated = this.availabilityLiteRepository.merge(existing, data);
      return await this.availabilityLiteRepository.save(updated);
    }

    const saved = await this.availabilityLiteRepository.save({ ...data, business: { id: business.id }, ingested: true });
    return saved;
  }
}
