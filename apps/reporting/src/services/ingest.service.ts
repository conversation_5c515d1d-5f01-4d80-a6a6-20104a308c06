import { HttpStatus, Inject, Injectable, Logger } from '@nestjs/common';
import { BusinessClient } from '@tangopay/shared/clients/businessClient.client';
import { IngestDataRequestDto } from '../dtos/ingest.dto';
import { GenericResponse } from '../proto/reporting/shared.pb';
import { InjectRepository } from '@nestjs/typeorm';
import { Ingestion } from '../entities/Ingestion.entity';
import { Repository } from 'typeorm';
import { StorageService } from '@tangopay/shared';
import { createHash, randomUUID } from 'crypto';
import { ReportIngestionMaitredService } from './ingest/maitred.service';
import { ReportIngestionToastService } from './ingest/toast.service';
import { NoBusinessOrEnterpriseException } from '@tangopay/shared/errors';
import { Business, Enterprise } from 'apps/business/src/proto/proto/business/business.pb';
import { ReportIngestionMarginEdgeService } from './ingest/marginedge.service';
import { ReportIngestionMarginEdgePublicService } from './ingest/marginedge-public.service';
import { ReportIngestionSevenShiftsService } from './ingest/seven-shifts.service';
import { ReportIngestionSlingService } from './ingest/sling.service';
import { ReportIngestionPushService } from './ingest/push.service';

type EnterpriseIngestion = {
  enterprise: Enterprise;
  ingestion: Ingestion;
  buffer: Buffer;
};

type BusinessIngestion = {
  business: Business;
  ingestion: Ingestion;
  buffer: Buffer;
};

@Injectable()
export class ReportDataIngestionService {
  private readonly logger = new Logger(ReportDataIngestionService.name);

  @InjectRepository(Ingestion)
  private ingestionRepository: Repository<Ingestion>;

  @Inject(StorageService)
  private readonly storageService: StorageService;

  @Inject(ReportIngestionMaitredService)
  private readonly maitredService: ReportIngestionMaitredService;

  @Inject(ReportIngestionToastService)
  private readonly toastService: ReportIngestionToastService;

  @Inject(ReportIngestionMarginEdgeService)
  private readonly marginEdgeService: ReportIngestionMarginEdgeService;

  @Inject(ReportIngestionMarginEdgePublicService)
  private readonly marginEdgePublicService: ReportIngestionMarginEdgePublicService;

  @Inject(ReportIngestionSevenShiftsService)
  private readonly sevenShiftsService: ReportIngestionSevenShiftsService;

  @Inject(ReportIngestionSlingService)
  private readonly slingService: ReportIngestionSlingService;

  @Inject(ReportIngestionPushService)
  private readonly pushService: ReportIngestionPushService;

  constructor(
    @Inject(BusinessClient)
    private businessClient: BusinessClient,
  ) {}

  private async uploadFileToGCS(payload: IngestDataRequestDto, harvestId: string): Promise<BusinessIngestion | EnterpriseIngestion> {
    const buffer = Buffer.from(payload.data, 'base64');
    const hash = createHash('sha256').update(`${randomUUID()}${new Date().getTime()}`).digest('hex');
    const file = await this.storageService.save(hash, buffer, [
      {
        contentType: payload.dataFormat === 'xml' ? 'application/xml' : 'application/json',
      },
    ]);
    if (payload.businessId) {
      const business = await this.businessClient.getBusinessById(payload.businessId);
      const ingestion = this.ingestionRepository.create({
        business: {
          id: payload.businessId,
        },
        externalSource: payload.dataSource,
        ingestionType: payload.dataType,
        gcsUrl: file.url,
        harvestId,
      });
      const result = await this.ingestionRepository.save(ingestion);
      return {
        business,
        buffer,
        ingestion: result,
      };
    } else if (payload.enterpriseId) {
      const enterprise = await this.businessClient.getEnterpriseById(payload.enterpriseId);
      const ingestion = this.ingestionRepository.create({
        enterprise: {
          id: payload.enterpriseId,
        },
        externalSource: payload.dataSource,
        ingestionType: payload.dataType,
        gcsUrl: file.url,
        harvestId,
      });
      const result = await this.ingestionRepository.save(ingestion);
      return {
        enterprise,
        buffer,
        ingestion: result,
      };
    }
    throw new NoBusinessOrEnterpriseException();
  }

  public async ingestData(payload: IngestDataRequestDto, harvestId: string): Promise<GenericResponse> {
    const { buffer, ingestion, ...bizEnterprise } = await this.uploadFileToGCS(payload, harvestId);

    if ('business' in bizEnterprise) {
      const business = bizEnterprise.business;
      this.logger.log(`Ingesting data for business ${business.name} (${payload.dataSource}, ${payload.dataType})`);
      if (payload.dataSource === 'maitred') {
        if (payload.dataType === 'punches_report_by_employee_name') {
          return this.maitredService.ingestLabourReport(payload.data, business);
        }
      } else if (payload.dataSource === 'toast') {
        if (payload.dataType === 'check_detail') {
          const data = buffer.toString('utf-8');
          return this.toastService.ingestCheckDetail(data, business);
        } else if (payload.dataType === 'payment_detail') {
          const data = buffer.toString('utf-8');
          return this.toastService.ingestPaymentDetails(data, business);
        } else if (payload.dataType === 'order_detail') {
          const data = buffer.toString('utf-8');
          return this.toastService.ingestOrderDetails(data, business);
        } else if (payload.dataType === 'item_selection_detail') {
          const data = buffer.toString('utf-8');
          return this.toastService.ingestItemSelectionDetails(data, business);
        } else if (payload.dataType === 'clock_punches') {
          const data = buffer.toString('utf-8');
          return this.toastService.ingestClockPunches(data, business);
        }
      } else if (payload.dataSource === 'marginEdge') {
        if (payload.dataType === 'recipes') {
          const data = buffer.toString('utf-8');
          return this.marginEdgeService.ingestRecipes(data, business);
        } else if (payload.dataType === 'inventory_orders') {
          const data = buffer.toString('utf-8');
          return this.marginEdgeService.ingestPurchaseReport(data, business);
        } else if (payload.dataType === 'inventory_products') {
          const data = buffer.toString('utf-8');
          return this.marginEdgeService.ingestProducts(data, business);
        }
      } else if (payload.dataSource === 'marginEdgePublic') {
        if (payload.dataType === 'inventory_orders_public') {
          const data = buffer.toString('utf-8');
          return this.marginEdgePublicService.ingestInventoryOrders(data, business);
        } else if (payload.dataType === 'inventory_items_public') {
          const data = buffer.toString('utf-8');
          return this.marginEdgePublicService.ingestInventoryItems(data, business);
        }
      } else if (payload.dataSource === '7shifts') {
        if (payload.dataType === 'scheduled_shifts') {
          const data = buffer.toString('utf-8');
          return this.sevenShiftsService.ingestScheduledShifts(data, business);
        } else if (payload.dataType === 'availability') {
          const data = buffer.toString('utf-8');
          return this.sevenShiftsService.ingestAvailability(data, business);
        }
      } else if (payload.dataSource === 'sling') {
        if (payload.dataType === 'scheduled_shifts') {
          const data = buffer.toString('utf-8');
          return this.slingService.ingestScheduledShifts(data, business);
        }
      } else if (payload.dataSource === 'push') {
        if (payload.dataType === 'scheduled_shifts') {
          const data = buffer.toString('utf-8');
          return this.pushService.ingestScheduledShifts(data, business);
        }
      }
    } else if ('enterprise' in bizEnterprise) {
      const enterprise = bizEnterprise.enterprise;
      if (payload.dataSource === 'toast') {
        if (payload.dataType === 'menu') {
          const data = buffer.toString('utf-8');
          return this.toastService.ingestMenu(data, enterprise);
        }
      }
    }
    return {
      status: HttpStatus.NO_CONTENT,
      error: null,
    };
  }
}
