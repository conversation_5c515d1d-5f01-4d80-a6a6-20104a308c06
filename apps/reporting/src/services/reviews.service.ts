import { HttpStatus, Inject, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { IngestedReview, Reviewable, ReviewableSnapshot, IngestedReviewResponse } from '../entities/GoogleReview.entity';
import { In, Repository } from 'typeorm';
import { IdRequestDTO, OptionalIdRequestDTO } from '../dtos/reporting.dto';
import {
  ReviewablesResponse,
  Reviewable as ReviewablePB,
  Review as ReviewPB,
  ReviewableSnapshot as ReviewableSnapshotPB,
  ReviewableResponse,
} from '../proto/reporting/reviews.pb';
import { CreateReviewableRequestDTO, UpdateReviewableRequestDTO } from '../dtos/review.dto';
import { GenericResponse } from '../proto/reporting/shared.pb';
import { omitBy, isUndefined } from 'lodash';
import { ReviewableNotFoundException, SerpApiException } from '@tangopay/shared/errors';
import { ConfigService } from '@nestjs/config';
import { getJson } from 'serpapi';
import { BusinessClient } from '@tangopay/shared/clients/businessClient.client';
import { Insight, InsightInbox } from '../entities/Insight.entity';
import { Business } from 'apps/business/src/proto/proto/business/business.pb';
import { AnthropicService } from '@tangopay/shared/anthropic/anthropic.service';
import { getMainBodyForAnthropicResponse } from './insights/utils';
import { User } from 'apps/auth/src/entities/user.entity';
import { ExtractedReviewData, SerpApiPlaceInfo, SerpApiResponse, SerpApiReview, SerpApiTopic } from '../types/places.serp';

const toReviewableSnapshot = (snapshot: ReviewableSnapshot): ReviewableSnapshotPB => ({
  id: snapshot.id,
  title: snapshot.title,
  address: snapshot.address,
  rating: snapshot.rating,
  reviewCount: snapshot.reviewCount,
  type: snapshot.type,
  reviewTopics: snapshot.reviewTopics.map((topic) => ({
    id: topic.id,
    keyword: topic.keyword,
    mentions: topic.mentions,
  })),
});

const toReview = (review: IngestedReview): ReviewPB => {
  // Get the first response if it exists (assuming one response per review)
  const response = review.ingestedReviewResponse;

  return {
    id: review.id,
    score: review.score,
    snippet: review.snippet,
    reviewedAt: review.reviewedAt.toISOString(),
    respondedAt: response?.respondedAt?.toISOString() || '',
    respondedSnippet: response?.snippet || '',
  };
};

const toReviewable = (reviewable: Reviewable): ReviewablePB => {
  // Filter reviews to only include those with snippets
  const validReviews = (reviewable.ingestedReviews || []).filter((review) => review.snippet.length > 0);

  return {
    id: reviewable.id,
    googlePlacesId: reviewable.googlePlacesId,
    description: reviewable.description || '',
    isSelf: reviewable.isSelf,
    reviews: validReviews.map(toReview),
    snapshots: (reviewable.reviewableSnapshots || []).map(toReviewableSnapshot),
  };
};

@Injectable()
export class ReviewsService {
  private readonly logger = new Logger(ReviewsService.name);

  @Inject(BusinessClient)
  private readonly businessClient: BusinessClient;

  @Inject(AnthropicService)
  private readonly anthropicService: AnthropicService;

  @InjectRepository(Reviewable)
  private readonly reviewableRepository: Repository<Reviewable>;

  @InjectRepository(IngestedReview)
  private readonly reviewRepository: Repository<IngestedReview>;

  @InjectRepository(IngestedReviewResponse)
  private readonly reviewResponseRepository: Repository<IngestedReviewResponse>;

  @InjectRepository(ReviewableSnapshot)
  private readonly reviewableSnapshotRepository: Repository<ReviewableSnapshot>;

  @InjectRepository(Insight)
  private readonly insightRepository: Repository<Insight>;

  @InjectRepository(InsightInbox)
  private readonly insightInboxRepository: Repository<InsightInbox>;

  @InjectRepository(User)
  private readonly userRepository: Repository<User>;

  constructor(private configService: ConfigService) {}

  async getReviewables(request: IdRequestDTO): Promise<ReviewablesResponse> {
    // Get the reviewables
    const reviewables = await this.reviewableRepository.find({
      where: {
        business: {
          id: request.id,
        },
      },
      relations: {
        reviewableSnapshots: true,
      },
      order: {
        createdAt: 'ASC',
        reviewableSnapshots: {
          createdAt: 'DESC',
        },
      },
    });

    return {
      status: HttpStatus.OK,
      error: undefined,
      data: reviewables.map(toReviewable),
    };
  }

  async getReviewable(request: IdRequestDTO): Promise<ReviewableResponse> {
    // Get the reviewables
    const reviewable = await this.reviewableRepository.findOne({
      where: {
        id: request.id,
      },
      relations: {
        ingestedReviews: {
          ingestedReviewResponse: true,
        },
        reviewableSnapshots: true,
      },
      order: {
        ingestedReviews: {
          reviewedAt: 'DESC',
        },
        reviewableSnapshots: {
          createdAt: 'DESC',
        },
      },
    });

    if (!reviewable) throw new ReviewableNotFoundException(request.id);

    return {
      status: HttpStatus.OK,
      error: undefined,
      data: toReviewable(reviewable),
    };
  }

  async createReviewable(request: CreateReviewableRequestDTO): Promise<ReviewableResponse> {
    const reviewable = await this.reviewableRepository.save({
      business: { id: request.businessId },
      googlePlacesId: request.googlePlacesId,
      description: request.description,
      isSelf: request.isSelf,
    });

    return {
      status: HttpStatus.OK,
      error: undefined,
      data: toReviewable(reviewable),
    };
  }

  async updateReviewable(request: UpdateReviewableRequestDTO): Promise<ReviewableResponse> {
    const reviewable = await this.reviewableRepository.findOne({
      where: { id: request.reviewableId },
    });

    if (!reviewable) throw new ReviewableNotFoundException(request.reviewableId);

    const updates = omitBy(
      {
        description: request.description,
        isSelf: request.isSelf,
      },
      isUndefined,
    );

    await this.reviewableRepository.update(request.reviewableId, updates);

    const updatedReviewable = await this.reviewableRepository.findOne({
      where: { id: request.reviewableId },
    });

    return {
      status: HttpStatus.OK,
      error: undefined,
      data: toReviewable(updatedReviewable),
    };
  }

  async deleteReviewable(request: IdRequestDTO): Promise<GenericResponse> {
    const reviewable = await this.reviewableRepository.findOne({
      where: { id: request.id },
    });

    if (!reviewable) throw new ReviewableNotFoundException(request.id);

    await this.reviewableRepository.delete(request.id);
    return {
      status: HttpStatus.OK,
      error: undefined,
    };
  }

  private checkStopCondition(
    review: SerpApiReview,
    isNewReviewable: boolean,
    existingReviewIds: Set<string>,
    reviewCount: number,
    oldestReviewDate: Date | null,
  ): boolean {
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
    if (!isNewReviewable && existingReviewIds.has(review.review_id)) {
      return true;
    } else if (isNewReviewable && (reviewCount >= 100 || oldestReviewDate <= sixMonthsAgo)) {
      return true;
    }
    return false;
  }

  async extractSerpData(googlePlacesId: string, existingReviewIds: Set<string> = new Set()): Promise<ExtractedReviewData> {
    const API_KEY = this.configService.get<string>('SERP_API_KEY');

    if (!API_KEY) throw new SerpApiException('SERP API Key not configured');

    const results: SerpApiReview[] = [];
    const placeInfo: SerpApiPlaceInfo = {
      title: '',
      address: '',
      rating: 0,
      reviews: 0,
      type: '',
    };
    const topics: SerpApiTopic[] = [];

    const getReviews = async (params: any) => {
      try {
        const paramsWithKey = { ...params, api_key: API_KEY };
        const reviewData: SerpApiResponse = await getJson(paramsWithKey);

        if (reviewData.error) {
          return {
            placeInfo,
            topics,
            reviews: results,
          };
        }

        // Extract place info and topics from first page only
        if (reviewData.place_info && !placeInfo.title) {
          placeInfo.title = reviewData.place_info.title || '';
          placeInfo.address = reviewData.place_info.address || '';
          placeInfo.rating = reviewData.place_info.rating || 0;
          placeInfo.reviews = reviewData.place_info.reviews || 0;
          placeInfo.type = reviewData.place_info.type || '';
        }
        if (reviewData.topics && topics.length === 0) {
          for (const topic of reviewData.topics) {
            topics.push(topic);
          }
        }

        if (reviewData.reviews && reviewData.reviews.length > 0) {
          for (const review of reviewData.reviews) {
            // Return if review already exists in the set
            if (existingReviewIds.has(review.review_id)) return;

            results.push(review);
          }

          // Check stop condition before adding review
          const isNewReviewable = existingReviewIds.size === 0;
          const lastReview = results[results.length - 1];
          const reviewCount = results.length;
          const oldestReviewDate = new Date(lastReview.iso_date);
          const shouldStop = this.checkStopCondition(lastReview, isNewReviewable, existingReviewIds, reviewCount, oldestReviewDate);
          if (!shouldStop && reviewData.serpapi_pagination?.next_page_token) {
            const nextUrl = new URL(reviewData.serpapi_pagination.next);
            const nextParams = Object.fromEntries(nextUrl.searchParams);
            await getReviews(nextParams);
          }
        }
      } catch (error) {
        this.logger.error(error);
        throw new SerpApiException(error.message);
      }
    };

    const params = {
      engine: 'google_maps_reviews',
      place_id: googlePlacesId,
      sort_by: 'newestFirst',
      google_domain: 'google.com',
      hl: 'en',
    };

    await getReviews(params);

    return {
      placeInfo,
      topics,
      reviews: results,
    };
  }

  async refreshReviews(request: IdRequestDTO): Promise<GenericResponse> {
    const reviewables = request.id
      ? await this.reviewableRepository.find({
          where: {
            business: {
              id: request.id,
            },
          },
        })
      : await this.reviewableRepository.find();

    if (reviewables.length === 0) {
      return {
        status: HttpStatus.OK,
        error: undefined,
      };
    }

    for (const reviewable of reviewables) {
      try {
        // Get the last 5 reviews
        const existingReviews = await this.reviewRepository.find({
          where: {
            reviewable: {
              id: reviewable.id,
            },
          },
          order: {
            reviewedAt: 'DESC',
          },
          take: 5,
        });

        const existingReviewIds = new Set(existingReviews.map((r) => r.reviewId));

        const extractedData = await this.extractSerpData(reviewable.googlePlacesId, existingReviewIds);

        if (extractedData.placeInfo) {
          const newReviews: IngestedReview[] = [];
          const newResponses: IngestedReviewResponse[] = [];

          for (const serpReview of extractedData.reviews) {
            // Skip if review already exists
            if (existingReviewIds.has(serpReview.review_id)) continue;

            const review = this.reviewRepository.create({
              reviewable,
              reviewId: serpReview.review_id,
              score: serpReview.rating,
              snippet: serpReview.extracted_snippet?.translated || serpReview.snippet || '',
              reviewedAt: new Date(serpReview.iso_date),
            });

            newReviews.push(review);

            if (serpReview.response) {
              const savedReview = await this.reviewRepository.save(review);

              const response = this.reviewResponseRepository.create({
                review: savedReview,
                snippet: serpReview.response.extracted_snippet?.translated || serpReview.response.snippet,
                respondedAt: new Date(serpReview.response?.iso_date),
              });

              newResponses.push(response);
            }
          }

          if (newReviews.length > 0) {
            await this.reviewRepository.save(newReviews);
          }

          if (newResponses.length > 0) {
            await this.reviewResponseRepository.save(newResponses);
          }

          const snapshot = this.reviewableSnapshotRepository.create({
            reviewable,
            reviewCount: newReviews.length,
            title: extractedData.placeInfo.title,
            address: extractedData.placeInfo.address,
            rating: extractedData.placeInfo.rating,
            type: extractedData.placeInfo.type,
            reviewTopics: extractedData.topics.map((topic) => ({
              id: topic.id,
              keyword: topic.keyword,
              mentions: topic.mentions,
            })),
          });

          await this.reviewableSnapshotRepository.save(snapshot);
        }
      } catch (error) {
        this.logger.error(`Failed to process reviewable ${reviewable.id}`, error instanceof Error ? error.stack : error);
      }
    }

    return {
      status: HttpStatus.OK,
      error: undefined,
    };
  }

  private formatReviewsForPrompt(reviews: IngestedReview[], responseMap: Map<string, IngestedReviewResponse[]>, locationName: string): string {
    if (reviews.length === 0) {
      return `${locationName}: No reviews available.`;
    }

    const formattedReviews = reviews
      .map((review) => {
        const responses = responseMap.get(review.id) || [];
        const responseText = responses.length > 0 ? `\n   Response: "${responses[0].snippet}" (${new Date(responses[0].respondedAt).toLocaleDateString()})` : '';

        return `- Date: ${new Date(review.reviewedAt).toLocaleDateString()}
      Rating: ${review.score}/5
      Review: "${review.snippet}"${responseText}`;
      })
      .join(`\n\n`);

    return `${locationName} (${reviews.length} reviews):
    ${formattedReviews}
    `;
  }

  private async analyzeReviews(prompt: string): Promise<string> {
    try {
      const response = await this.anthropicService.getResponse(
        [
          {
            role: 'user',
            content: `You're an expert restaurant analyst with 20+ years of experience.`,
          },
        ],
        prompt,
        [
          {
            type: 'web_search_20250305',
            name: 'web_search',
          },
        ],
        'claude-4-sonnet-20250514',
      );

      const mainBody = getMainBodyForAnthropicResponse(response)
        .map((block) => block.text)
        .join('\n');

      return mainBody;
    } catch (e) {
      console.error(e);
      return '';
    }
  }

  private async buildAIPrompt(
    business: Business,
    selfReviews: IngestedReview[],
    competitorReviewsByReviewableId: Map<string, IngestedReview[]>,
    competitorReviewables: Reviewable[],
    reviewResponses: IngestedReviewResponse[],
  ): Promise<string> {
    const responseMap = new Map<string, IngestedReviewResponse[]>();
    reviewResponses.forEach((response) => {
      const reviewId = response.review.id;
      if (!responseMap.has(reviewId)) {
        responseMap.set(reviewId, []);
      }
      responseMap.get(reviewId).push(response);
    });

    // Format self reviews
    const selfReviewsFormatted = this.formatReviewsForPrompt(selfReviews, responseMap, 'My Restaurant');

    // Format competitor reviews
    const competitorReviewsFormatted: string[] = [];
    for (const [reviewableId, reviews] of competitorReviewsByReviewableId) {
      const competitor = competitorReviewables.find((r) => r.id === reviewableId);
      const competitorName = competitor?.description || 'Competitor';
      competitorReviewsFormatted.push(this.formatReviewsForPrompt(reviews, responseMap, competitorName));
    }

    const prompt = `You're an expert business analyst in the restaurant industry working for ${business.name} located at ${business.streetAddress}, ${business.city}, ${
      business.postalCode
    }. Your goal is to generate a detailed Weekly Guest Feedback Pulse - Me vs Competition for my location.

    Here are all reviews for my location and my primary competitors:
    
    **My Restaurant Reviews:**
    ${selfReviewsFormatted}
    
    **Competitor Reviews:**
    ${competitorReviewsFormatted.join('\n\n')}
    
    Using this information, can you please:
    
    Aggregate & Quantify
       - Count total reviews and average star rating.  
       - Compute the distribution of ratings (e.g., % 5-star, 4-star, etc.).
    
    Thematic Sentiment Analysis
       - Automatically group comments into key themes (e.g., Food Quality, Service, Ambiance, Price, Cleanliness, Wait Times).  
       - For each theme, report:  
         - Number of mentions  
         - Average sentiment (positive/neutral/negative)  
         - Representative excerpts (one strongly positive and one strongly negative, if available).
    
    AI-Generated Insights
       - Identify the top 3 strengths (themes with consistently high praise).  
       - Identify the top 3 areas for improvement (themes with frequent complaints or lower sentiment).  
       - Note any emerging patterns or one-off but critical issues (e.g., a recent surge of comments about delivery mistakes).
    
    AI-Suggested SMART Goals
       For each area of improvement, propose a **Specific, Measurable, Achievable, Relevant, Time-bound** goal. For example:  
       - "Reduce mentions of 'slow service' by 50% within the next three months by retraining staff and optimizing table assignments."  
       - "Increase positive comments on 'food temperature' from 60% to 85% within eight weeks by adjusting kitchen plating procedures."
    
    Additional Recommendations
       - Any quick wins (low-effort, high-impact fixes).  
       - Longer-term strategic suggestions (e.g., menu tweaks, staff incentives, ambiance upgrades).  
       - Benchmarking: if possible, compare average rating to similar nearby venues.
    
    Output Format
       Organize your report into clear sections with headings:  
       - Overview & Metrics  
       - Thematic Sentiment Breakdown  
       - Key Strengths  
       - Areas for Improvement  
       - SMART Goals  
       - My average rating change vs. 7-day baseline.  
       - 2 most common themes (↑ / ↓).  
       -  One positive & one negative quote (≤ 25 words each).  
       -  Competitor with biggest rating dip or surge and what people are saying about them and how they compare vs me. Give a bit of a breakdown and analysis here so I can understand this h2h.
       - Analysis of my primary competitors reviews and anything notable from there over the course of the past week. Highlight things I should know about my competitors based on guest reviews. Identify any trends if possible.
       - Additional Recommendations  
    
    Formatting:
    Skip the preamble in your output. The result should start directly with the title header.`;

    return prompt;
  }

  private extractSupportingData(aiResponse: string): any[] {
    return [
      {
        type: 'ai_analysis',
        generatedAt: new Date(),
        analysisText: aiResponse,
      },
    ];
  }

  private async processBusinessInsights(businessId: string) {
    const allReviews = await this.reviewRepository.find({
      where: {
        reviewable: {
          business: {
            id: businessId,
          },
        },
      },
      relations: ['reviewable'],
      order: {
        reviewedAt: 'DESC',
      },
    });

    // Get business details
    const business = await this.businessClient.getBusinessById(businessId);

    const reviewables = await this.reviewableRepository.find({
      where: {
        business: {
          id: businessId,
        },
      },
    });

    // Seperate reviews by self vs competitor
    const selfReviewable = reviewables.find((r) => r.isSelf);
    const competitorReviewables = reviewables.filter((r) => !r.isSelf);

    const reviewIds = allReviews.map((r) => r.id);
    const reviewResponses = await this.reviewResponseRepository.find({
      where: {
        review: {
          id: In(reviewIds),
        },
      },
      relations: {
        review: true,
      },
    });

    // Group reviews by reviewable
    const selfReviews = allReviews.filter((r) => r.reviewable.id === selfReviewable?.id);

    const competitorReviewsByReviewableId = new Map<string, IngestedReview[]>();

    competitorReviewables.forEach((competitor) => {
      const reviews = allReviews.filter((r) => r.reviewable.id === competitor?.id);
      if (reviews.length > 0 && competitor?.id) {
        competitorReviewsByReviewableId.set(competitor?.id, reviews);
      }
    });

    // Format data for AI prompt
    const prompt = await this.buildAIPrompt(business, selfReviews, competitorReviewsByReviewableId, competitorReviewables, reviewResponses);

    // Analyze reviews
    const aiResponse = await this.analyzeReviews(prompt);

    // Create Insight
    const insight = await this.insightRepository.save({
      title: `Weekly Guest Feedback Pulse - ${business.name}`,
      body: aiResponse,
      supportingData: this.extractSupportingData(aiResponse),
      business: {
        id: business.id,
      },
    });

    const users = await this.userRepository.find({
      where: {
        staffMembers: {
          business: {
            id: businessId,
          },
        },
      },
    });

    const messages = users.map((user) =>
      this.insightInboxRepository.create({
        user: {
          id: user.id,
        },
        insight: {
          id: insight.id,
        },
        readAt: null,
        isArchived: false,
        isStarred: false,
      }),
    );

    await this.insightInboxRepository.save(messages);
  }

  async generateReviewInsights(request: OptionalIdRequestDTO): Promise<GenericResponse> {
    if (!request.id) {
      const allBusinesses = await this.businessClient.getAllBusinesses();
      await Promise.all(
        allBusinesses.map(async (business) => {
          await this.processBusinessInsights(business.id);
        }),
      );
    } else {
      // Process single business
      await this.processBusinessInsights(request.id);
    }

    return {
      status: HttpStatus.OK,
      error: undefined,
    };
  }
}
