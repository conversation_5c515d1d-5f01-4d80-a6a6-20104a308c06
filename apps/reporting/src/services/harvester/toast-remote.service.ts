'use-strict';
import { Inject, Logger } from '@nestjs/common';
import { RemoteHarvesterJob } from '../../entities/RemoteHarvesterJob.entity';
import { z } from 'zod';
import { InjectRepository } from '@nestjs/typeorm';
import { ReportDataIngestionService } from '../ingest.service';
import dayjs from 'dayjs';
import { Repository } from 'typeorm';

const RESULT_BASE64_DATA = z.object({
  success: z.boolean(),
  error: z.string().optional(),
  data: z.string().base64().optional(),
});

const RESULT_JSON_DATA = z.object({
  success: z.boolean(),
  error: z.string().optional(),
  data: z.string(),
});

const SALES_DATA = z.object({
  orders: z.string().base64(),
  payments: z.string().base64(),
  items: z.string().base64(),
});

export class ToastRemoteHarvesterService {
  private readonly logger = new Logger(ToastRemoteHarvesterService.name);

  @Inject(ReportDataIngestionService)
  private readonly ingestionService: ReportDataIngestionService;

  @InjectRepository(RemoteHarvesterJob)
  private readonly remoteHarvesterJobRepository: Repository<RemoteHarvesterJob>;

  async ingestClockPunches(job: RemoteHarvesterJob, dataStr: string): Promise<void> {
    try {
      if (!job.business) throw new Error('Business not found');

      const parsedData = RESULT_BASE64_DATA.safeParse(JSON.parse(dataStr));
      if (!parsedData.success) throw new Error('Invalid data');
      const { success, error, data } = parsedData.data;

      if (success && data) {
        await this.ingestionService.ingestData(
          {
            businessId: job.business.id,
            data: data,
            dataFormat: 'csv',
            dataSource: 'toast',
            dataType: 'clock_punches',
          },
          job.id,
        );
        await this.remoteHarvesterJobRepository.update(job.id, {
          status: 'completed',
          resolvedAt: dayjs().toDate(),
        });
      } else if (error) {
        await this.remoteHarvesterJobRepository.update(job.id, {
          status: 'failed',
          errorMessage: error,
          resolvedAt: dayjs().toDate(),
        });
      } else {
        throw new Error('Invalid data');
      }
    } catch (error) {
      await this.remoteHarvesterJobRepository.update(job.id, {
        status: 'failed',
        errorMessage: error.message ?? 'Unknown error',
        resolvedAt: dayjs().toDate(),
      });
    }
  }

  async ingestSales(job: RemoteHarvesterJob, dataStr: string): Promise<void> {
    try {
      if (!job.business) throw new Error('Business not found');

      const parsedData = RESULT_JSON_DATA.safeParse(JSON.parse(dataStr));
      if (!parsedData.success) throw new Error('Data does not match expected format');
      const { success, error, data } = parsedData.data;

      if (success && data) {
        const parsedSalesData = SALES_DATA.safeParse(JSON.parse(data));
        if (parsedSalesData.data) {
          const { orders, payments, items } = parsedSalesData.data;
          await this.ingestionService.ingestData(
            {
              businessId: job.business.id,
              data: orders,
              dataFormat: 'csv',
              dataSource: 'toast',
              dataType: 'order_detail',
            },
            job.id,
          );
          await this.ingestionService.ingestData(
            {
              businessId: job.business.id,
              data: payments,
              dataFormat: 'csv',
              dataSource: 'toast',
              dataType: 'payment_detail',
            },
            job.id,
          );
          await this.ingestionService.ingestData(
            {
              businessId: job.business.id,
              data: items,
              dataFormat: 'csv',
              dataSource: 'toast',
              dataType: 'item_selection_detail',
            },
            job.id,
          );
          await this.remoteHarvesterJobRepository.update(job.id, {
            status: 'completed',
            resolvedAt: dayjs().toDate(),
          });
        } else {
          throw new Error('Could not parse sales data');
        }
      } else if (error) {
        await this.remoteHarvesterJobRepository.update(job.id, {
          status: 'failed',
          errorMessage: error,
          resolvedAt: dayjs().toDate(),
        });
      } else {
        throw new Error('Could not parse results data');
      }
    } catch (error) {
      await this.remoteHarvesterJobRepository.update(job.id, {
        status: 'failed',
        errorMessage: error.message ?? 'Unknown error',
        resolvedAt: dayjs().toDate(),
      });
    }
  }
}
