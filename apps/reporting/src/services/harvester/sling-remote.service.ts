'use-strict';
import { Inject, Logger } from '@nestjs/common';
import { RemoteHarvesterJob } from '../../entities/RemoteHarvesterJob.entity';
import { z } from 'zod';
import { InjectRepository } from '@nestjs/typeorm';
import { ReportDataIngestionService } from '../ingest.service';
import dayjs from 'dayjs';
import { Repository } from 'typeorm';

const RESULT_BASE64_DATA = z.object({
  success: z.boolean(),
  error: z.string().optional(),
  data: z.string().base64().optional(),
});

export class SlingRemoteHarvesterService {
  private readonly logger = new Logger(SlingRemoteHarvesterService.name);

  @Inject(ReportDataIngestionService)
  private readonly ingestionService: ReportDataIngestionService;

  @InjectRepository(RemoteHarvesterJob)
  private readonly remoteHarvesterJobRepository: Repository<RemoteHarvesterJob>;

  async ingestShiftSchedule(job: RemoteHarvesterJob, dataStr: string): Promise<void> {
    try {
      if (!job.business) throw new Error('Business not found');

      const parsedData = RESULT_BASE64_DATA.safeParse(JSON.parse(dataStr));
      if (!parsedData.success) throw new Error('Invalid data');
      const { success, error, data } = parsedData.data;

      if (success && data) {
        await this.ingestionService.ingestData(
          {
            businessId: job.business.id,
            data: data,
            dataFormat: 'json',
            dataSource: 'sling',
            dataType: 'scheduled_shifts',
          },
          job.id,
        );
        await this.remoteHarvesterJobRepository.update(job.id, {
          status: 'completed',
          resolvedAt: dayjs().toDate(),
        });
      } else if (error) {
        await this.remoteHarvesterJobRepository.update(job.id, {
          status: 'failed',
          errorMessage: error,
          resolvedAt: dayjs().toDate(),
        });
      } else {
        throw new Error('Invalid data');
      }
    } catch (error) {
      this.logger.error(error, 'Error ingesting shift schedule');
      await this.remoteHarvesterJobRepository.update(job.id, {
        status: 'failed',
        errorMessage: error.message ?? 'Unknown error',
        resolvedAt: dayjs().toDate(),
      });
    }
  }
}
