import { ConfigService } from '@nestjs/config';
import {
  DataLinkAlreadyExistsException,
  IncorrectDataSourceTypeToProcessException,
  LinkInfoDoesNotMatchLinkTypeException,
  NoSftpUrlException,
  NoToastIdException,
} from '@tangopay/shared/errors';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DataLink } from '../../entities/DataLink.entity';
import { LinkDataSourceRequestDto } from '../../dtos/harvest.dto';
import { Business } from 'apps/business/src/proto/proto/business/business.pb';
import { Logger } from '@tangopay/shared/logger/logger';
import { DataHarvest, HarvestedFile } from '../../entities/DataHarvest.entity';
import SftpClient from 'ssh2-sftp-client';
import { GenericResponse } from '../../proto/reporting/shared.pb';
import { HttpStatus, Inject } from '@nestjs/common';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import utc from 'dayjs/plugin/utc';
import { StorageService } from '@tangopay/shared';
import { createHash, randomUUID } from 'crypto';
import { ReportDataIngestionService } from '../ingest.service';

dayjs.extend(customParseFormat);
dayjs.extend(utc);

const RETRIVE_FILES: Record<string, RegExp | string> = {
  menu: RegExp('MenuExportV2.*\\.json'),
  order: 'OrderDetails.csv',
  payment: 'PaymentDetails.csv',
  check: 'CheckDetails.csv',
  products: 'ItemSelectionDetails.csv',
  punches: 'TimeEntries.csv',
};

const PROCESS_ORDER = ['menu', 'order', 'payment', 'check', 'products', 'punches'];

const DATA_TYPE_MAP = {
  menu: 'menu',
  order: 'order_detail',
  payment: 'payment_detail',
  check: 'check_detail',
  products: 'item_selection_detail',
  punches: 'clock_punches',
};

export class ReportDataToastHarvesterService {
  private readonly logger = new Logger(ReportDataToastHarvesterService.name);

  @Inject(StorageService)
  private readonly storageService: StorageService;

  @Inject(ConfigService)
  private readonly configService: ConfigService;

  @Inject(ReportDataIngestionService)
  private readonly ingestionService: ReportDataIngestionService;

  @InjectRepository(DataLink)
  private readonly dataLinkRepository: Repository<DataLink>;

  @InjectRepository(DataHarvest)
  private readonly dataHarvestRepository: Repository<DataHarvest>;

  @InjectRepository(HarvestedFile)
  private readonly harvestedFileRepository: Repository<HarvestedFile>;

  async createToastDataLink(request: LinkDataSourceRequestDto, business: Business): Promise<GenericResponse> {
    const toastId = request.data.find((d) => d.key === 'toastId')?.value;

    if (!toastId) throw new NoToastIdException();

    const sftpHost = request.data.find((d) => d.key === 'sftpHost')?.value;
    const sftpUsername = request.data.find((d) => d.key === 'sftpUsername')?.value;

    if (!sftpHost || !sftpUsername) throw new NoSftpUrlException();

    const existing = await this.dataLinkRepository.findOne({
      where: {
        business: {
          id: business.id,
        },
        source: 'toast',
      },
    });

    if (existing) throw new DataLinkAlreadyExistsException(business.id, 'toast');

    const dataLink = this.dataLinkRepository.create({
      source: 'toast',
      business: {
        id: business.id,
      },
      cadence: 'daily',
      linkInfo: {
        type: 'toast',
        toastId,
        sftpHost,
        sftpUsername,
      },
    });

    const saved = await this.dataLinkRepository.save(dataLink);

    return {
      status: HttpStatus.OK,
      error: null,
    };
  }

  private async saveFileForHarvestToGCS(dataLink: DataLink, harvest: DataHarvest, date: Date, fileType: string, fileName: string, buffer: Buffer) {
    const type = fileName.endsWith('.json') ? 'application/json' : 'text/csv';
    const gcsFile = await this.storageService.save(`/ingest/${dataLink.id}/${dayjs(date).format('YYYYMMDD')}/${fileName}`, buffer, [
      {
        contentType: type,
      },
    ]);

    const file = this.harvestedFileRepository.create({
      fileName,
      gcsPath: gcsFile.path,
      fileContentType: fileType,
      dataHarvest: {
        id: harvest.id,
      },
    });

    return this.harvestedFileRepository.save(file);
  }

  private async ingestDate(dataLink: DataLink, date: Date) {
    if (dataLink.linkInfo.type !== 'toast') throw new LinkInfoDoesNotMatchLinkTypeException(dataLink.id);
    const toastId = dataLink.linkInfo.toastId;
    const sftpHost = dataLink.linkInfo.sftpHost;
    const sftpUsername = dataLink.linkInfo.sftpUsername;

    const sftpClient = new SftpClient('toast-sftp');
    await sftpClient.connect({
      host: sftpHost,
      username: sftpUsername,
      privateKey: this.configService.get('TOAST_SFTP_PRIVATE_KEY'),
    });

    const harvest = this.dataHarvestRepository.create({
      dataLink: {
        id: dataLink.id,
      },
      deduplicationKey: dayjs(date).format('YYYYMMDD'),
      status: 'pending',
    });

    const savedHarvest = await this.dataHarvestRepository.save(harvest);

    const fileList = await sftpClient.list(`/${toastId}/${dayjs(date).format('YYYYMMDD')}`);

    const fileNames: Record<string, SftpClient.FileInfo> = Object.keys(RETRIVE_FILES).reduce((acc, key) => {
      const val = RETRIVE_FILES[key];
      if (typeof val === 'string') {
        acc[key] = fileList.find((f) => f.name === val);
      } else {
        acc[key] = fileList.find((f) => val.test(f.name));
      }
      return acc;
    }, {});

    const files = await Promise.all(
      Object.entries(fileNames).map(async ([key, file]) => {
        if (!file) return null;
        const content = await sftpClient.get(`/${toastId}/${dayjs(date).format('YYYYMMDD')}/${file.name}`);
        if (Buffer.isBuffer(content)) {
          return this.saveFileForHarvestToGCS(dataLink, savedHarvest, date, key, file.name, content);
        }
        return null;
      }),
    );

    await this.dataHarvestRepository.update(savedHarvest.id, {
      status: 'success',
      harvestedAt: new Date(),
    });
  }

  async harvestAllAndInitialiseDataLink(dataLink: DataLink): Promise<GenericResponse> {
    if (dataLink.linkInfo.type !== 'toast') throw new LinkInfoDoesNotMatchLinkTypeException(dataLink.id);

    const toastId = dataLink.linkInfo.toastId;
    const sftpHost = dataLink.linkInfo.sftpHost;
    const sftpUsername = dataLink.linkInfo.sftpUsername;

    const previousHarvests = await this.dataHarvestRepository.find({
      where: {
        dataLink: {
          id: dataLink.id,
        },
      },
    });

    const sftpClient = new SftpClient('toast-sftp');
    await sftpClient.connect({
      host: sftpHost,
      username: sftpUsername,
      privateKey: this.configService.get('TOAST_SFTP_PRIVATE_KEY'),
    });

    const files = await sftpClient.list(`/${toastId}`);

    const dates = files.filter((f) => f.type === 'd').map((f) => dayjs(f.name, 'YYYYMMDD').toDate());
    sftpClient.end();

    const datesToHarvest = dates.filter((d) => !previousHarvests.some((h) => h.deduplicationKey === dayjs(d).format('YYYYMMDD')));

    for (const date of datesToHarvest.sort((a, b) => dayjs(a).diff(dayjs(b)))) {
      await this.ingestDate(dataLink, date);
    }

    if (datesToHarvest.length > 0) {
      await this.dataLinkRepository.update(dataLink.id, {
        initialisedAt: new Date(),
      });
    }

    return {
      status: HttpStatus.OK,
      error: null,
    };
  }

  async processHarvest(harvest: DataHarvest) {
    if (harvest.dataLink.source !== 'toast') throw new IncorrectDataSourceTypeToProcessException(harvest.dataLink.id, 'toast', harvest.dataLink.source);

    await this.dataHarvestRepository.update(harvest.id, {
      startedProcessingAt: new Date(),
    });

    const foodAndBevEnterprise = harvest.dataLink.business?.enterpriseLinks.find((el) => el.foodAndBev)?.enterprise;

    for (const nextToProcess of PROCESS_ORDER) {
      const file = harvest.harvestedFiles.find((f) => f.fileContentType === nextToProcess);
      if (!file) continue;

      if (!foodAndBevEnterprise && nextToProcess === 'menu') continue;

      const data = await this.storageService.get(file.gcsPath);

      const b64Data = data.buffer.toString('base64');

      await this.ingestionService.ingestData(
        {
          businessId: nextToProcess !== 'menu' ? harvest.dataLink.business.id : null,
          enterpriseId: nextToProcess === 'menu' ? foodAndBevEnterprise.id : null,
          data: b64Data,
          dataSource: 'toast',
          dataType: DATA_TYPE_MAP[nextToProcess],
          // technically redundant but there you go
          dataFormat: 'csv',
        },
        harvest.id,
      );
    }

    await this.dataHarvestRepository.update(harvest.id, {
      processedAt: new Date(),
    });
  }
}
