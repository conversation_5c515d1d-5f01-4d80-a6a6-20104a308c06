import { HttpStatus, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from '@tangopay/shared/logger/logger';
import { DataLink } from '../../entities/DataLink.entity';
import { Repository } from 'typeorm';
import { ImmediateHarvestRequestDto, LinkDataSourceRequestDto } from '../../dtos/harvest.dto';
import { Business } from 'apps/menu/src/proto/proto/menu-management.pb';
import { GenericResponse } from '../../proto/reporting/shared.pb';
import {
  IncorrectDataSourceTypeToProcessException,
  IncorrectHarvestTypeException,
  LinkInfoDoesNotMatchLinkTypeException,
  MarginEdgeApiException,
  NoMarginEdgeApiKeyException,
  NoMarginEdgeRestaurantUnitIdException,
  NoMarginEdgeTokenException,
} from '@tangopay/shared/errors';
import { gunzip, gzip } from 'zlib';
import { DataHarvest, HarvestedBinaryData, HarvestedProducts, HarvestedPurchaseReport } from '../../entities/DataHarvest.entity';
import { ReportDataIngestionService } from '../ingest.service';
import { FullRecipe, PurchaseableProduct, PurchaseFromVendorResponse, PurchaseReportResponse } from './marginedge';
import dayjs from 'dayjs';
import { randomUUID } from 'crypto';

export class ReportDataMarginEdgeHarvesterService {
  private readonly logger = new Logger(ReportDataMarginEdgeHarvesterService.name);

  @Inject(ConfigService)
  private readonly configService: ConfigService;

  @Inject(ReportDataIngestionService)
  private readonly ingestionService: ReportDataIngestionService;

  @InjectRepository(DataLink)
  private readonly dataLinkRepository: Repository<DataLink>;

  @InjectRepository(DataHarvest)
  private readonly dataHarvestRepository: Repository<DataHarvest>;

  @InjectRepository(HarvestedBinaryData)
  private readonly harvestedBinaryDataRepository: Repository<HarvestedBinaryData>;

  async createMarginEdgeDataLink(request: LinkDataSourceRequestDto, business: Business): Promise<GenericResponse> {
    const marginEdgeRestaurantUnitId = request.data.find((d) => d.key === 'marginEdgeRestaurantUnitId')?.value;
    if (!marginEdgeRestaurantUnitId) throw new NoMarginEdgeRestaurantUnitIdException();

    const marginEdgeApiKey = request.data.find((d) => d.key === 'marginEdgeApiKey')?.value;
    if (!marginEdgeApiKey) throw new NoMarginEdgeApiKeyException();

    const dataLink = this.dataLinkRepository.create({
      source: 'marginEdge',
      business: {
        id: business.id,
      },
      cadence: 'daily',
      linkInfo: {
        type: 'marginEdge',
        marginEdgeRestaurantUnitId,
        marginEdgeApiKey,
      },
    });

    await this.dataLinkRepository.save(dataLink);

    return {
      status: HttpStatus.OK,
      error: null,
    };
  }

  async harvestAllAndInitialiseDataLink(dataLink: DataLink): Promise<GenericResponse> {
    if (dataLink.linkInfo.type !== 'marginEdge') throw new LinkInfoDoesNotMatchLinkTypeException(dataLink.id);

    const harvest = this.dataHarvestRepository.create({
      dataLink: {
        id: dataLink.id,
      },
      status: 'pending',
      deduplicationKey: dayjs(new Date()).format('YYYYMMDD'),
    });

    const savedHarvest = await this.dataHarvestRepository.save(harvest);

    await this.dataHarvestRepository.update(savedHarvest.id, {
      harvestedAt: new Date(),
      status: 'success',
    });

    return {
      status: HttpStatus.OK,
      error: null,
    };
  }

  async processHarvest(harvest: DataHarvest): Promise<GenericResponse> {
    const dataLink = harvest.dataLink;
    if (dataLink.source !== 'marginEdge') throw new IncorrectDataSourceTypeToProcessException(dataLink.id, 'marginEdge', dataLink.source);

    await this.dataHarvestRepository.update(harvest.id, {
      startedProcessingAt: new Date(),
    });

    try {
      for (const binaryData of harvest.harvestedBinaryData) {
        const decompressed = await new Promise<Buffer>((res, rej) =>
          gunzip(binaryData.data as unknown as Uint8Array, (err, buff) => {
            if (err) rej(err);
            else res(buff);
          }),
        );
        if (binaryData.dataType === 'recipes') {
          await this.ingestionService.ingestData(
            {
              businessId: dataLink.business.id,
              data: decompressed.toString('base64'),
              dataSource: 'marginEdge',
              dataType: 'recipes',
              dataFormat: 'json',
            },
            harvest.id,
          );
        } else if (binaryData.dataType === 'inventory_orders') {
          await this.ingestionService.ingestData(
            {
              businessId: dataLink.business.id,
              data: decompressed.toString('base64'),
              dataSource: 'marginEdge',
              dataType: 'inventory_orders',
              dataFormat: 'json',
            },
            harvest.id,
          );
        } else if (binaryData.dataType === 'inventory_products') {
          await this.ingestionService.ingestData(
            {
              businessId: dataLink.business.id,
              data: decompressed.toString('base64'),
              dataSource: 'marginEdge',
              dataType: 'inventory_products',
              dataFormat: 'json',
            },
            harvest.id,
          );
        }
      }

      this.dataHarvestRepository.update(harvest.id, {
        processedAt: new Date(),
        status: 'success',
      });

      return {
        status: HttpStatus.OK,
        error: null,
      };
    } catch (err) {
      this.dataHarvestRepository.update(harvest.id, {
        processedAt: new Date(),
        status: 'error',
        errorMessage: JSON.stringify(err, null, 2),
      });
      return {
        status: HttpStatus.INTERNAL_SERVER_ERROR,
        error: err.message || 'Unknown error',
      };
    }
  }

  async performImmediateHarvest(request: ImmediateHarvestRequestDto, dataLink: DataLink) {
    if (dataLink.source !== 'marginEdge') throw new IncorrectDataSourceTypeToProcessException(dataLink.id, 'marginEdge', dataLink.source);
    if (dataLink.linkInfo.type !== 'marginEdge') throw new IncorrectDataSourceTypeToProcessException(dataLink.id, 'marginEdge', dataLink.source);
    if (request.harvestType !== 'recipes' && request.harvestType !== 'inventory_orders' && request.harvestType !== 'inventory_products')
      throw new IncorrectHarvestTypeException(request.harvestType, ['recipes', 'inventory_orders', 'inventory_products']);

    const marginEdgeToken = request.data.find((d) => d.key === 'marginEdgeToken')?.value;
    if (!marginEdgeToken) throw new NoMarginEdgeTokenException();

    const unitId = dataLink.linkInfo.marginEdgeRestaurantUnitId;
    const dataHarvest = this.dataHarvestRepository.create({
      dataLink: {
        id: dataLink.id,
      },
      status: 'pending',
      deduplicationKey: randomUUID(),
    });

    if (request.harvestType === 'recipes') {
      dataHarvest.parameters = JSON.stringify({
        harvestType: request.harvestType,
        marginEdgeRestaurantUnitId: unitId,
      });
      const savedDataHarvest = await this.dataHarvestRepository.save(dataHarvest);
      await this.harvestRecipes(savedDataHarvest, unitId, marginEdgeToken);
    } else if (request.harvestType === 'inventory_orders') {
      const endDateString = request.data.find((d) => d.key === 'endDate')?.value;
      const startDateString = request.data.find((d) => d.key === 'startDate')?.value;
      const endDate = endDateString ? dayjs(endDateString) : dayjs();
      const startDate = startDateString ? dayjs(startDateString) : endDate.subtract(1, 'year');
      dataHarvest.parameters = JSON.stringify({
        harvestType: request.harvestType,
        startDate: startDate.format('YYYY-MM-DD'),
        endDate: endDate.format('YYYY-MM-DD'),
      });
      const savedDataHarvest = await this.dataHarvestRepository.save(dataHarvest);
      await this.harvestOrders(savedDataHarvest, marginEdgeToken, startDate, endDate);
    } else if (request.harvestType === 'inventory_products') {
      const endDateString = request.data.find((d) => d.key === 'endDate')?.value;
      const startDateString = request.data.find((d) => d.key === 'startDate')?.value;
      const endDate = endDateString ? dayjs(endDateString) : dayjs();
      const startDate = startDateString ? dayjs(startDateString) : endDate.subtract(1, 'year');
      dataHarvest.parameters = JSON.stringify({
        harvestType: request.harvestType,
        startDate: startDate.format('YYYY-MM-DD'),
        endDate: endDate.format('YYYY-MM-DD'),
      });
      const savedDataHarvest = await this.dataHarvestRepository.save(dataHarvest);
      await this.harvestProducts(savedDataHarvest, marginEdgeToken, startDate, endDate);
    }
  }

  private async harvestRecipes(dataHarvest: DataHarvest, marginEdgeRestaurantUnitId: string, marginEdgeToken: string): Promise<void> {
    const harvestedRecipes: FullRecipe[] = [];
    try {
      const recipesData = await fetch(`https://app.marginedge.com/api/recipes`, {
        headers: {
          Authorization: `Bearer ${marginEdgeToken}`,
        },
      });
      this.logger.debug(`Fetch recipes: ${recipesData.status}`);
      if (!recipesData.ok) {
        this.logger.error(`Recipes data returned status ${recipesData.status}`);
        this.logger.error(await recipesData.text());
        throw new MarginEdgeApiException(`Recipes data returned status ${recipesData.status}`);
      }

      const unitId = parseInt(marginEdgeRestaurantUnitId);

      const recipes = (await recipesData.json()) as FullRecipe[];

      for (const recipe of recipes) {
        if (recipe.inactive) continue;
        // If the recipe is restricted to a specific unit, and the unit is not the one we're interested in, skip it
        if (recipe.restrictTenants && !recipe.restrictiveRestaurantUnits.includes(unitId)) continue;

        const recipeData = await fetch(`https://app.marginedge.com/api/recipes/${recipe.id}`, {
          headers: {
            Authorization: `Bearer ${marginEdgeToken}`,
          },
        });

        this.logger.debug(`Fetch recipe ${recipe.id}: ${recipeData.status}`);
        if (recipeData.status !== 200) {
          this.logger.error(`Recipe ${recipe.id} returned status ${recipeData.status}`);
          this.logger.debug(recipe);
        }

        const recipeDataJson = await recipeData.json();

        harvestedRecipes.push(recipeDataJson);
      }
    } catch (err) {
      this.logger.error(err);
      throw new MarginEdgeApiException(err.message);
    }

    const compressed = await new Promise<Buffer>((res, rej) =>
      gzip(JSON.stringify(harvestedRecipes), (err, buff) => {
        if (err) rej(err);
        else res(buff);
      }),
    );

    const harvestedBinaryData = this.harvestedBinaryDataRepository.create({
      dataHarvest: {
        id: dataHarvest.id,
      },
      data: compressed,
      dataType: 'recipes',
    });

    await this.harvestedBinaryDataRepository.save(harvestedBinaryData);

    await this.dataHarvestRepository.update(dataHarvest.id, {
      harvestedAt: new Date(),
      status: 'success',
    });
  }

  private async harvestOrders(dataHarvest: DataHarvest, marginEdgeToken: string, startDate: dayjs.Dayjs, endDate: dayjs.Dayjs): Promise<void> {
    const queryParams = new URLSearchParams({
      startDate: startDate.format('YYYY-MM-DD'),
      endDate: endDate.format('YYYY-MM-DD'),
    });
    const harvestedPurchaseReport: HarvestedPurchaseReport = {
      purchasedItems: [],
    };
    try {
      const ordersData = await fetch(`https://app.marginedge.com/api/orders/purchaseReport/v2?${queryParams.toString()}`, {
        headers: {
          Authorization: `Bearer ${marginEdgeToken}`,
        },
      });
      if (!ordersData.ok) {
        this.logger.error(`Orders data returned status ${ordersData.status}`);
        this.logger.error(await ordersData.text());
        throw new MarginEdgeApiException(`Orders data returned status ${ordersData.status}`);
      }
      const orders = (await ordersData.json()) as PurchaseReportResponse;

      const innerQueryParams = new URLSearchParams({
        startDt: startDate.format('YYYY-MM-DD'),
        endDt: endDate.format('YYYY-MM-DD'),
        purchaseReport: 'true',
      });
      for (const purchaseReportRow of orders.purchaseReportRows) {
        const purchasesData = await fetch(
          `https://app.marginedge.com/api/performance/purchaseReportDetails/${purchaseReportRow.conceptProductId}?${innerQueryParams.toString()}`,
          {
            headers: {
              Authorization: `Bearer ${marginEdgeToken}`,
            },
          },
        );
        if (!purchasesData.ok) {
          this.logger.error(`Purchases data returned status ${purchasesData.status}`);
          this.logger.error(await purchasesData.text());
          throw new MarginEdgeApiException(`Purchases data returned status ${purchasesData.status}`);
        }
        const purchases = (await purchasesData.json()) as PurchaseFromVendorResponse;
        harvestedPurchaseReport.purchasedItems.push({
          ...purchaseReportRow,
          purchases,
        });
      }
    } catch (err) {
      this.logger.error(err);
      throw new MarginEdgeApiException(err.message);
    }

    const compressed = await new Promise<Buffer>((res, rej) =>
      gzip(JSON.stringify(harvestedPurchaseReport), (err, buff) => {
        if (err) rej(err);
        else res(buff);
      }),
    );

    const harvestedBinaryData = this.harvestedBinaryDataRepository.create({
      dataHarvest: {
        id: dataHarvest.id,
      },
      data: compressed,
      dataType: 'inventory_orders',
    });

    await this.harvestedBinaryDataRepository.save(harvestedBinaryData);

    await this.dataHarvestRepository.update(dataHarvest.id, {
      harvestedAt: new Date(),
      status: 'success',
    });
  }

  private async harvestProducts(dataHarvest: DataHarvest, marginEdgeToken: string, startDate: dayjs.Dayjs, endDate: dayjs.Dayjs): Promise<void> {
    const productsQueryParams = new URLSearchParams({
      includeItemCounts: 'true',
      includeCategoryOverrides: 'true',
    });
    const purchaseReportQueryParams = new URLSearchParams({
      startDate: startDate.format('YYYY-MM-DD'),
      endDate: endDate.format('YYYY-MM-DD'),
    });
    const harvestedProducts: HarvestedProducts = {
      products: [],
      purchases: [],
    };
    try {
      const response = await fetch(`https://app.marginedge.com/api/products?${productsQueryParams.toString()}`, {
        headers: {
          Authorization: `Bearer ${marginEdgeToken}`,
        },
      });
      if (!response.ok) {
        this.logger.error(`Products data returned status ${response.status}`);
        this.logger.error(await response.text());
        throw new MarginEdgeApiException(`Products data returned status ${response.status}`);
      }
      harvestedProducts.products = (await response.json()) as PurchaseableProduct[];
      const ordersResponse = await fetch(`https://app.marginedge.com/api/orders/purchaseReport/v2?${purchaseReportQueryParams.toString()}`, {
        headers: {
          Authorization: `Bearer ${marginEdgeToken}`,
        },
      });
      if (!ordersResponse.ok) {
        this.logger.error(`Orders data returned status ${ordersResponse.status}`);
        this.logger.error(await ordersResponse.text());
        throw new MarginEdgeApiException(`Orders data returned status ${ordersResponse.status}`);
      }
      const orders = (await ordersResponse.json()) as PurchaseReportResponse;
      harvestedProducts.purchases = orders.purchaseReportRows;
    } catch (err) {
      this.logger.error(err);
      throw new MarginEdgeApiException(err.message);
    }

    const compressed = await new Promise<Buffer>((res, rej) =>
      gzip(JSON.stringify(harvestedProducts), (err, buff) => {
        if (err) rej(err);
        else res(buff);
      }),
    );

    const harvestedBinaryData = this.harvestedBinaryDataRepository.create({
      dataHarvest: {
        id: dataHarvest.id,
      },
      data: compressed,
      dataType: 'inventory_products',
    });

    await this.harvestedBinaryDataRepository.save(harvestedBinaryData);

    await this.dataHarvestRepository.update(dataHarvest.id, {
      harvestedAt: new Date(),
      status: 'success',
    });
  }
}
