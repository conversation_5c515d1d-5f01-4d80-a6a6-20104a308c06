import { HttpStatus, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Logger } from '@tangopay/shared/logger/logger';
import { DataLink } from '../../entities/DataLink.entity';
import { Repository } from 'typeorm';
import { ImmediateHarvestRequestDto, LinkDataSourceRequestDto } from '../../dtos/harvest.dto';
import { GenericResponse } from '../../proto/reporting/shared.pb';
import {
  IncorrectDataSourceTypeToProcessException,
  IncorrectHarvestTypeException,
  LinkInfoDoesNotMatchLinkTypeException,
  MarginEdgeApiException,
  NoMarginEdgeApiKeyException,
  NoMarginEdgeRestaurantUnitIdException,
} from '@tangopay/shared/errors';
import axios from 'axios';
import { gunzip, gzip } from 'zlib';
import { DataHarvest, HarvestedBinaryData, HarvestedInventoryOrders } from '../../entities/DataHarvest.entity';
import dayjs from 'dayjs';
import { ReportDataIngestionService } from '../ingest.service';
import { randomUUID } from 'crypto';
import { VendorItemPackaging } from '../../entities/VendorItemPackaging.entity';
import { InventoryPricedItem } from 'apps/inventory/src/entities/lite/priced-item.entity';
import { Business } from 'apps/business/src/proto/proto/business/business.pb';
import {
  PaginatedMarginEdgeProductResponse,
  Product,
  InventoryOrder,
  InventoryOrderDetail,
  PaginatedInventoryOrdersResponse,
  PaginatedVendorItemPackagingResponse,
} from './marginedge-public';
import { BusinessClient } from '@tangopay/shared/clients/businessClient.client';
const MARGINEDGE_PUBLIC_API_RATE_LIMIT = 1000;

function rateLimit(ms = MARGINEDGE_PUBLIC_API_RATE_LIMIT + 50) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export class ReportDataMarginEdgePublicHarvesterService {
  private readonly logger = new Logger(ReportDataMarginEdgePublicHarvesterService.name);

  @Inject(ReportDataIngestionService)
  private readonly ingestionService: ReportDataIngestionService;

  @InjectRepository(DataHarvest)
  private readonly dataHarvestRepository: Repository<DataHarvest>;

  @InjectRepository(DataLink)
  private readonly dataLinkRepository: Repository<DataLink>;

  @InjectRepository(HarvestedBinaryData)
  private readonly harvestedBinaryDataRepository: Repository<HarvestedBinaryData>;

  @InjectRepository(VendorItemPackaging)
  private readonly vendorItemPackagingRepository: Repository<VendorItemPackaging>;

  @InjectRepository(InventoryPricedItem)
  private readonly pricedItemRepository: Repository<InventoryPricedItem>;

  @Inject(BusinessClient)
  private readonly businessClient: BusinessClient;

  async createMarginEdgePublicDataLink(request: LinkDataSourceRequestDto, business: Business): Promise<GenericResponse> {
    const marginEdgeRestaurantUnitId = request.data.find((d) => d.key === 'marginEdgeRestaurantUnitId')?.value;
    if (!marginEdgeRestaurantUnitId) throw new NoMarginEdgeRestaurantUnitIdException();

    const marginEdgeApiKey = request.data.find((d) => d.key === 'marginEdgeApiKey')?.value;
    if (!marginEdgeApiKey) throw new NoMarginEdgeApiKeyException();

    const dataLink = this.dataLinkRepository.create({
      source: 'marginEdgePublic',
      business: {
        id: business.id,
      },
      cadence: 'daily',
      linkInfo: {
        type: 'marginEdgePublic',
        marginEdgeRestaurantUnitId,
        marginEdgeApiKey,
      },
    });

    await this.dataLinkRepository.save(dataLink);

    return {
      status: HttpStatus.OK,
      error: null,
    };
  }

  async fetchAllProductsAsCSV(restaurantUnitId: string, apiKey: string): Promise<Buffer> {
    let response = await axios.get<PaginatedMarginEdgeProductResponse>(`https://api.marginedge.com/public/products?restaurantUnitId=${restaurantUnitId}`, {
      headers: {
        'x-api-key': apiKey,
      },
    });

    const products: Product[] = response.data.products.filter((p) => p.companyConceptProductId !== 'null');

    while (response.data.nextPage) {
      response = await axios.get<PaginatedMarginEdgeProductResponse>(
        `https://api.marginedge.com/public/products?restaurantUnitId=${restaurantUnitId}&nextPage=${response.data.nextPage}`,
        {
          headers: {
            'x-api-key': apiKey,
          },
        },
      );

      products.push(...response.data.products.filter((p) => p.companyConceptProductId !== 'null'));

      await rateLimit();
    }

    const compressed = await new Promise<Buffer>((res, rej) =>
      gzip(JSON.stringify(products), (err, buff) => {
        if (err) rej(err);
        else res(buff);
      }),
    );

    return compressed;
  }

  async harvestAllAndInitialiseDataLink(dataLink: DataLink): Promise<GenericResponse> {
    if (dataLink.linkInfo.type !== 'marginEdgePublic') throw new LinkInfoDoesNotMatchLinkTypeException(dataLink.id);

    const harvest = this.dataHarvestRepository.create({
      dataLink: {
        id: dataLink.id,
      },
      status: 'pending',
      deduplicationKey: dayjs(new Date()).format('YYYYMMDD'),
    });

    await this.dataHarvestRepository.save(harvest);

    return {
      status: HttpStatus.OK,
      error: null,
    };
  }

  async performImmediateHarvest(request: ImmediateHarvestRequestDto, dataLink: DataLink) {
    if (dataLink.source !== 'marginEdgePublic') throw new IncorrectDataSourceTypeToProcessException(dataLink.id, 'marginEdgePublic', dataLink.source);
    if (dataLink.linkInfo.type !== 'marginEdgePublic') throw new LinkInfoDoesNotMatchLinkTypeException(dataLink.id);
    const unitId = request.data.find((d) => d.key === 'marginEdgeRestaurantUnitId')?.value;
    if (!unitId) throw new NoMarginEdgeRestaurantUnitIdException();

    if (request.harvestType !== 'inventory_orders_public' && request.harvestType !== 'inventory_items_public')
      throw new IncorrectHarvestTypeException(request.harvestType, ['inventory_orders_public', 'inventory_items_public']);

    const dataHarvest = this.dataHarvestRepository.create({
      dataLink: {
        id: dataLink.id,
      },
      status: 'pending',
      deduplicationKey: randomUUID(),
    });

    if (request.harvestType === 'inventory_orders_public') {
      const savedDataHarvest = await this.dataHarvestRepository.save(dataHarvest);
      await this.harvestOrders(savedDataHarvest, dataLink.business.id, unitId, dataLink.linkInfo.marginEdgeApiKey);
    } else if (request.harvestType === 'inventory_items_public') {
      const savedDataHarvest = await this.dataHarvestRepository.save(dataHarvest);
      await this.harvestItems(savedDataHarvest, unitId, dataLink.linkInfo.marginEdgeApiKey);
    }
  }

  async processHarvest(harvest: DataHarvest): Promise<GenericResponse> {
    const dataLink = harvest.dataLink;
    if (dataLink.source !== 'marginEdgePublic') throw new IncorrectDataSourceTypeToProcessException(dataLink.id, 'marginEdgePublic', dataLink.source);

    await this.dataHarvestRepository.update(harvest.id, {
      startedProcessingAt: new Date(),
    });

    for (const binaryData of harvest.harvestedBinaryData) {
      const decompressed = await new Promise<Buffer>((res, rej) =>
        gunzip(binaryData.data as unknown as Uint8Array, (err, buff) => {
          if (err) rej(err);
          else res(buff);
        }),
      );
      if (binaryData.dataType === 'inventory_orders_public') {
        await this.ingestionService.ingestData(
          {
            businessId: dataLink.business.id,
            data: decompressed.toString('base64'),
            dataSource: 'marginEdgePublic',
            dataType: 'inventory_orders',
            dataFormat: 'json',
          },
          harvest.id,
        );
      } else {
        this.logger.warn(`Unknown data type: ${binaryData.dataType}`);
      }
    }

    return {
      status: HttpStatus.OK,
      error: null,
    };
  }

  private async harvestItems(dataHarvest: DataHarvest, marginEdgeRestaurantUnitId: string, marginEdgeApiKey: string): Promise<void> {
    const products = await this.fetchAllProductsAsCSV(marginEdgeRestaurantUnitId, marginEdgeApiKey);

    const harvestedBinaryData = this.harvestedBinaryDataRepository.create({
      dataHarvest: {
        id: dataHarvest.id,
      },
      data: products,
      dataType: 'products',
    });

    await this.harvestedBinaryDataRepository.save(harvestedBinaryData);

    await this.dataHarvestRepository.update(dataHarvest.id, {
      harvestedAt: new Date(),
      status: 'success',
    });
  }

  private async harvestOrders(dataHarvest: DataHarvest, businessId: string, marginEdgeRestaurantUnitId: string, marginEdgeApiKey: string): Promise<void> {
    const today = dayjs().format('YYYY-MM-DD');
    // TODO: better time range
    const eightWeeksAgo = dayjs().subtract(8, 'week').format('YYYY-MM-DD');
    const business = await this.businessClient.getBusinessById(businessId);
    const enterprise = business.enterprises.find((e) => e.foodAndBev);
    const basicOrders: InventoryOrder[] = [];
    let url = `https://api.marginedge.com/public/orders?restaurantUnitId=${marginEdgeRestaurantUnitId}&startDate=${eightWeeksAgo}&endDate=${today}`;
    try {
      while (true) {
        const response = await fetch(url, {
          headers: {
            'x-api-key': marginEdgeApiKey,
          },
        });
        await rateLimit();
        if (!response.ok) {
          throw { message: response.statusText };
        }
        const data = (await response.json()) as PaginatedInventoryOrdersResponse;
        if (data.orders.length === 0) {
          break;
        }
        basicOrders.push(...data.orders);
        if (!data.nextPage) {
          break;
        }
        url = `https://api.marginedge.com/public/orders?restaurantUnitId=${marginEdgeRestaurantUnitId}&startDate=${eightWeeksAgo}&endDate=${today}&nextPage=${data.nextPage}`;
      }
    } catch (err) {
      this.logger.error(err);
      throw new MarginEdgeApiException(err.message);
    }
    const orderDetails: InventoryOrderDetail[] = [];
    const seenPackagings: Set<string> = new Set();
    for (const order of basicOrders) {
      const response = await fetch(`https://api.marginedge.com/public/orders/${order.orderId}?restaurantUnitId=${marginEdgeRestaurantUnitId}`, {
        headers: {
          'x-api-key': marginEdgeApiKey,
        },
      });
      await rateLimit();
      if (!response.ok) {
        this.logger.error(`Order ${order.orderId} returned status ${response.status}`);
        this.logger.debug(await response.json());
        continue;
      }
      const detail = (await response.json()) as InventoryOrderDetail;
      orderDetails.push(detail);
      for (const lineItem of detail.lineItems) {
        const packId = `${lineItem.packagingId}`;
        if (seenPackagings.has(packId)) {
          continue;
        }
        seenPackagings.add(packId);
        // load into db
        await this.getVendorItemPackaging(
          packId,
          businessId,
          enterprise.id,
          order.vendorId,
          lineItem.vendorItemCode,
          // TODO: this is not the concept product id, but the company concept product id
          lineItem.companyConceptProductId,
          marginEdgeApiKey,
          marginEdgeRestaurantUnitId,
        );
      }
    }
    const toCompress: HarvestedInventoryOrders = {
      orders: orderDetails,
    };

    const compressed = await new Promise<Buffer>((res, rej) =>
      gzip(JSON.stringify(toCompress), (err, buff) => {
        if (err) rej(err);
        else res(buff);
      }),
    );

    const harvestedBinaryData = this.harvestedBinaryDataRepository.create({
      dataHarvest: {
        id: dataHarvest.id,
      },
      data: compressed,
      dataType: 'inventory_orders',
    });

    await this.harvestedBinaryDataRepository.save(harvestedBinaryData);

    await this.dataHarvestRepository.update(dataHarvest.id, {
      harvestedAt: new Date(),
      status: 'success',
    });
  }

  // assumes that, when called, it is safe to call the marginEdge API
  // guarantees that, after returning, it is safe to call the marginEdge API again
  private async getVendorItemPackaging(
    packagingId: string,
    businessId: string,
    enterpriseId: string,
    vendorId: string,
    vendorItemCode: string,
    conceptProductId: string,
    marginEdgeApiKey: string,
    marginEdgeRestaurantUnitId: string,
  ): Promise<VendorItemPackaging> {
    if (!vendorItemCode) return null;
    const packaging = await this.vendorItemPackagingRepository.findOne({
      where: {
        packagingId,
        vendorId,
        vendorItemCode,
        business: {
          id: businessId,
        },
      },
    });
    if (packaging) {
      return packaging;
    }

    const response = await fetch(
      `https://api.marginedge.com/public/vendors/${vendorId}/vendorItems/${vendorItemCode}/packaging?restaurantUnitId=${marginEdgeRestaurantUnitId}`,
      {
        headers: {
          'X-Api-Key': marginEdgeApiKey,
        },
      },
    );
    const limit = rateLimit();
    if (!response.ok) {
      this.logger.error(`Vendor item packaging for ${vendorId} ${vendorItemCode} returned status ${response.status}`);
      await limit;
      return null;
    }
    const data = (await response.json()) as PaginatedVendorItemPackagingResponse;
    if (data.packagings.length === 0) {
      await limit;
      return null;
    }
    const allPackagings = await Promise.all(
      data.packagings.map(async (p) => {
        const existing = await this.vendorItemPackagingRepository.findOne({
          where: {
            vendorId,
            vendorItemCode,
            packagingId: p.packagingId,
          },
        });
        if (existing) {
          return existing;
        }
        const pricedItem = await this.pricedItemRepository.findOne({
          where: {
            ingestedId: conceptProductId,
            enterprise: {
              id: enterpriseId,
            },
          },
        });
        const toSave = this.vendorItemPackagingRepository.create({
          vendorId,
          vendorItemCode,
          packagingId: p.packagingId,
          name: p.packagingName,
          unitName: p.unit,
          quantity: p.quantity,
          business: {
            id: businessId,
          },
          item: pricedItem,
        });
        return await toSave.save();
      }),
    );
    const match = allPackagings.find((p) => p.packagingId === packagingId);
    if (!match) {
      this.logger.error(`Vendor item packaging for ${vendorId} ${vendorItemCode} not found`);
    }
    await limit;
    return match;
  }
}
