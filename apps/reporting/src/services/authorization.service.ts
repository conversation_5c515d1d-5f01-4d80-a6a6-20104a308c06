import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Reviewable } from '../entities/GoogleReview.entity';
import { Repository } from 'typeorm';
import { ReportingPermissionsRequest } from '../proto/reporting/authorization.pb';
import { GenericResponse } from '../proto/reporting/shared.pb';
import { checkIdsForBusiness } from '@tangopay/shared/utils/authorization';

@Injectable()
export class ReportingAuthService {
  private readonly logger = new Logger(ReportingAuthService.name);

  constructor(
    @InjectRepository(Reviewable)
    private readonly reviewableRepository: Repository<Reviewable>,
  ) {}

  async authorize(request: ReportingPermissionsRequest): Promise<GenericResponse> {
    await checkIdsForBusiness(this.reviewableRepository, request.businessIds, request.reviewableIds, { entityName: 'reviewable', logger: this.logger });

    return {
      status: HttpStatus.OK,
      error: null,
    };
  }
}
