import {
  DAILY_SALES_LABOUR_GUEST_CTE_QUERY,
  SHIFTS_CTE_QUERY,
  SUB_ACTUAL_WORK_DETAILS_CTE_QUERY,
  SUB_DAILY_TRENDS_CTE_QUERY,
  SUB_OVERALL_METRICS_CTE_QUERY,
  SUB_PRODUCTS_CTE_QUERY,
  SUB_SCHEDULE_DETAILS_CTE_QUERY,
  SUB_SCHEDULE_SUMMARY_CTE_QUERY,
  SUB_STAFF_PERFORMANCE_CTE_QUERY,
  SUB_TABS_CTE_QUERY,
  SUB_TOP_ITEMS_CTE_QUERY,
  SUB_WORK_EVENTS_CTE_QUERY,
} from '../queries/base-queries';
import Agent from './agent';

class SupportAgent extends Agent {
  constructor(schemaSelectorPrompt: string, queryDecomposerPrompt: string, sqlGeneratorPrompt: string) {
    const interfaces = `
// ===============================
// SHARED INTERFACES
// ===============================

interface RevenueTrend {
  totalRevenue: number;
  totalOrders: number;
  averageOrderValue: number;
}

interface ItemPerformance {
  name: string;
  menuCategoryName: string;
  menuName: string;
  type: string;
  totalRevenue: number;
  totalOrders: number;
}

interface KeyInsights {
  peakSalesHour: Date;
  topSellingItem: string;
  totalItemsSold: number;
  bestPerformingDay?: string;
}

interface Staff {
  staffName: string;
  totalHours: number;
  totalEarned: number;
  totalShifts: number;
  payRateHourly: number;
  payRateAnnual: number;
  totalTips: number;
}

interface Schedule {
  [date: string]: {
    staffName: string;
    roleName: string;
    departmentName: string;
    shiftType: string;
    startTime: Date;
    endTime: Date;
    payRateHourly: number;
    payRateAnnual: number;
  };
}

// ===============================
// SALES UI INTERFACES
// ===============================

export interface SalesSummary {
  type: 'sales';
  scope: 'individual' | 'daily' | 'weekly' | 'monthly';
  screensToShow: ('overview' | 'trends' | 'top-items')[];
  serviceAreas: string[];
  createdAt: Date;
  startDate: Date;
  endDate?: Date;
  totalRevenue: number;
  totalOrders: number;
  averageOrderValue: number;
  growthRate: number;
  // All sales metrics except for "individual" items
  insights?: KeyInsights;
  weeklyTrends?: {
    monday: RevenueTrend;
    tuesday: RevenueTrend;
    wednesday: RevenueTrend;
    thursday: RevenueTrend;
    friday: RevenueTrend;
    saturday: RevenueTrend;
    sunday: RevenueTrend;
  };
  monthlyTrends?: {
    Q1: RevenueTrend;
    Q2: RevenueTrend;
    Q3: RevenueTrend;
    Q4: RevenueTrend;
  };
  topItems?: ItemPerformance[];
}

export interface SalesComparison {
  type: 'sales';
  scope: 'comparison';
  currentPeriod: SalesSummary;
  previousPeriod: SalesSummary;
  percentageChanges: {
    revenue: number;
    orders: number;
    averageOrderValue: number;
  };
  trendDirection: 'up' | 'down' | 'stable';
  significantChanges: string[];
  topGainers: ItemPerformance[];
  topDecliners: ItemPerformance[];
}

export interface SalesTabular {
  type: 'sales';
  scope: 'tabular';
  headers: string[];
  rows: Array<Array<string | number>>;
  totals: Array<string | number>;
  sortedBy: string;
  sortDirection: 'asc' | 'desc';
  filters: Record<string, any>;
}

// ===============================
// PRODUCT UI INTERFACES
// ===============================

export interface ProductSummary {
  type: 'product';
  scope: 'individual' | 'category' | 'menu';
  productName: string;
  productId: string;
  menuCategory: string;
  menuName: string;
  productType: string;
  createdAt: Date;
  startDate: Date;
  endDate?: Date;
  totalRevenue: number;
  totalQuantitySold: number;
  totalVoided: number;
  averagePrice: number;
  revenueRank: number;
  quantityRank: number;
  categoryRank: number;
  growthRate: number;
  salesTrend: 'increasing' | 'decreasing' | 'stable';
  dailyPerformance: Array<{
    date: string;
    revenue: number;
    quantity: number;
  }>;
  categoryComparison: Array<{
    productName: string;
    revenue: number;
    rank: number;
  }>;
}

export interface ProductComparison {
  type: 'product';
  scope: 'comparison';
  productA: ProductSummary;
  productB: ProductSummary;
  winner: {
    revenue: string;
    quantity: string;
    growth: string;
  };
  insights: string[];
}

export interface ProductTabular {
  type: 'product';
  scope: 'tabular';
  headers: string[];
  rows: Array<Array<string | number>>;
  totals: Array<string | number>;
  categoryFilter?: string;
  typeFilter?: string;
  sortedBy: string;
  sortDirection: 'asc' | 'desc';
}

// ===============================
// SCHEDULE UI INTERFACES
// ===============================

export interface ScheduleSummary {
  type: 'schedule';
  scope: 'individual' | 'weekly';
  createdAt: Date;
  startDate: Date;
  endDate?: Date;
  schedule: Schedule[];
  staff: Staff[];
  totalHours: number;
  laborCost: number;
  totalShifts: number;
  activeStaff: number;
  busiestDay: string;
  busiestDayShifts: number;
  averageCostPerDay: number;
  mostScheduledStaff: string;
  mostScheduledShifts: number;
}

export interface ScheduleComparison {
  type: 'schedule';
  scope: 'comparison';
  scheduled: {
    totalHours: number;
    totalCost: number;
    staffCount: number;
  };
  actual: {
    totalHours: number;
    totalCost: number;
    staffCount: number;
  };
  variance: {
    hoursVariance: number;
    costVariance: number;
    efficiencyScore: number;
  };
  issues: {
    noShows: string[];
    overtime: Array<{ name: string; hours: number }>;
    undertime: Array<{ name: string; hours: number }>;
  };
}

export interface ScheduleTabular {
  type: 'schedule';
  scope: 'tabular';
  headers: string[];
  rows: Array<Array<string | number>>;
  dateRange: { start: string; end: string };
  departmentFilter?: string;
  roleFilter?: string;
}

// ===============================
// LABOR UI INTERFACES
// ===============================

export interface LaborSummary {
  type: 'labor';
  scope: 'efficiency' | 'cost' | 'performance';
  createdAt: Date;
  startDate: Date;
  endDate?: Date;
  laborCostPercentage: number;
  targetLaborPercentage: number;
  variance: number;
  totalLaborCost: number;
  totalRevenue: number;
  efficiency: {
    salesPerLaborHour: number;
    customersPerLabor: number;
    revenuePerDollarSpent: number;
  };
  departmentBreakdown: Array<{
    department: string;
    laborCost: number;
    hours: number;
    efficiency: number;
    staffCount: number;
  }>;
  trendAnalysis: {
    direction: 'improving' | 'declining' | 'stable';
    recommendations: string[];
  };
}

export interface LaborComparison {
  type: 'labor';
  scope: 'comparison';
  currentPeriod: LaborSummary;
  previousPeriod: LaborSummary;
  industryBenchmark: {
    averageLaborPercentage: number;
    performanceVsBenchmark: string;
  };
  improvements: string[];
  concerns: string[];
}

export interface LaborTabular {
  type: 'labor';
  scope: 'tabular';
  headers: string[];
  rows: Array<Array<string | number>>;
  totals: Array<string | number>;
  targetPercentage: number;
}

// ===============================
// STAFF UI INTERFACES
// ===============================

export interface StaffSummary {
  type: 'staff';
  scope: 'individual' | 'department' | 'role';
  staffName?: string;
  staffId?: string;
  department?: string;
  role?: string;
  createdAt: Date;
  startDate: Date;
  endDate?: Date;
  totalStaff: number;
  activeStaff: number;
  performance: {
    topPerformer: string;
    averageHours: number;
    averageEarnings: number;
    averageEfficiency: number;
  };
  departmentStats: Array<{
    department: string;
    staffCount: number;
    totalHours: number;
    averagePay: number;
    efficiency: number;
  }>;
  individualStats: Array<{
    staffName: string;
    role: string;
    totalHours: number;
    totalEarned: number;
    efficiency: number;
    tips: number;
    rank: number;
  }>;
}

export interface StaffComparison {
  type: 'staff';
  scope: 'comparison';
  staffA: {
    name: string;
    performance: any;
  };
  staffB: {
    name: string;
    performance: any;
  };
  winner: {
    hours: string;
    earnings: string;
    efficiency: string;
    tips: string;
  };
  insights: string[];
}

export interface StaffTabular {
  type: 'staff';
  scope: 'tabular';
  headers: string[];
  rows: Array<Array<string | number>>;
  totals: Array<string | number>;
  departmentFilter?: string;
  roleFilter?: string;
}

// ===============================
// SALES-BY-STAFF UI INTERFACES
// ===============================

export interface SalesByStaffSummary {
  type: 'sales-by-staff';
  scope: 'individual' | 'department' | 'comparison';
  createdAt: Date;
  startDate: Date;
  endDate?: Date;
  totalStaffWithSales: number;
  topPerformer: {
    name: string;
    totalSales: number;
    customerCount: number;
    averageTicket: number;
    efficiency: number;
  };
  departmentTotals: Array<{
    department: string;
    totalSales: number;
    staffCount: number;
    averagePerStaff: number;
  }>;
  individualPerformance: Array<{
    staffName: string;
    role: string;
    department: string;
    totalSales: number;
    customerCount: number;
    averageTicket: number;
    tips: number;
    hoursWorked: number;
    salesPerHour: number;
    rank: number;
  }>;
}

export interface SalesByStaffComparison {
  type: 'sales-by-staff';
  scope: 'comparison';
  staffA: {
    name: string;
    sales: number;
    efficiency: number;
  };
  staffB: {
    name: string;
    sales: number;
    efficiency: number;
  };
  winner: {
    totalSales: string;
    efficiency: string;
    customerService: string;
  };
  insights: string[];
}

export interface SalesByStaffTabular {
  type: 'sales-by-staff';
  scope: 'tabular';
  headers: string[];
  rows: Array<Array<string | number>>;
  totals: Array<string | number>;
  departmentFilter?: string;
  roleFilter?: string;
}

// ===============================
// UNION TYPES
// ===============================

export type UIDataStructure =
  | SalesSummary
  | SalesComparison
  | SalesTabular
  | ProductSummary
  | ProductComparison
  | ProductTabular
  | ScheduleSummary
  | ScheduleComparison
  | ScheduleTabular
  | LaborSummary
  | LaborComparison
  | LaborTabular
  | StaffSummary
  | StaffComparison
  | StaffTabular
  | SalesByStaffSummary
  | SalesByStaffComparison
  | SalesByStaffTabular;

`;

    super(
      'SupportAgent',
      `You're a professional software engineer having worked with LLMs and PostgresSQL in building LLM projects for multiple companies.
	
You're trying to build an LLM that is able to answer user questions based on data in your database by leveraging common table expressions (CTEs).

The CTEs you have available are:
${SUB_PRODUCTS_CTE_QUERY}
${SUB_TABS_CTE_QUERY}
${SUB_WORK_EVENTS_CTE_QUERY}
${DAILY_SALES_LABOUR_GUEST_CTE_QUERY}
${SHIFTS_CTE_QUERY}
${SUB_DAILY_TRENDS_CTE_QUERY}
${SUB_TOP_ITEMS_CTE_QUERY}
${SUB_OVERALL_METRICS_CTE_QUERY}
${SUB_SCHEDULE_DETAILS_CTE_QUERY}
${SUB_ACTUAL_WORK_DETAILS_CTE_QUERY}
${SUB_STAFF_PERFORMANCE_CTE_QUERY}
${SUB_SCHEDULE_SUMMARY_CTE_QUERY}

and the SQL generator can leverage multiple CTEs at once depending on the question. Ideally what we'd like is for a SQL query to be generated to use the CTEs like they are tables that already exist and we could do something like
\`\`\`
      const cteHeaders = ((context.schemaInfo?.ctes || []) as string[])
        .map((cte) => {
          switch (cte) {
            case 'products':
              return SUB_PRODUCTS_CTE_QUERY;
            case 'tabs':
              return SUB_TABS_CTE_QUERY;
            case 'work_events':
              return SUB_WORK_EVENTS_CTE_QUERY;
            default:
              return null;
          }
        })
        .filter(Boolean);

      context.sqlQuery = cteHeaders.length > 0 ? \`WITH \${cteHeaders.join(',\n')}\n\n\${sqlQuery.replace(/^\s*WITH\s+/i, '').trim()}\` : sqlQuery;
\`\`\`
to be able to replace a CTE like "products" with it's definition e.g.
\`\`\`
      WITH products AS (
        SELECT * FROM products_table
      )
\`\`\`
and then the rest of the query would be the rest of the query with the CTE replaced with the definition.

NOTE: Each question is intended to come with significantly more information than the question asked because we're generating a UI for the user with a lot of details about their business.
Currently, the goal is to have answer questions for UIs that support sales, products, schedule, labor, staff and sales by staff information, where the UI would either be regular (UI with data for a single time period, product etc.), comparison (UI with data for 2 time periods, products etc.) or tabular (UI with data for a table which is often a group by menu category, product type etc.).

Here are the expected UI data structures that the system should generate (from responses.ts):

${interfaces}

There's 2 agents before the SQL generator agent does it's work; SchemaSelectorAgent and QueryDecomposerAgent.

The system prompt for SchemaSelectorAgent is:
${schemaSelectorPrompt}

The system prompt for QueryDecomposerAgent is:
${queryDecomposerPrompt}

Finally, the system prompt for the SQL generator agent is:
${sqlGeneratorPrompt}

If you are called this means the execution failed. Your job is to figure out what went wrong and provide the extact reason or reasons why the query failed.

You will have access to the result generated by each agent.

Some possible reasons I can think of are:
- The CTE didn't have the variable
- One of the agents made a mistake in it's work
- One of the agents didn't have the full context on how to solve this problem
...

We really need to have a working solution so that agentically we'd be able to generate SQL queries that are correct and workable. If your solution involves reducing the agents or changing the information or the prompt, all of these are welcome ideas.

Please provide your best feedback on how to improve the system. No 2 queries will likely be the same.
`,
      true,
    );
  }

  formatInput(responses: string) {
    return responses;
  }
}

export default SupportAgent;
