import Agent from './agent';

// Enhanced SQL Generator with better syntax validation
class EnhancedSQLGeneratorAgent extends Agent {
  constructor() {
    super(
      'EnhancedSQLGenerator',
      `You are a PostgreSQL expert specializing in restaurant management systems with ZERO TOLERANCE for syntax errors. Every query you generate MUST be syntactically perfect.

CRITICAL RULES:
1. Generate ONLY SELECT statements - NEVER create WITH clauses
2. CTEs already exist with exact column names listed below
3. Use EXACT column names from CTE definitions - do not make up column names
4. Perfect syntax required - zero tolerance for errors

ABSOLUTE SYNTAX RULES (NEVER VIOLATE):

1. NULLIF ALWAYS takes exactly 2 parameters:
   ✅ NULLIF(COUNT(DISTINCT t.id), 0)
   ✅ NULLIF(SUM(amount), 0)  
   ❌ NULLIF(COUNT(DISTINCT t.id))::NUMERIC, 0
   ❌ NULLIF(SUM(amount)::NUMERIC, 0)

2. ROUND casting rules:
   ✅ ROUND((expression)::NUMERIC, 2)
   ✅ ROUND((SUM(amount) / NULLIF(COUNT(*), 0))::NUMERIC, 2)
   ❌ ROUND(expression::NUMERIC, 2)
   ❌ ROUND((SUM(amount)::NUMERIC / COUNT(*)), 2)

3. Parentheses balance - count them!
   ✅ ROUND((SUM(sub_total_amount) / NULLIF(COUNT(DISTINCT tab_id), 0))::NUMERIC / 100, 2)
   ❌ ROUND((SUM(sub_total_amount) / NULLIF(COUNT(DISTINCT tab_id), 0)::NUMERIC / 100, 2)

4. Comma placement:
   ✅ SELECT field1, field2, field3 FROM table
   ❌ SELECT field1, field2, field3, FROM table

5. Alias naming - ALWAYS snake_case:
   ✅ total_sales, staff_name, average_ticket_size
   ❌ totalSales, staffName, averageTicketSize

CRITICAL SCHEMA KNOWLEDGE:
CTE:

products:
- staff_id (uuid): CRITICAL for staff analysis
- tab_id (uuid): CRITICAL for connecting sales to tabs
- sub_total_amount (number): CENTS - divide by 100
- through_quantity (number): actual quantity sold
- ordered_day (date)
- business_id (uuid)
- name (string): product name

tabs:
- id (uuid): matches tab_id in products
- staff_id (uuid): matches staff_id in products/work_events
- number_of_guests (number)
- tip_amount (number): if available
- business_id (uuid)

work_events:
- staff_id (uuid): matches products.staff_id
- minutes_worked (number): for efficiency calculations
- work_day (date): matches products.ordered_day
- business_id (uuid)

STAFF SALES QUERY PATTERN:
For "top staff by sales", the pattern is:
1. GROUP BY staff_id from products
2. SUM(sub_total_amount) for total sales
3. COUNT(DISTINCT tab_id) for total tabs handled
4. JOIN tabs for guest count and tips
5. JOIN work_events for hours worked
6. Calculate efficiency metrics

TEMPLATE for staff sales analysis:
SELECT 
  p.staff_id,
  ROUND((SUM(p.sub_total_amount) / 100)::NUMERIC, 2) as total_sales,
  COUNT(DISTINCT p.tab_id) as total_tabs_handled,
  COALESCE(SUM(t.number_of_guests), 0) as total_customers_served,
  ROUND((SUM(p.sub_total_amount) / NULLIF(COUNT(DISTINCT p.tab_id), 0))::NUMERIC / 100, 2) as average_ticket_size,
  ROUND((COALESCE(SUM(w.minutes_worked), 0) / 60.0)::NUMERIC, 2) as total_hours_worked,
  ROUND((SUM(p.sub_total_amount) / NULLIF(SUM(w.minutes_worked), 0) * 60)::NUMERIC / 100, 2) as sales_per_hour,
  ROW_NUMBER() OVER (ORDER BY SUM(p.sub_total_amount) DESC) as sales_rank
FROM products p
LEFT JOIN tabs t ON t.id = p.tab_id 
LEFT JOIN work_events w ON w.staff_id = p.staff_id AND w.work_day = p.ordered_day
WHERE p.business_id = '{business_id}'
  AND p.ordered_day >= '{start_date}'
  AND p.ordered_day <= '{end_date}'
GROUP BY p.staff_id
ORDER BY total_sales DESC;

SYNTAX VALIDATION CHECKLIST:
Before outputting SQL, verify:
□ Every opening parenthesis has a closing one
□ Every NULLIF has exactly 2 comma-separated parameters  
□ Every ROUND casts to ::NUMERIC inside parentheses
□ No trailing commas in SELECT or WHERE clauses
□ All aliases use snake_case
□ business_id filter is present
□ Date filters use proper format

ERROR PREVENTION:
- Count parentheses manually
- Never put ::NUMERIC inside NULLIF parameters
- Always use COALESCE for potentially NULL results
- Test division by zero scenarios

Return ONLY the SQL query with perfect syntax.`,
    );
  }

  formatInput(input: string, context: { steps: any[]; schemaInfo: any; entityMatches: any; timeContext: any; businessId: string }) {
    const { steps, schemaInfo, entityMatches, timeContext, businessId } = context;

    return `Enhanced Query: ${input}
Business ID: ${businessId}
Time Context: ${JSON.stringify(timeContext)}
Items to Query: ${JSON.stringify(schemaInfo.items_to_query)}
UI Type: ${schemaInfo.ui}
UI Scope: ${schemaInfo.ui_type}

Query Steps Summary:
${steps.map((step: any, index: number) => `Step ${index + 1}: ${step.description} (${step.type})`).join('\n')}

Required CTEs: ${schemaInfo.ctes?.join(', ')}

CTE Column Reference:
${this.getCTEColumnReference(schemaInfo.ctes)}

CRITICAL REMINDERS:
- All amounts are in CENTS (divide by 100 for dollars)
- Use snake_case for aliases
- NULLIF(value, 0) takes exactly 2 parameters
- No 'price' field exists - calculate as sub_total_amount / through_quantity
- Always include business_id filter
- Use through_quantity (not ordered_quantity) for accurate counts

SYNTAX REQUIREMENTS:
- Perfect parentheses balance
- NULLIF(value, 0) format only
- ROUND((expr)::NUMERIC, 2) format only
- snake_case aliases only
- No syntax errors allowed

Generate ONLY the SQL query:`;
  }

  private getCTEColumnReference(ctes: string[]): string {
    const cteColumns: Record<string, string> = {
      products: `
  - name (string): product name
  - sub_total_amount (number): CENTS
  - through_quantity (number): ordered minus voided
  - voided_quantity (number)
  - ordered_day (date)
  - menu_category (string)
  - product_id (uuid)
  - business_id (uuid)`,

      top_items: `
  - name (string): product name
  - revenue_rank (number)
  - quantity_rank (number) 
  - category_revenue_rank (number)
  - total_revenue (number): CENTS
  - menu_category (string)
  - business_id (uuid)`,

      work_events: `
  - staff_id (uuid)
  - total_pay (number): CENTS
  - minutes_worked (number)
  - work_day (date)
  - hourly_rate (number): CENTS per hour
  - business_id (uuid)`,

      daily_sales_labour_guest: `
  - ordered_day (date)
  - sales_total (number): CENTS
  - guest_count (number)
  - total_pay (number): CENTS
  - minutes_worked (number)
  - business_id (uuid)`,
    };

    return (ctes || []).map((cte) => `${cte}:${cteColumns[cte] || '  - (columns not specified)'}`).join('\n');
  }

  parseResponse(response: string): string {
    let sql = response.trim();

    // Remove markdown code blocks
    if (sql.includes('```sql')) {
      const sqlMatch = sql.match(/```sql\s*([\s\S]*?)\s*```/);
      if (sqlMatch) {
        sql = sqlMatch[1].trim();
      }
    }

    // Remove any WITH blocks that might have been created
    sql = sql.replace(/WITH\s+[\s\S]*?(?=SELECT)/im, '').trim();

    // Validate that it starts with SELECT
    if (!sql.match(/^\s*SELECT/i)) {
      throw new Error(`Generated SQL must start with SELECT. Got: ${sql.substring(0, 50)}...`);
    }

    // Fix common ROUND syntax issues
    sql = sql.replace(/ROUND\(([^,)]+),\s*(\d+)\)/g, 'ROUND(($1)::NUMERIC, $2)');

    // Remove trailing semicolon and re-add
    sql = sql.replace(/;$/, '').trim();
    if (!sql.endsWith(';')) {
      sql += ';';
    }

    return sql;
  }
}

export default EnhancedSQLGeneratorAgent;
