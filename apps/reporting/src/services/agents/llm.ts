import Anthropic from '@anthropic-ai/sdk';
import { ContentBlock, TextBlock } from '@anthropic-ai/sdk/resources/messages';

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY || '************************************************************************************************************',
});

function getMainBodyForAnthropicResponse(content: ContentBlock[]) {
  const result: TextBlock[] = [];
  for (let i = content.length - 1; i >= 0; i--) {
    const block = content[i];
    if (block.type === 'text') {
      result.unshift({
        ...block,
        // @ts-ignore
        text: block.text.replaceAll(/•/g, '-'),
      });
    } else {
      break;
    }
  }
  return result;
}

class LLMWrapper {
  model = 'claude-sonnet-4-20250514';

  async complete(systemPrompt: string, userPrompt: string) {
    try {
      const response = await anthropic.messages.create({
        model: this.model,
        messages: [
          {
            role: 'user',
            content: userPrompt,
          },
        ],
        max_tokens: 4096,
        system: systemPrompt,
        temperature: 0.05,
        // thinking: {
        //   type: 'enabled',
        //   budget_tokens: 2048,
        // },
      });

      const content = getMainBodyForAnthropicResponse(response.content)
        .map((block) => block.text)
        .join('\n');

      return content;
    } catch (e) {
      console.error(e);
    }
  }
}

export default LLMWrapper;
