import Anthropic from '@anthropic-ai/sdk';
import { ContentBlock, TextBlock } from '@anthropic-ai/sdk/resources/messages';

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY || '************************************************************************************************************',
});

function getMainBodyForAnthropicResponse(content: ContentBlock[]) {
  const result: TextBlock[] = [];
  for (let i = content.length - 1; i >= 0; i--) {
    const block = content[i];
    if (block.type === 'text') {
      result.unshift({
        ...block,
        // @ts-ignore
        text: block.text.replaceAll(/•/g, '-'),
      });
    } else {
      break;
    }
  }
  return result;
}

class LLMWrapper {
  model = 'claude-sonnet-4-20250514';

  extendedThinking: boolean = false;

  constructor(extendedThinking: boolean = false) {
    this.extendedThinking = extendedThinking;
  }

  async complete(systemPrompt: string, userPrompt: string) {
    try {
      const params = {
        model: this.model,
        messages: [
          {
            role: 'user' as 'user' | 'assistant',
            content: userPrompt,
          },
        ],
        max_tokens: this.extendedThinking ? 8192 : 4096,
        system: systemPrompt,
        thinking: undefined,
        temperature: undefined,
      };

      if (this.extendedThinking) {
        params.thinking = {
          type: 'enabled',
          budget_tokens: 4096,
        };
      } else {
        params.temperature = 0.05;
      }

      const response = await anthropic.messages.create(params);

      const content = getMainBodyForAnthropicResponse(response.content)
        .map((block) => block.text)
        .join('\n');

      return content;
    } catch (e) {
      console.error(e);
    }
  }
}

export default LLMWrapper;
