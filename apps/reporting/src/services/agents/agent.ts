import { CopilotHistory } from '../../entities/Copilot.entity';
import LL<PERSON><PERSON>rapper from './llm';
import { encoding_for_model, Tiktoken } from 'tiktoken';

// const llm = new LLMWrapper();

class Agent {
  name: string;
  systemPrompt: string;
  inputTokens: number;
  outputTokens: number;

  enc: Tiktoken;
  extendedThinking: boolean;

  llm: LLMWrapper;

  constructor(name: string, systemPrompt: string, extendedThinking: boolean = false) {
    this.name = name;
    this.systemPrompt = systemPrompt;
    this.inputTokens = 0;
    this.outputTokens = 0;
    this.enc = encoding_for_model('gpt-4o');
    this.llm = new LLMWrapper(extendedThinking);
  }

  async execute(input: string, context = {}, historicalChat: CopilotHistory[] = []) {
    const userPrompt = this.formatInput(input, context, historicalChat);
    const response = await this.llm.complete(this.systemPrompt, userPrompt);
    this.outputTokens = this.enc.encode(response).length;
    return this.parseResponse(response);
  }

  formatInput(input: string, context = {}, historicalChat = []) {
    const formattedInput = JSON.stringify({ input, context, historicalChat }, null, 2);
    this.inputTokens = this.enc.encode(formattedInput).length;
    return formattedInput;
  }

  parseResponse(response: string | undefined) {
    if (!response) return { raw: 'No response provided' };
    try {
      if (response.includes('```json')) {
        const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[1]);
        }
      } else if (response.includes('```sql')) {
        const sqlMatch = response.match(/```sql\s*([\s\S]*?)\s*```/);
        if (sqlMatch) {
          return sqlMatch[1];
        }
      }
      return JSON.parse(response);
    } catch (e) {
      return { raw: response };
    }
  }

  getInputTokens() {
    return this.inputTokens;
  }

  getOutputTokens() {
    return this.outputTokens;
  }

  getTotalTokens() {
    return this.inputTokens + this.outputTokens;
  }

  getSystemPrompt() {
    return this.systemPrompt;
  }
}

export default Agent;
