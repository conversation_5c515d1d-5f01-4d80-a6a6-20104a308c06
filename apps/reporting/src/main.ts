import { INestMicroservice, ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { Transport } from '@nestjs/microservices';
import { ReportingModule } from './reporting.module';
import { protobufPackage } from './proto/reporting/reporting.pb';
import { HttpExceptionFilter } from '@tangopay/shared/filters/http-exception.filter';
import { protobufPackage as healthProtobufPackage } from '@tangopay/shared/health/proto/health.pb';
import { RpcExceptionFilter } from '@tangopay/shared/filters/rpc-exception.filter';
import * as Sentry from '@sentry/node';

async function bootstrap() {
  const app: INestMicroservice = await NestFactory.createMicroservice(ReportingModule, {
    transport: Transport.GRPC,
    options: {
      url: `${process.env.MICROSERVICE_TRANSPORTER_URL_HOST ?? '0.0.0.0'}:50063`,
      package: [protobufPackage, healthProtobufPackage],
      maxReceiveMessageLength: 1024 * 1024 * 100,
      protoPath: [
        'tango-proto/proto/reporting/authorization.proto',
        'tango-proto/proto/reporting/shared.proto',
        'tango-proto/proto/reporting/analysis.proto',
        'tango-proto/proto/reporting/reporting.proto',
        'tango-proto/proto/reporting/unity.proto',
        'tango-proto/proto/reporting/data.proto',
        'tango-proto/proto/reporting/detailed-data.proto',
        'tango-proto/proto/reporting/generate.proto',
        'tango-proto/proto/reporting/tipout.proto',
        'tango-proto/proto/reporting/budget.proto',
        'tango-proto/proto/reporting/insights.proto',
        'tango-proto/proto/reporting/ingest.proto',
        'tango-proto/proto/reporting/mailing.proto',
        'tango-proto/proto/reporting/harvest.proto',
        'tango-proto/proto/reporting/digest.proto',
        'tango-proto/proto/reporting/forecasting.proto',
        'tango-proto/proto/reporting/kpis.proto',
        'tango-proto/proto/reporting/reviews.proto',
        'tango-proto/proto/health.proto',
        'tango-proto/proto/reporting/copilot.proto',
      ],
      loader: {
        defaults: true,
      },
    },
    // if production exclude debug
    logger: process.env.CLUSTER_ENVIRONMENT === 'production' ? ['log', 'warn', 'error'] : ['debug', 'log', 'warn', 'error'],
  });
  Sentry.init({
    //dsn - get this value from your project in sentry
    dsn: process.env.SENTRY_DSN,
    // Configures the sample rate for error events, in the range of 0.0 to 1.0.
    //The default is 1.0 which means that 100% of error events are sent. If set to 0.1 only 10% of error events will be sent.
    sampleRate: 1,
    //Specify whether this SDK should send events to Sentry.
    enabled: true,
    //Specify environment you're working in (ie staging, production, local, etc)
    environment: process.env.CLUSTER_ENVIRONMENT,
  });

  app.useGlobalFilters(new RpcExceptionFilter(), new HttpExceptionFilter());
  app.useGlobalPipes(new ValidationPipe({ whitelist: true, transform: true }));

  await app.listen();
  // const service = app.get<DetailedDataRetrievalService>(DetailedDataRetrievalService);
  // console.log(
  //   await service.getTabs(
  //     'business',
  //     '6bce6f66-5818-432f-b750-a7e1db37872c', // 'd0cf2ed3-a45a-4d7a-8f07-34406795c57b',
  //     dayjs('2024-04-30T00:00:00.000Z').toDate(),
  //     dayjs('2024-05-27T00:00:00.000Z').toDate(),
  //     dayjs('2024-04-30T00:00:00.000Z').toDate(),
  //   ),
  // );
  // await listenTask;
}
bootstrap();
