import { Controller, UseFilters } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { HttpExceptionFilter } from '@tangopay/shared/filters/http-exception.filter';
import { RpcExceptionFilter } from '@tangopay/shared/filters/rpc-exception.filter';
import { REPORTING_AUTHORIZATION_SERVICE_NAME, ReportingPermissionsRequest } from '../proto/reporting/authorization.pb';
import { ReportingAuthService } from '../services/authorization.service';
import { GenericResponse } from '../proto/reporting/shared.pb';

@Controller('reporting-authorization')
@UseFilters(RpcExceptionFilter, HttpExceptionFilter)
export default class ReportingAuthorizationController {
  constructor(private readonly service: ReportingAuthService) {}

  @GrpcMethod(REPORTING_AUTHORIZATION_SERVICE_NAME, 'Authorize')
  async authorize(payload: ReportingPermissionsRequest): Promise<GenericResponse> {
    return this.service.authorize(payload);
  }
}
