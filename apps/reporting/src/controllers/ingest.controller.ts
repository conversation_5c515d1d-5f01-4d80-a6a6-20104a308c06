import { Controller } from '@nestjs/common';
import { ReportDataIngestionService } from '../services/ingest.service';
import { GrpcMethod } from '@nestjs/microservices';
import { REPORT_DATA_INGESTION_SERVICE_NAME } from '../proto/reporting/ingest.pb';
import { GenericResponse } from '../proto/reporting/shared.pb';
import { IngestDataRequestDto } from '../dtos/ingest.dto';

@Controller()
export class ReportDataIngestionController {
  constructor(private readonly reportDataIngestionService: ReportDataIngestionService) {}

  @GrpcMethod(REPORT_DATA_INGESTION_SERVICE_NAME, 'IngestData')
  private async ingestData(payload: IngestDataRequestDto): Promise<GenericResponse> {
    return await this.reportDataIngestionService.ingestData(payload, null);
  }
}
