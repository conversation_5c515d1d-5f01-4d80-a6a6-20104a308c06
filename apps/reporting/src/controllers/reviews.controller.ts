import { Controller } from '@nestjs/common';
import { ReviewsService } from '../services/reviews.service';
import { ReviewablesResponse, ReviewableResponse, REVIEWS_SERVICE_NAME } from '../proto/reporting/reviews.pb';
import { GrpcMethod } from '@nestjs/microservices';
import { IdRequestDTO, OptionalIdRequestDTO } from '../dtos/reporting.dto';
import { CreateReviewableRequestDTO, UpdateReviewableRequestDTO } from '../dtos/review.dto';
import { GenericResponse } from '../proto/reporting/shared.pb';

@Controller()
export class ReviewsController {
  constructor(private readonly reviewsService: ReviewsService) {}

  @GrpcMethod(REVIEWS_SERVICE_NAME, 'GetReviewables')
  async getReviewables(data: IdRequestDTO): Promise<ReviewablesResponse> {
    return this.reviewsService.getReviewables(data);
  }

  @GrpcMethod(REVIEWS_SERVICE_NAME, 'GetReviewable')
  async getReviews(data: IdRequestDTO): Promise<ReviewableResponse> {
    return this.reviewsService.getReviewable(data);
  }

  @GrpcMethod(REVIEWS_SERVICE_NAME, 'CreateReviewable')
  async createReviewable(data: CreateReviewableRequestDTO): Promise<ReviewableResponse> {
    return this.reviewsService.createReviewable(data);
  }

  @GrpcMethod(REVIEWS_SERVICE_NAME, 'UpdateReviewable')
  async updateReviewable(data: UpdateReviewableRequestDTO): Promise<ReviewableResponse> {
    return this.reviewsService.updateReviewable(data);
  }

  @GrpcMethod(REVIEWS_SERVICE_NAME, 'DeleteReviewable')
  async deleteReviewable(data: IdRequestDTO): Promise<GenericResponse> {
    return this.reviewsService.deleteReviewable(data);
  }

  @GrpcMethod(REVIEWS_SERVICE_NAME, 'RefreshReviews')
  async refreshReviews(data: IdRequestDTO): Promise<GenericResponse> {
    return this.reviewsService.refreshReviews(data);
  }

  @GrpcMethod(REVIEWS_SERVICE_NAME, 'GenerateReviewInsights')
  async generateReviewInsights(data: OptionalIdRequestDTO): Promise<GenericResponse> {
    return this.reviewsService.generateReviewInsights(data);
  }
}
