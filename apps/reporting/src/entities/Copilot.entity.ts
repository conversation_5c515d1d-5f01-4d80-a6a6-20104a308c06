import { BaseEntity, Column, CreateDate<PERSON><PERSON>umn, DeleteDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('CopilotHistory')
export class CopilotHistory extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', name: 'session_id' })
  sessionId: string;

  @Column({ type: 'uuid', name: 'business_id' })
  businessId: string;

  @Column({ type: 'text', name: 'user_query' })
  userQuery: string;

  @Column({ type: 'text', name: 'enhanced_query' })
  enhancedQuery: string;

  @Column({ type: 'text', name: 'message' })
  message: string;

  @Column({ type: 'text', name: 'ui' })
  UI: string;

  @Column({ type: 'text', name: 'ui_type' })
  UIType: string;

  @Column({ type: 'integer', name: 'total_tokens' })
  totalTokens: number;

  @Column({ type: 'varchar', name: 'follow_up_questions', array: true })
  followUpQuestions: string[];

  @Column({ type: 'varchar', name: 'ctes', array: true })
  ctes: string[];

  @Column({ type: 'jsonb', name: 'time_context' })
  timeContext: { type: 'relative' | 'absolute' | 'none'; start_date: string; end_date: string };

  @Column({ type: 'text', name: 'query_type' })
  queryType: string;

  @Column({ type: 'jsonb', name: 'options' })
  options: { id: string; name: string }[];

  @Column({ type: 'jsonb', name: 'items_to_query' })
  itemsToQuery: { type: string; name: string }[];

  @Column({ type: 'jsonb', name: 'query_decomposer' })
  queryDecomposer: any;

  @Column({ type: 'varchar', name: 'sql_query' })
  sqlQuery: string;

  @Column({ type: 'bool', name: 'success' })
  success: boolean;

  @CreateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
    name: 'created_at',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
    onUpdate: 'CURRENT_TIMESTAMP(6)',
    name: 'updated_at',
  })
  updatedAt: Date;

  @DeleteDateColumn({ type: 'timestamp', nullable: true, name: 'deleted_at' })
  deletedAt: Date | null;
}
