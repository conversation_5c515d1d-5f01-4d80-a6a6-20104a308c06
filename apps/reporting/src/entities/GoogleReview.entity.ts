import { Business } from 'apps/business/src/entities/business/business.entity';
import {
  BaseEntity,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  Relation,
  UpdateDateColumn,
} from 'typeorm';

@Entity('Reviewables')
export class Reviewable extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Index()
  @ManyToOne(() => Business)
  @JoinColumn({ name: 'business_id', referencedColumnName: 'id' })
  business: Relation<Business>;

  @Column({ type: 'varchar', name: 'google_places_id' })
  googlePlacesId: string;

  @Column({ type: 'bool', name: 'is_self', default: false })
  isSelf: boolean;

  @Column({ type: 'varchar', nullable: true, name: 'description' })
  description: string | null;

  @OneToMany(() => IngestedReview, (reviewable) => reviewable.reviewable)
  @JoinColumn({ name: 'review_id', referencedColumnName: 'id' })
  ingestedReviews: Relation<IngestedReview[]>;

  @OneToMany(() => ReviewableSnapshot, (snapshot) => snapshot.reviewable)
  @JoinColumn({ name: 'reviewable_snapshot_id', referencedColumnName: 'id' })
  reviewableSnapshots: Relation<ReviewableSnapshot[]>;

  @CreateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
    name: 'created_at',
  })
  public createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
    onUpdate: 'CURRENT_TIMESTAMP(6)',
    name: 'updated_at',
  })
  public updatedAt: Date;

  @DeleteDateColumn({ type: 'timestamp', nullable: true, name: 'deleted_at' })
  public deletedAt: Date;
}

@Entity('IngestedReviews')
export class IngestedReview extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', name: 'review_id' })
  reviewId: string;

  @ManyToOne(() => Reviewable)
  @JoinColumn({ name: 'reviewable_id', referencedColumnName: 'id' })
  reviewable: Relation<Reviewable>;

  @Column({ type: 'float', name: 'score' })
  score: number;

  @Column({ type: 'varchar', name: 'snippet' })
  snippet: string;

  @Column({ type: 'timestamp', name: 'reviewed_at' })
  reviewedAt: Date;

  @OneToOne(() => IngestedReviewResponse, (response) => response.review)
  ingestedReviewResponse: Relation<IngestedReviewResponse>;

  @CreateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
    name: 'created_at',
  })
  public createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
    onUpdate: 'CURRENT_TIMESTAMP(6)',
    name: 'updated_at',
  })
  public updatedAt: Date;

  @DeleteDateColumn({ type: 'timestamp', nullable: true, name: 'deleted_at' })
  public deletedAt: Date;
}

@Entity('IngestedReviewResponses')
export class IngestedReviewResponse extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @OneToOne(() => IngestedReview, (review) => review.ingestedReviewResponse)
  @JoinColumn({ name: 'review_id', referencedColumnName: 'id' })
  review: Relation<IngestedReview>;

  @Column({ type: 'varchar', name: 'snippet' })
  snippet: string;

  @Column({ type: 'timestamp', name: 'responded_at' })
  respondedAt: Date;

  @CreateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
    name: 'created_at',
  })
  public createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
    onUpdate: 'CURRENT_TIMESTAMP(6)',
    name: 'updated_at',
  })
  public updatedAt: Date;

  @DeleteDateColumn({ type: 'timestamp', nullable: true, name: 'deleted_at' })
  public deletedAt: Date;
}

interface ReviewTopics {
  id: string;
  keyword: string;
  mentions: number;
}

@Entity('ReviewableSnapshots')
export class ReviewableSnapshot extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Reviewable)
  @JoinColumn({ name: 'reviewable_id', referencedColumnName: 'id' })
  reviewable: Relation<Reviewable>;

  @Column({ type: 'varchar', name: 'title' })
  title: string;

  @Column({ type: 'varchar', name: 'address' })
  address: string;

  @Column({ type: 'float', name: 'rating' })
  rating: number;

  @Column({ type: 'int', name: 'review_count' })
  reviewCount: number;

  @Column({ type: 'varchar', name: 'type' })
  type: string;

  @Column({ type: 'jsonb', name: 'review_topics' })
  reviewTopics: ReviewTopics[];

  @CreateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
    name: 'created_at',
  })
  public createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
    onUpdate: 'CURRENT_TIMESTAMP(6)',
    name: 'updated_at',
  })
  public updatedAt: Date;

  @DeleteDateColumn({ type: 'timestamp', nullable: true, name: 'deleted_at' })
  public deletedAt: Date;
}
