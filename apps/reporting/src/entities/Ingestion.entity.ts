import { Business } from 'apps/business/src/entities/business/business.entity';
import { Enterprise } from 'apps/business/src/entities/enterprise/enterprise.entity';
import { BaseEntity, Column, CreateDateColumn, DeleteDateColumn, Entity, JoinColumn, ManyToOne, PrimaryGeneratedColumn, Relation, UpdateDateColumn } from 'typeorm';

@Entity('Ingestions')
export class Ingestion extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // uuid of of the harvest (or job) that created the file
  @Column({ type: 'uuid', name: 'harvest_id', nullable: true })
  harvestId: string | null;

  @ManyToOne(() => Business, { nullable: true })
  @JoinColumn({ name: 'business_id', referencedColumnName: 'id' })
  business: Relation<Business>;

  @ManyToOne(() => Enterprise, { nullable: true })
  @JoinColumn({ name: 'enterprise_id', referencedColumnName: 'id' })
  enterprise: Relation<Enterprise>;

  @Column({ type: 'varchar', name: 'ingestion_type' })
  ingestionType: string;

  /**
   * Where the ingestion came from
   */
  @Column({ type: 'varchar', name: 'external_source' })
  externalSource: string;

  /**
   * The URL of the GCS file that was ingested
   */
  @Column({ type: 'varchar', name: 'gcs_url' })
  gcsUrl: string;

  /**
   * The datetime when the ingestion was created.
   */
  @CreateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
    name: 'created_at',
  })
  public createdAt: Date;

  /**
   * The datetime when the ingestion was last updated.
   */
  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
    onUpdate: 'CURRENT_TIMESTAMP(6)',
    name: 'updated_at',
  })
  public updatedAt: Date;

  /**
   * The datetime when the ingestion was deleted.
   */
  @DeleteDateColumn({ type: 'timestamp', nullable: true, name: 'deleted_at' })
  public deletedAt: Date;
}
