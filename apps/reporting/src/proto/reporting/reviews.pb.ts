/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Error, GenericResponse, IdRequest } from "./shared.pb";

export const protobufPackage = "reporting";

export interface Reviewable {
  /** ID of the reviewable */
  id: string;
  /** Google Places ID of the reviewable */
  googlePlacesId: string;
  /** Description of the reviewable */
  description: string;
  /** Is the reviewable the business itself */
  isSelf: boolean;
  /** The reviews for the reviewable */
  reviews: Review[];
  /** The review snapshots for the reviewable */
  snapshots: ReviewableSnapshot[];
}

export interface Review {
  /** ID of the review */
  id: string;
  /** Score of the review */
  score: number;
  /** Snippet of the review */
  snippet: string;
  /** Reviewed at */
  reviewedAt: string;
  /** Responded at */
  respondedAt: string;
  /** Responded snippet */
  respondedSnippet: string;
}

export interface ReviewableSnapshot {
  /** ID of the reviewable snapshot */
  id: string;
  /** Title of the reviewable at the time of the snapshot */
  title: string;
  /** Address of the reviewable at the time of the snapshot */
  address: string;
  /** Rating of the reviewable at the time of the snapshot */
  rating: number;
  /** Review count of the reviewable at the time of the snapshot */
  reviewCount: number;
  /** Type of the reviewable at the time of the snapshot */
  type: string;
  /** Review topics associated with the reviewable at the time of the snapshot */
  reviewTopics: ReviewTopic[];
}

export interface ReviewTopic {
  /** ID of the review topic */
  id: string;
  /** Keyword of the review topic */
  keyword: string;
  /** Mentions of the review topic */
  mentions: number;
}

export interface CreateReviewableRequest {
  businessId: string;
  googlePlacesId: string;
  description?: string | undefined;
  isSelf?: boolean | undefined;
}

export interface UpdateReviewableRequest {
  reviewableId: string;
  description?: string | undefined;
  isSelf?: boolean | undefined;
}

export interface ReviewablesResponse {
  status: number;
  error?: Error | undefined;
  data: Reviewable[];
}

export interface ReviewableResponse {
  status: number;
  error?: Error | undefined;
  data: Reviewable | undefined;
}

export const REPORTING_PACKAGE_NAME = "reporting";

export interface ReviewsServiceClient {
  getReviewables(request: IdRequest, metadata?: Metadata): Observable<ReviewablesResponse>;

  getReviewable(request: IdRequest, metadata?: Metadata): Observable<ReviewableResponse>;

  createReviewable(request: CreateReviewableRequest, metadata?: Metadata): Observable<ReviewableResponse>;

  updateReviewable(request: UpdateReviewableRequest, metadata?: Metadata): Observable<ReviewableResponse>;

  deleteReviewable(request: IdRequest, metadata?: Metadata): Observable<GenericResponse>;

  refreshReviews(request: IdRequest, metadata?: Metadata): Observable<GenericResponse>;

  generateReviewInsights(request: IdRequest, metadata?: Metadata): Observable<GenericResponse>;
}

export interface ReviewsServiceController {
  getReviewables(
    request: IdRequest,
    metadata?: Metadata,
  ): Promise<ReviewablesResponse> | Observable<ReviewablesResponse> | ReviewablesResponse;

  getReviewable(
    request: IdRequest,
    metadata?: Metadata,
  ): Promise<ReviewableResponse> | Observable<ReviewableResponse> | ReviewableResponse;

  createReviewable(
    request: CreateReviewableRequest,
    metadata?: Metadata,
  ): Promise<ReviewableResponse> | Observable<ReviewableResponse> | ReviewableResponse;

  updateReviewable(
    request: UpdateReviewableRequest,
    metadata?: Metadata,
  ): Promise<ReviewableResponse> | Observable<ReviewableResponse> | ReviewableResponse;

  deleteReviewable(
    request: IdRequest,
    metadata?: Metadata,
  ): Promise<GenericResponse> | Observable<GenericResponse> | GenericResponse;

  refreshReviews(
    request: IdRequest,
    metadata?: Metadata,
  ): Promise<GenericResponse> | Observable<GenericResponse> | GenericResponse;

  generateReviewInsights(
    request: IdRequest,
    metadata?: Metadata,
  ): Promise<GenericResponse> | Observable<GenericResponse> | GenericResponse;
}

export function ReviewsServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "getReviewables",
      "getReviewable",
      "createReviewable",
      "updateReviewable",
      "deleteReviewable",
      "refreshReviews",
      "generateReviewInsights",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("ReviewsService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("ReviewsService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const REVIEWS_SERVICE_NAME = "ReviewsService";
