/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { wrappers } from "protobufjs";
import { Observable } from "rxjs";
import { Struct } from "../../google/protobuf/struct.pb";
import { Error } from "./shared.pb";

export const protobufPackage = "reporting";

export interface QueryCopilotRequest {
  query: string;
  businessId: string;
  sessionId: string;
  metadata?: string | undefined;
}

export interface QueryCopilotResponse {
  status: number;
  error: Error | undefined;
  response: CopilotAnswer | undefined;
}

export interface CopilotAnswer {
  message: string;
  /** 'text | 'sales' | 'product' | 'schedule' | 'labor' | 'staff' | 'sales-by-staff' */
  UI: string;
  data:
    | { [key: string]: any }
    | undefined;
  /** 'regular' | 'comparison' | 'tabular' | 'follow-up' */
  UIType: string;
  totalTokens: number;
  followUpQuestions: string[];
}

export const REPORTING_PACKAGE_NAME = "reporting";

wrappers[".google.protobuf.Struct"] = { fromObject: Struct.wrap, toObject: Struct.unwrap } as any;

export interface CopilotServiceClient {
  queryCopilot(request: QueryCopilotRequest, metadata?: Metadata): Observable<QueryCopilotResponse>;
}

export interface CopilotServiceController {
  queryCopilot(
    request: QueryCopilotRequest,
    metadata?: Metadata,
  ): Promise<QueryCopilotResponse> | Observable<QueryCopilotResponse> | QueryCopilotResponse;
}

export function CopilotServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["queryCopilot"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("CopilotService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("CopilotService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const COPILOT_SERVICE_NAME = "CopilotService";
