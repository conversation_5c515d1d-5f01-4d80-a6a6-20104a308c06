/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { GenericResponse } from "./shared.pb";

export const protobufPackage = "reporting";

export interface ReportingPermissionsRequest {
  businessIds: string[];
  enterpriseIds: string[];
  reviewableIds: string[];
}

export const REPORTING_PACKAGE_NAME = "reporting";

export interface ReportingAuthorizationServiceClient {
  authorize(request: ReportingPermissionsRequest, metadata?: Metadata): Observable<GenericResponse>;
}

export interface ReportingAuthorizationServiceController {
  authorize(
    request: ReportingPermissionsRequest,
    metadata?: Metadata,
  ): Promise<GenericResponse> | Observable<GenericResponse> | GenericResponse;
}

export function ReportingAuthorizationServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["authorize"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("ReportingAuthorizationService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("ReportingAuthorizationService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const REPORTING_AUTHORIZATION_SERVICE_NAME = "ReportingAuthorizationService";
