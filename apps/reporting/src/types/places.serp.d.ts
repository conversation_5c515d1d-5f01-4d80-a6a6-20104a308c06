export type SerpApiReview = {
  review_id: string;
  rating: number;
  date: string;
  iso_date: string;
  snippet: string;
  extracted_snippet?: {
    original: string;
    translated: string;
  };
  response?: {
    date: string;
    iso_date: string;
    snippet: string;
    extracted_snippet?: {
      original: string;
      translated: string;
    };
  };
};

export type SerpApiPlaceInfo = {
  title: string;
  address: string;
  rating: number;
  reviews: number;
  type: string;
};

export type SerpApiTopic = {
  keyword: string;
  mentions: number;
  id: string;
};

export type SerpApiResponse = {
  error?: string;
  place_info?: SerpApiPlaceInfo;
  topics?: SerpApiTopic[];
  reviews?: SerpApiReview[];
  serpapi_pagination?: {
    next?: string;
    next_page_token?: string;
  };
};

export type ExtractedReviewData = {
  placeInfo: SerpApiPlaceInfo | null;
  topics: SerpApiTopic[];
  reviews: SerpApiReview[];
};
