export type Reference = Record<string, string>;

type SchemaInfo = {
  enhanced_query: string;
  ctes: string[];
  time_context: {
    type: 'relative' | 'absolute' | 'none';
    start_date: string;
    end_date: string;
  };
  query_type: 'sales' | 'labor' | 'inventory' | 'menu' | 'cross_domain';
  options: { id: string; name: string }[];
  valid_query: boolean;
  recommended_follow_up_questions: string[];
  has_missing_information: boolean;
  missing_information?: { type: string; name: string }[];
  items_to_query: { type: string; name: string }[];
};

type QueryDecomposerStep = {
  steps: number;
  description: string;
  type: string;
  details: any;
  complexity: string;
  output_structure: {
    format: string;
    fields: string[];
  };
};

type QueryDecomposer = {
  steps: QueryDecomposerStep[];
};

type Validation = {
  valid: boolean;
  issues: string[];
  severity: 'ok' | 'warning' | 'error';
  suggested_query?: string;
  estimated_cost: 'low' | 'medium' | 'high';
  syntax_errors?: string[];
  UI: string;
  UIType: string;
  message: string;
};

export type QueryContext = {
  businessId: string;
  userQuery: string;
  timestamp: string;
  schemaInfo?: SchemaInfo;
  decomposition?: QueryDecomposer;
  previousSqlQuery?: string;
  sqlQuery?: string;
  validation?: Validation;
  confidence?: any;
  historicalChat: CopilotHistory[];
};
