import { IsBoolean, <PERSON>NotEmpty, <PERSON><PERSON>ptional, IsString, IsUUID } from 'class-validator';
import { CreateReviewableRequest, UpdateReviewableRequest } from '../proto/reporting/reviews.pb';

export class CreateReviewableRequestDTO implements CreateReviewableRequest {
  @IsUUID()
  @IsNotEmpty()
  businessId: string;

  @IsString()
  @IsNotEmpty()
  googlePlacesId: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsBoolean()
  @IsOptional()
  isSelf?: boolean;
}

export class UpdateReviewableRequestDTO implements UpdateReviewableRequest {
  @IsUUID()
  @IsNotEmpty()
  reviewableId: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsBoolean()
  @IsOptional()
  isSelf?: boolean;
}
