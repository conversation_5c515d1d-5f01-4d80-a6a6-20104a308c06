import { <PERSON>Enum, IsNotEmpty, <PERSON><PERSON>ptional, IsUUID } from 'class-validator';
import {
  DailyLaborReportRequest,
  DailySalesReportRequest,
  ReportType,
  TimeCardSummaryReportRequest,
  WeeklySalarySummaryReportRequest,
} from '../proto/reporting/reporting.pb';
import { Transform } from 'class-transformer';
import { IsCalendarDate } from '@tangopay/shared';
import { IdRequest } from '../proto/reporting/shared.pb';

export class IdRequestDTO implements IdRequest {
  @IsNotEmpty()
  @IsUUID()
  id: string;
}

export class OptionalIdRequestDTO implements IdRequest {
  @IsOptional()
  @IsUUID()
  id: string;
}

export class WeeklySalarySummaryReportDTO implements WeeklySalarySummaryReportRequest {
  @IsNotEmpty()
  @IsUUID()
  businessId: string;

  @Transform(({ value }) => parseInt(value))
  @IsCalendarDate()
  numericFirstDateOfTheWeek: number;

  @IsEnum(ReportType)
  format: ReportType;
}

export class TimeCardSummaryReportDTO implements TimeCardSummaryReportRequest {
  @IsNotEmpty()
  @IsUUID()
  businessId: string;

  @Transform(({ value }) => parseInt(value))
  @IsCalendarDate()
  numericDate: number;

  @IsEnum(ReportType)
  format: ReportType;
}

export class DailyLaborReportDTO implements DailyLaborReportRequest {
  @IsNotEmpty()
  @IsUUID()
  businessId: string;

  @Transform(({ value }) => parseInt(value))
  @IsCalendarDate()
  reportDate: number;

  @IsEnum(ReportType)
  format: ReportType;
}

export class DailySalesReportDTO implements DailySalesReportRequest {
  @IsNotEmpty()
  @IsUUID()
  businessId: string;

  @Transform(({ value }) => parseInt(value))
  @IsCalendarDate()
  weekStartNumericDate: number;

  @IsEnum(ReportType)
  format: ReportType;
}
