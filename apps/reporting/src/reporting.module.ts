import { Module } from '@nestjs/common';
import { ReportingController } from './controllers/reporting.controller';
import { PayrollReportingService } from './services/payroll.reporting.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ServeStaticModule } from '@nestjs/serve-static';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SharedModule, StorageModule, TangoCloudEntities } from '@tangopay/shared';
import { join } from 'path';
import { SalesReportingService } from './services/sales.reporting.service';
import { ScheduledReportingService } from './services/scheduled.service';
import { SendGridModule } from '@tangopay/shared/sendgrid';
import { Business } from 'apps/business/src/entities/business/business.entity';
import { UnityController } from './controllers/unity.controller';
import { UnityService } from './services/unity.service';
import { UnityReport } from './entities/UnityReport.entity';
import { ScheduledUnityReport } from './entities/ScheduledUnityReport';
import { FunnelsService } from './services/funnels.service';
import { ProductPaid } from 'apps/ordering/src/entities/payments/products-paid.entity';
import { ProductInOrder } from 'apps/ordering/src/entities/orders/products-in-order.entity';
import { Payment } from 'apps/ordering/src/entities/payments/payments.entity';
import { BusinessProduct, Product } from 'apps/menu/src/entities/product.entity';
import { ScheduledController } from './controllers/scheduled.controller';
import { HealthController } from '@tangopay/shared/health/grpcHealth.controller';
import { CashEvent } from 'apps/ordering/src/entities/cash-events/cash-events.entity';
import { VoidedProduct } from 'apps/ordering/src/entities/payments/voided-products.entity';
import { WorkEvent } from 'apps/staffing/src/entities/payroll/work-event.entity';
import { ReportDataService } from './services/data.service';
import { ReportDataController } from './controllers/data.controller';
import { Tab } from 'apps/ordering/src/entities/tabs/tabs.entity';
import { StripeRefund } from 'apps/ordering/src/entities/payments/stripe-refund.entity';
import { CashRefund } from 'apps/ordering/src/entities/payments/cash-refund.entity';
import { ReportGenerationController } from './controllers/generate.controller';
import { ReportGenerationService } from './services/generate.service';
import { TangoGiftCard, TangoGiftCardLoadEvent, TangoGiftCardSpendEvent } from 'apps/gift-cards/src/entities/tango-gift-card.entity';
import { CachedBusinessStats, CachedEnterpriseStats } from './entities/CachedBusinessStats.entity';
import { ReportingProduct } from 'apps/ordering/src/entities/reporting/reporting-product.entity';
import { DetailedDataRetrievalService } from './services/detailed-data/retrieval.service';
import { BusinessEnterpriseLink } from 'apps/business/src/entities/business/business-enterprise.entity';
import { ReportingProductEvent } from 'apps/ordering/src/entities/reporting/reporting-product-event.entity';
import { Reservation } from 'apps/booking/src/entities/reservation.entity';
import { WalkInParty } from 'apps/booking/src/entities/walk-in.entity';
import { StaffMember } from 'apps/staffing/src/entities/staff/staff-member.entity';
import { DetailedDataService } from './services/detailed-data/main.service';
import { DetailedDataController } from './controllers/detailed-data.controller';
import { DetailedDataCacheService } from './services/detailed-data/cache.service';
import { DetailedDataGenerationService } from './services/detailed-data/generation.service';
import { ReportingNotification } from './entities/ReportingNotifications.entity';
import { ReportNotificationService } from './services/notification.service';
import { DetailedDataInternalService } from './services/detailed-data/internal.service';
import { Enterprise } from 'apps/business/src/entities/enterprise/enterprise.entity';
import { TipOut } from './entities/Tipout.entity';
import { TipOutService } from './services/tipout.service';
import { TipOutController } from './controllers/tipout.controller';
import { InventoryEvent } from 'apps/inventory/src/entities/tracking/event.entity';
import { ActualRecurringCost, Budget, DepartmentBudget, IncidentalCost, ProjectionOverride, RecurringCost, SalesProjection } from './entities/Budget.entity';
import { Actuals } from './entities/Actuals.entity';
import { BudgetActualsService } from './services/budget/actuals.service';
import { ReportBudgetController } from './controllers/budget.controller';
import { ReportBudgetService } from './services/budget/budget.service';
import { ReportBudgetCalculationService } from './services/budget/calculation.service';
import { BudgetCostsService } from './services/budget/costs.service';
import { AnalysisController } from './controllers/analysis.controller';
import { AnalysisService } from './services/analysis/analysis.service';
import { InsightDialogue, InsightDialogueAction, InsightDialogueMessage } from './entities/Dialog.entity';
import { ReportInsightsController } from './controllers/insights.controller';
import { ReportInsightsService } from './services/insights.service';
import { OpenAIModule } from '@tangopay/shared/openai/openai.module';
import { AnthropicModule } from '@tangopay/shared/anthropic/anthropic.module';
import { CachedStaffProfitability, CachedTabProfitability } from './entities/CachedPerformanceStats.entity';
import { ProfitabilityService } from './services/analysis/profitability.service';
import { CustomerSatisfaction } from 'apps/ordering/src/entities/reporting/customer-satisfaction.entity';
import { ReportDataIngestionService } from './services/ingest.service';
import { ReportDataIngestionController } from './controllers/ingest.controller';
import { Ingestion } from './entities/Ingestion.entity';
import { ReportRecipient } from './entities/ReportRecipient.entity';
import { ReportMailingService } from './services/mailing.service';
import { ReportMailingController } from './controllers/mailing.controller';
import { ReportIngestionSearcherService } from './services/ingest/searcher.service';
import { Role } from 'apps/business/src/entities/enterprise/role.entity';
import { Department } from 'apps/business/src/entities/enterprise/department.entity';
import { PayRate } from 'apps/staffing/src/entities/payroll/pay-rate.entity';
import { DetailedDataAverageCacheService } from './services/detailed-data/average.service';
import { AverageCachedBusinessStats, AverageCachedBusinessStatSample } from './entities/AverageCachedBusinessStats.entity';
import { ReportIngestionMaitredService } from './services/ingest/maitred.service';
import { ReportIngestionToastService } from './services/ingest/toast.service';
import { ModifierOption } from 'apps/menu/src/entities/modifier-option.entity';
import { Modifier } from 'apps/menu/src/entities/modifier.entity';
import { MenuCategory, MenuCategoryProduct } from 'apps/menu/src/entities/menu-category.entity';
import { Menu, MenuCategoryLink } from 'apps/menu/src/entities/menu.entity';
import { Order } from 'apps/ordering/src/entities/orders/orders.entity';
import { PaysFor } from 'apps/ordering/src/entities/payments/pays-for.entity';
import { DataLink } from './entities/DataLink.entity';
import { DataHarvest, HarvestedBinaryData, HarvestedFile } from './entities/DataHarvest.entity';
import { HarvesterController } from './controllers/harvester.controller';
import { ReportDataHarvesterService } from './services/harvester.service';
import { ReportDataToastHarvesterService } from './services/harvester/toast.service';
import { ReportDataMarginEdgeHarvesterService } from './services/harvester/marginedge.service';
import { InventoryPricedItem, PricedItemBusinessInfo } from 'apps/inventory/src/entities/lite/priced-item.entity';
import { ReportIngestionMarginEdgeService } from './services/ingest/marginedge.service';
import { PricedIngredient } from 'apps/inventory/src/entities/lite/priced-ingredient.entity';
import { PricedRecipe } from 'apps/inventory/src/entities/lite/priced-recipe.entity';
import { PricedItemEvent } from 'apps/inventory/src/entities/lite/priced-item-event.entity';
import { ForecastingService } from './services/forecasting.service';
import { ForecastingController } from './controllers/forecasting.controller';
import { CachedForecast } from './entities/CachedForecast.entity';
import { VendorItemPackaging } from './entities/VendorItemPackaging.entity';
import { ReportDataMarginEdgePublicHarvesterService } from './services/harvester/marginedge-public.service';
import { ReportIngestionMarginEdgePublicService } from './services/ingest/marginedge-public.service';
import { StringMatchingService } from './services/ingest/string-matching.service';
import { ReportKPIService } from './services/kpi.service';
import { ReportKPIsController } from './controllers/kpi.controller';
import { StripeSource } from 'apps/ordering/src/entities/payments/source.stripe.entity';
import { CashSource } from 'apps/ordering/src/entities/payments/source.cash.entity';
import { GiftCardSource } from 'apps/ordering/src/entities/payments/source.gift-card.entity';
import { LossSource } from 'apps/ordering/src/entities/payments/source.loss.entity';
import { ExternalSource } from 'apps/ordering/src/entities/payments/source.external.entity';
import { MarginEdgeDigestService } from './services/digest/marginedge-digest.service';
import { DigestController } from './controllers/digest.controller';
import { KPISettings } from './entities/KPISettings.entity';
import { ReportKPIQueriesService } from './services/kpis/queries.service';
import { ReportKPITimeSeriesService } from './services/kpis/time-series.service';
import { ReportDataResyHarvesterService } from './services/harvester/resy.service';
import { ReportIngestionResyService } from './services/ingest/resy.service';
import { Table } from 'apps/booking/src/entities/table.entity';
import { ReportIngestionSevenShiftsService } from './services/ingest/seven-shifts.service';
import { Shift } from 'apps/scheduling/src/entities/scheduling/shift.entity';
import { ResyDigestService } from './services/digest/resy-digest.service';
import { AvailabilityLite } from 'apps/scheduling/src/entities/scheduling/availability-lite.entity';
import { RemoteHarvester } from './entities/RemoteHarvester.entity';
import { RemoteHarvesterCredential } from './entities/RemoteHarvesterCredentials.entity';
import { RemoteHarvesterService } from './services/remote-harvester.service';
import { RemoteHarvesterController } from './controllers/remote-harvester.controller';
import { DetailedDataSpeedService } from './services/detailed-data/speed.service';
import { RemoteHarvesterJob } from './entities/RemoteHarvesterJob.entity';
import { TangoRemoteHarvesterService } from './services/harvester/tango-remote.service';
import { ToastRemoteHarvesterService } from './services/harvester/toast-remote.service';
import { RemoteHarvesterSchedule } from './entities/RemoteHarvestSchedule.entity';
import { Insight, InsightInbox } from './entities/Insight.entity';
import { Reviewable, IngestedReview, IngestedReviewResponse, ReviewableSnapshot } from './entities/GoogleReview.entity';
import { ReviewsController } from './controllers/reviews.controller';
import { ReviewsService } from './services/reviews.service';
import { ReportingAuthService } from './services/authorization.service';
import ReportingAuthorizationController from './controllers/authorization.controller';
import { InsightQueriesService } from './services/queries/insights-queries.service';
import { ReportDialogueService } from './services/dialogue.service';
import { User } from 'apps/auth/src/entities/user.entity';
import { ForecastQueriesService } from './services/queries/forecast-queries.service';
import { ScheduleForecastService } from './services/forecasting/schedule.service';
import { ReportIngestionSlingService } from './services/ingest/sling.service';
import { SalesForecastService } from './services/forecasting/sales.service';
import { LabourForecastService } from './services/forecasting/labour.service';
import { SlingRemoteHarvesterService } from './services/harvester/sling-remote.service';
import { PushRemoteHarvesterService } from './services/harvester/push-remote.service';
import { ReportIngestionPushService } from './services/ingest/push.service';
import { CopilotController } from './controllers/copilot.controller';
import { CopilotService } from './services/copilot.service';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    StorageModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => {
        return {
          projectId: config.get<string>('GCLOUD_PROJECT_ID'),
          privateKey: config.get<string>('GCLOUD_STORAGE_SERVICE_ACCOUNT_PK'),
          clientEmail: config.get<string>('GCLOUD_STORAGE_SERVICE_ACCOUNT_CLIENT_EMAIL'),
          mediaBucket: config.get<string>('GCLOUD_STORAGE_SERVICE_ACCOUNT_MEDIA_BUCKET'),
        };
      },
      inject: [ConfigService],
    }),
    SendGridModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => {
        return {
          apikey: config.get<string>('SENDGRID_API_KEY'),
        };
      },
      inject: [ConfigService],
    }),
    OpenAIModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        apikey: config.get<string>('OPENAI_API_KEY'),
      }),
      inject: [ConfigService],
    }),
    AnthropicModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => ({
        apikey: config.get<string>('ANTHROPIC_API_KEY'),
      }),
      inject: [ConfigService],
    }),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'public'),
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => {
        return {
          type: 'postgres',
          host: config.get<string>('POSTGRES_HOST'),
          port: config.get<number>('POSTGRES_PORT'),
          username: config.get<string>('POSTGRES_USERNAME'),
          password: config.get<string>('POSTGRES_PASSWORD'),
          database: config.get<string>('POSTGRES_DATABASE'),
          entities: [...TangoCloudEntities],
          synchronize: false,
          migrationsTransactionMode: 'each',
          useUTC: true,
        };
      },
      inject: [ConfigService],
    }),
    SendGridModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => {
        return {
          apikey: config.get<string>('SENDGRID_API_KEY'),
        };
      },
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      Enterprise,
      Business,
      StaffMember,
      Role,
      Department,
      PayRate,
      UnityReport,
      ScheduledUnityReport,
      ProductPaid,
      ProductInOrder,
      Payment,
      BusinessProduct,
      CashEvent,
      VoidedProduct,
      WorkEvent,
      Tab,
      InventoryEvent,
      StripeRefund,
      CashRefund,
      TangoGiftCardLoadEvent,
      TangoGiftCardSpendEvent,
      CachedBusinessStats,
      AverageCachedBusinessStats,
      AverageCachedBusinessStatSample,
      CachedEnterpriseStats,
      CachedTabProfitability,
      CustomerSatisfaction,
      CachedStaffProfitability,
      ReportingProduct,
      ReportingProductEvent,
      BusinessEnterpriseLink,
      TangoGiftCard,
      Reservation,
      Table,
      WalkInParty,
      ReportingNotification,
      TipOut,
      // Budget Entities
      Budget,
      Actuals,
      DepartmentBudget,
      SalesProjection,
      RecurringCost,
      ActualRecurringCost,
      IncidentalCost,
      ProjectionOverride,
      // Insight Entities
      Insight,
      InsightInbox,
      InsightDialogue,
      InsightDialogueMessage,
      InsightDialogueAction,
      // Ingestion Entities
      Ingestion,
      // AI Agent
      ReportRecipient,
      // Menu Entities
      ModifierOption,
      Modifier,
      Product,
      MenuCategoryProduct,
      MenuCategory,
      Menu,
      MenuCategoryLink,
      Order,
      PaysFor,
      StripeSource,
      CashSource,
      GiftCardSource,
      LossSource,
      ExternalSource,
      // Harvesting Data
      DataLink,
      DataHarvest,
      HarvestedFile,
      HarvestedBinaryData,
      // Competitors
      Reviewable,
      IngestedReview,
      IngestedReviewResponse,
      ReviewableSnapshot,
      User,

      // Inventory Lite
      InventoryPricedItem,
      PricedItemBusinessInfo,
      PricedIngredient,
      PricedRecipe,
      PricedItemEvent,
      VendorItemPackaging,

      // Forecast
      CachedForecast,

      // KPISettings
      KPISettings,

      // Scheduling
      Shift,
      AvailabilityLite,

      // Remote Harvester
      RemoteHarvester,
      RemoteHarvesterCredential,
      RemoteHarvesterJob,
      RemoteHarvesterSchedule,
    ]),
    SharedModule,
  ],
  controllers: [
    ReportingAuthorizationController,
    ReportingController,
    UnityController,
    ScheduledController,
    ReportDataController,
    ReportGenerationController,
    HealthController,
    DetailedDataController,
    TipOutController,
    ReportBudgetController,
    AnalysisController,
    ReviewsController,
    ReportInsightsController,
    ReportDataIngestionController,
    ReportMailingController,
    HarvesterController,
    DigestController,
    ForecastingController,
    ReportKPIsController,
    RemoteHarvesterController,
    CopilotController,
  ],
  providers: [
    ReportingAuthService,
    PayrollReportingService,
    SalesReportingService,
    ScheduledReportingService,
    UnityService,
    FunnelsService,
    ReportDataService,
    ReportGenerationService,
    DetailedDataGenerationService,
    DetailedDataRetrievalService,
    DetailedDataCacheService,
    DetailedDataInternalService,
    DetailedDataService,
    DetailedDataAverageCacheService,
    ReportNotificationService,
    TipOutService,
    BudgetActualsService,
    ReportBudgetService,
    ReportBudgetCalculationService,
    BudgetCostsService,
    AnalysisService,
    ProfitabilityService,
    ReviewsService,
    ReportInsightsService,
    ReportDialogueService,
    InsightQueriesService,
    ForecastQueriesService,
    ReportDataIngestionService,
    ReportIngestionSearcherService,
    ReportIngestionMaitredService,
    ReportIngestionToastService,
    ReportIngestionMarginEdgeService,
    ReportIngestionMarginEdgePublicService,
    ReportIngestionSevenShiftsService,
    ReportIngestionSlingService,
    ReportIngestionPushService,
    ReportMailingService,
    ReportDataHarvesterService,
    ReportDataToastHarvesterService,
    ReportDataMarginEdgeHarvesterService,
    ReportDataMarginEdgePublicHarvesterService,
    ReportDataResyHarvesterService,
    ReportIngestionResyService,
    MarginEdgeDigestService,
    ResyDigestService,
    ScheduleForecastService,
    SalesForecastService,
    LabourForecastService,
    ForecastingService,
    StringMatchingService,
    ReportKPIService,
    ReportKPIQueriesService,
    ReportKPITimeSeriesService,
    DetailedDataSpeedService,
    // Remote Harvester services
    TangoRemoteHarvesterService,
    ToastRemoteHarvesterService,
    SlingRemoteHarvesterService,
    PushRemoteHarvesterService,
    RemoteHarvesterService,
    CopilotService,
  ],
})
export class ReportingModule {}
