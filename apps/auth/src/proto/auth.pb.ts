/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";

export const protobufPackage = "auth";

export interface PreAuthorisationParameters {
  key: string;
  value: string;
}

export interface CreatePreAuthorisedSessionRequest {
  /** type = 'scheduling' */
  sessionType: string;
  /** what routes the session has access to, if empty, all routes are allowed */
  routes: string[];
  /**
   * what business ids the session has access to, if empty, no business ids are
   * allowed
   */
  businessIds: string[];
  /**
   * what enterprise ids the session has access to, if empty, no enterprise ids
   * are allowed
   */
  enterpriseIds: string[];
  /** what email the session was sent to */
  sentTo: string;
  /** parameters for the session */
  parameters: PreAuthorisationParameters[];
  /** when the session expires */
  expiresAt: string;
}

export interface PreAuthorisedSession {
  id: string;
  sessionType: string;
  routes: string[];
  businessIds: string[];
  enterpriseIds: string[];
  sentTo: string;
  token: string;
  parameters: PreAuthorisationParameters[];
  adminUrl: string;
  createdAt: string;
  expiresAt: string;
}

export interface PreAuthorisedSessionResponse {
  status: number;
  error?: Error | undefined;
  data: PreAuthorisedSession | undefined;
}

export interface SignUrlRequest {
  url: string;
}

export interface SignUrlResponse {
  status: number;
  error?: Error | undefined;
  data: string;
}

export interface CheckUrlSignatureRequest {
  url: string;
  signature: string;
}

export interface EmailRequest {
  email: string;
}

export interface ExistingUserRequest {
  email?: string | undefined;
  phoneNumber?: string | undefined;
}

export interface ExistingUserResponse {
  status: number;
  error?: Error | undefined;
  data: boolean;
}

export interface LoginSession {
  id: string;
  ipAddress: string;
  method: string;
  platform: string;
  twoFactor: boolean;
  createdAt: string;
  lastSeenAt: string;
  fcmToken?: string | undefined;
}

export interface LoginSessionResponse {
  status: number;
  error?: Error | undefined;
  data: LoginSession[];
}

export interface ChangeEmailRequest {
  userId: string;
  newEmail: string;
}

export interface ChangePhoneNumberRequest {
  userId: string;
  newPhoneNumber: string;
}

export interface ConfirmTOTPRequest {
  challengeId: string;
  code: string;
}

export interface ElevateTokenRequest {
  userId: string;
  accessToken: string;
}

export interface SecureRequest {
  userId: string;
  passwordHash: string;
}

export interface Get2FATOTPSecretResponse {
  status: number;
  error?: Error | undefined;
  data?: string | undefined;
}

export interface VerificationTokenRequest {
  token: string;
}

export interface URL {
  url: string;
}

export interface VerificationResponse {
  status: number;
  error?: Error | undefined;
  data: URL | undefined;
}

export interface IdRequest {
  id: string;
}

export interface Error {
  type: string;
  message: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  userId: string;
  totpChallenge: string;
  userPhoneLast4: string;
}

export interface UserAddress {
  line1: string;
  line2?: string | undefined;
  city: string;
  zip: string;
  state: string;
  country: string;
}

export interface EmergencyContact {
  emergencyContactName: string;
  emergencyContactRelation: string;
  emergencyContactPhoneNumber: string;
}

export interface User {
  firstName: string;
  lastName: string;
  email: string;
  dateOfBirth: string;
  address: UserAddress | undefined;
  emergencyContact: EmergencyContact | undefined;
  id: string;
  phoneNumber?: string | undefined;
  phoneNumberVerified: boolean;
  emailVerified: boolean;
  publicOnboarding: boolean;
}

export interface GetCurrentUserProfileRequest {
  userId: string;
}

export interface AvailableBusiness {
  id: string;
  name: string;
  foodAndBeverageEnterpriseId?: string | undefined;
  reputationManagementEnterpriseId?: string | undefined;
  giftCardsEnterpriseId?: string | undefined;
  enterpriseIds: string[];
  staffId?: string | undefined;
}

export interface AvailableEnterprise {
  id: string;
  name: string;
  staffId?: string | undefined;
}

export interface AvailableUserProfilesData {
  user: User | undefined;
  availableBusinesses: AvailableBusiness[];
  availableEnterprises: AvailableEnterprise[];
  isSuperAdmin?: boolean | undefined;
}

export interface GetCurrentUserProfileResponse {
  status: number;
  error: string[];
  data?: AvailableUserProfilesData | undefined;
}

/** RefreshTokenRequest */
export interface RefreshTokenRequest {
  refreshToken: string;
}

/** RefreshTokenResponse */
export interface RefreshTokenResponse {
  status: number;
  error?: Error | undefined;
  data?: AuthTokens | undefined;
}

/** VerifyPhoneRequest */
export interface VerifyPhoneRequest {
  phoneNumber: string;
  code: string;
  platform?: string | undefined;
  ip?: string | undefined;
}

/** VerifyPhoneRequest */
export interface VerifyEmailRequest {
  email: string;
  code: string;
  platform?: string | undefined;
  ip?: string | undefined;
}

/** SignInWithPhoneRequest */
export interface SignInWithPhoneRequest {
  phoneNumber: string;
  platform?: string | undefined;
}

/** SignInWithEmailRequest */
export interface SignInWithEmailRequest {
  email: string;
  platform?: string | undefined;
}

/** CreateUser */
export interface CreateUserRequest {
  email: string;
  password: string;
  phoneNumber?: string | undefined;
  firstName: string;
  lastName: string;
  publicOnboarding?: boolean | undefined;
}

/** GetUser */
export interface GetUserRequest {
  userId: string;
}

export interface UserResponse {
  status: number;
  error?: Error | undefined;
  data?: User | undefined;
}

/** EditUser */
export interface UpdateUserRequest {
  userId: string;
  firstName?: string | undefined;
  lastName?: string | undefined;
  email?: string | undefined;
  phone?: string | undefined;
  dateOfBirth?: string | undefined;
  address?: UserAddress | undefined;
}

/** DeleteUser */
export interface DeleteUserRequest {
  userId: string;
}

/** Get All Users */
export interface GetAllUsersRequest {
  businessId: string;
}

export interface UsersResponse {
  status: number;
  error?: Error | undefined;
  data: User[];
}

/** Login */
export interface LoginRequest {
  email: string;
  password: string;
  platform?: string | undefined;
  ip?: string | undefined;
  fcmToken?: string | undefined;
}

export interface PhoneLoginRequest {
  phoneNumber: string;
  password: string;
  platform?: string | undefined;
  ip?: string | undefined;
}

export interface LoginResponse {
  status: number;
  error?: Error | undefined;
  data?: AuthTokens | undefined;
}

/** Validate */
export interface ValidateRequest {
  token: string;
}

export interface ValidateResponse {
  status: number;
  error?: Error | undefined;
  data?: ValidateResponse_Data | undefined;
}

export interface ValidateResponse_Data {
  userId?:
    | string
    | undefined;
  /** businesses the token acts on behalf of */
  businessIds: string[];
  /** enterprises the token acts on behalf of */
  enterpriseIds: string[];
  /** businesses the token acts on behalf of in the food and bev context */
  foodAndBevBusinessIds: string[];
  /** enterprises the token acts on behalf of in the food and bev context */
  foodAndBevEnterpriseIds: string[];
  /** businesses the token acts on behalf of in the reputation context */
  reputationBusinessIds: string[];
  /** enterprises the token acts on behalf of in the reputation context */
  reputationEnterpriseIds: string[];
  /** businesses the token acts on behalf of in the gift card context */
  giftCardBusinessIds: string[];
  /** enterprises the token acts on behalf of in the gift card context */
  giftCardEnterpriseIds: string[];
  isSuperUser: boolean;
  /** staff members the token acts on behalf of */
  staffIds: string[];
  /** the routes the token has access to */
  routes: string[];
  /** Parameters associated with a pre-authorised session */
  parameters: PreAuthorisationParameters[];
  /** the session id of the login session */
  sessionId: string;
}

export interface UpdatePasswordRequest {
  userId: string;
  newPassword: string;
  previousPassword?: string | undefined;
  token?: string | undefined;
}

export interface GenericResponse {
  status: number;
  error?: Error | undefined;
}

export interface EmptyRequest {
}

export interface ActiveSession {
  id: string;
  userId: string;
  loggedInAt: string;
  fcmToken: string;
}

export interface FCMTokenRequest {
  sessionId: string;
  fcmToken: string;
}

export const AUTH_PACKAGE_NAME = "auth";

export interface AuthServiceClient {
  /** // Retrieval */

  getAllUsers(request: GetAllUsersRequest, metadata?: Metadata): Observable<UsersResponse>;

  getUser(request: GetUserRequest, metadata?: Metadata): Observable<UserResponse>;

  getCurrentUserProfile(
    request: GetCurrentUserProfileRequest,
    metadata?: Metadata,
  ): Observable<GetCurrentUserProfileResponse>;

  /** Get the login session history */

  getLoginHistory(request: IdRequest, metadata?: Metadata): Observable<LoginSessionResponse>;

  /** // CRUD */

  createUser(request: CreateUserRequest, metadata?: Metadata): Observable<UserResponse>;

  updateUser(request: UpdateUserRequest, metadata?: Metadata): Observable<UserResponse>;

  deleteUser(request: DeleteUserRequest, metadata?: Metadata): Observable<GenericResponse>;

  /** // Login */

  login(request: LoginRequest, metadata?: Metadata): Observable<LoginResponse>;

  phoneLogin(request: PhoneLoginRequest, metadata?: Metadata): Observable<LoginResponse>;

  signInWithPhone(request: SignInWithPhoneRequest, metadata?: Metadata): Observable<GenericResponse>;

  signInWithEmail(request: SignInWithEmailRequest, metadata?: Metadata): Observable<GenericResponse>;

  attachFcmToken(request: FCMTokenRequest, metadata?: Metadata): Observable<GenericResponse>;

  getActiveSessionsForUser(request: IdRequest, metadata?: Metadata): Observable<LoginSessionResponse>;

  logout(request: IdRequest, metadata?: Metadata): Observable<GenericResponse>;

  /** // Stage 2 of login + 2FA */

  verifyPhoneNumber(request: VerifyPhoneRequest, metadata?: Metadata): Observable<LoginResponse>;

  verifyEmail(request: VerifyEmailRequest, metadata?: Metadata): Observable<LoginResponse>;

  /** Confirm the TOTP code */

  confirmTotp(request: ConfirmTOTPRequest, metadata?: Metadata): Observable<LoginResponse>;

  /** // Update/Change Password */

  updatePassword(request: UpdatePasswordRequest, metadata?: Metadata): Observable<GenericResponse>;

  sendResetPasswordLink(request: SignInWithEmailRequest, metadata?: Metadata): Observable<GenericResponse>;

  /**
   * // Verify + Change Email/Phone
   * Send email verification
   */

  sendEmailVerification(request: IdRequest, metadata?: Metadata): Observable<GenericResponse>;

  /** Send phone verification */

  sendPhoneNumberVerification(request: IdRequest, metadata?: Metadata): Observable<GenericResponse>;

  /** Send email change verification */

  changeEmail(request: ChangeEmailRequest, metadata?: Metadata): Observable<GenericResponse>;

  /** Send phone change verification */

  changePhoneNumber(request: ChangePhoneNumberRequest, metadata?: Metadata): Observable<GenericResponse>;

  /** Verify phone or email */

  verifyEmailOrPhone(request: VerificationTokenRequest, metadata?: Metadata): Observable<VerificationResponse>;

  /** // Internal/Hidden methods */

  validate(request: ValidateRequest, metadata?: Metadata): Observable<ValidateResponse>;

  refreshToken(request: RefreshTokenRequest, metadata?: Metadata): Observable<RefreshTokenResponse>;

  existingUser(request: ExistingUserRequest, metadata?: Metadata): Observable<ExistingUserResponse>;

  signUrl(request: SignUrlRequest, metadata?: Metadata): Observable<SignUrlResponse>;

  checkUrlSignature(request: CheckUrlSignatureRequest, metadata?: Metadata): Observable<GenericResponse>;

  getUserByEmail(request: EmailRequest, metadata?: Metadata): Observable<UserResponse>;

  createPreAuthorisedSession(
    request: CreatePreAuthorisedSessionRequest,
    metadata?: Metadata,
  ): Observable<PreAuthorisedSessionResponse>;

  getPreAuthorisedSession(request: IdRequest, metadata?: Metadata): Observable<PreAuthorisedSessionResponse>;
}

export interface AuthServiceController {
  /** // Retrieval */

  getAllUsers(
    request: GetAllUsersRequest,
    metadata?: Metadata,
  ): Promise<UsersResponse> | Observable<UsersResponse> | UsersResponse;

  getUser(
    request: GetUserRequest,
    metadata?: Metadata,
  ): Promise<UserResponse> | Observable<UserResponse> | UserResponse;

  getCurrentUserProfile(
    request: GetCurrentUserProfileRequest,
    metadata?: Metadata,
  ): Promise<GetCurrentUserProfileResponse> | Observable<GetCurrentUserProfileResponse> | GetCurrentUserProfileResponse;

  /** Get the login session history */

  getLoginHistory(
    request: IdRequest,
    metadata?: Metadata,
  ): Promise<LoginSessionResponse> | Observable<LoginSessionResponse> | LoginSessionResponse;

  /** // CRUD */

  createUser(
    request: CreateUserRequest,
    metadata?: Metadata,
  ): Promise<UserResponse> | Observable<UserResponse> | UserResponse;

  updateUser(
    request: UpdateUserRequest,
    metadata?: Metadata,
  ): Promise<UserResponse> | Observable<UserResponse> | UserResponse;

  deleteUser(
    request: DeleteUserRequest,
    metadata?: Metadata,
  ): Promise<GenericResponse> | Observable<GenericResponse> | GenericResponse;

  /** // Login */

  login(request: LoginRequest, metadata?: Metadata): Promise<LoginResponse> | Observable<LoginResponse> | LoginResponse;

  phoneLogin(
    request: PhoneLoginRequest,
    metadata?: Metadata,
  ): Promise<LoginResponse> | Observable<LoginResponse> | LoginResponse;

  signInWithPhone(
    request: SignInWithPhoneRequest,
    metadata?: Metadata,
  ): Promise<GenericResponse> | Observable<GenericResponse> | GenericResponse;

  signInWithEmail(
    request: SignInWithEmailRequest,
    metadata?: Metadata,
  ): Promise<GenericResponse> | Observable<GenericResponse> | GenericResponse;

  attachFcmToken(
    request: FCMTokenRequest,
    metadata?: Metadata,
  ): Promise<GenericResponse> | Observable<GenericResponse> | GenericResponse;

  getActiveSessionsForUser(
    request: IdRequest,
    metadata?: Metadata,
  ): Promise<LoginSessionResponse> | Observable<LoginSessionResponse> | LoginSessionResponse;

  logout(
    request: IdRequest,
    metadata?: Metadata,
  ): Promise<GenericResponse> | Observable<GenericResponse> | GenericResponse;

  /** // Stage 2 of login + 2FA */

  verifyPhoneNumber(
    request: VerifyPhoneRequest,
    metadata?: Metadata,
  ): Promise<LoginResponse> | Observable<LoginResponse> | LoginResponse;

  verifyEmail(
    request: VerifyEmailRequest,
    metadata?: Metadata,
  ): Promise<LoginResponse> | Observable<LoginResponse> | LoginResponse;

  /** Confirm the TOTP code */

  confirmTotp(
    request: ConfirmTOTPRequest,
    metadata?: Metadata,
  ): Promise<LoginResponse> | Observable<LoginResponse> | LoginResponse;

  /** // Update/Change Password */

  updatePassword(
    request: UpdatePasswordRequest,
    metadata?: Metadata,
  ): Promise<GenericResponse> | Observable<GenericResponse> | GenericResponse;

  sendResetPasswordLink(
    request: SignInWithEmailRequest,
    metadata?: Metadata,
  ): Promise<GenericResponse> | Observable<GenericResponse> | GenericResponse;

  /**
   * // Verify + Change Email/Phone
   * Send email verification
   */

  sendEmailVerification(
    request: IdRequest,
    metadata?: Metadata,
  ): Promise<GenericResponse> | Observable<GenericResponse> | GenericResponse;

  /** Send phone verification */

  sendPhoneNumberVerification(
    request: IdRequest,
    metadata?: Metadata,
  ): Promise<GenericResponse> | Observable<GenericResponse> | GenericResponse;

  /** Send email change verification */

  changeEmail(
    request: ChangeEmailRequest,
    metadata?: Metadata,
  ): Promise<GenericResponse> | Observable<GenericResponse> | GenericResponse;

  /** Send phone change verification */

  changePhoneNumber(
    request: ChangePhoneNumberRequest,
    metadata?: Metadata,
  ): Promise<GenericResponse> | Observable<GenericResponse> | GenericResponse;

  /** Verify phone or email */

  verifyEmailOrPhone(
    request: VerificationTokenRequest,
    metadata?: Metadata,
  ): Promise<VerificationResponse> | Observable<VerificationResponse> | VerificationResponse;

  /** // Internal/Hidden methods */

  validate(
    request: ValidateRequest,
    metadata?: Metadata,
  ): Promise<ValidateResponse> | Observable<ValidateResponse> | ValidateResponse;

  refreshToken(
    request: RefreshTokenRequest,
    metadata?: Metadata,
  ): Promise<RefreshTokenResponse> | Observable<RefreshTokenResponse> | RefreshTokenResponse;

  existingUser(
    request: ExistingUserRequest,
    metadata?: Metadata,
  ): Promise<ExistingUserResponse> | Observable<ExistingUserResponse> | ExistingUserResponse;

  signUrl(
    request: SignUrlRequest,
    metadata?: Metadata,
  ): Promise<SignUrlResponse> | Observable<SignUrlResponse> | SignUrlResponse;

  checkUrlSignature(
    request: CheckUrlSignatureRequest,
    metadata?: Metadata,
  ): Promise<GenericResponse> | Observable<GenericResponse> | GenericResponse;

  getUserByEmail(
    request: EmailRequest,
    metadata?: Metadata,
  ): Promise<UserResponse> | Observable<UserResponse> | UserResponse;

  createPreAuthorisedSession(
    request: CreatePreAuthorisedSessionRequest,
    metadata?: Metadata,
  ): Promise<PreAuthorisedSessionResponse> | Observable<PreAuthorisedSessionResponse> | PreAuthorisedSessionResponse;

  getPreAuthorisedSession(
    request: IdRequest,
    metadata?: Metadata,
  ): Promise<PreAuthorisedSessionResponse> | Observable<PreAuthorisedSessionResponse> | PreAuthorisedSessionResponse;
}

export function AuthServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "getAllUsers",
      "getUser",
      "getCurrentUserProfile",
      "getLoginHistory",
      "createUser",
      "updateUser",
      "deleteUser",
      "login",
      "phoneLogin",
      "signInWithPhone",
      "signInWithEmail",
      "attachFcmToken",
      "getActiveSessionsForUser",
      "logout",
      "verifyPhoneNumber",
      "verifyEmail",
      "confirmTotp",
      "updatePassword",
      "sendResetPasswordLink",
      "sendEmailVerification",
      "sendPhoneNumberVerification",
      "changeEmail",
      "changePhoneNumber",
      "verifyEmailOrPhone",
      "validate",
      "refreshToken",
      "existingUser",
      "signUrl",
      "checkUrlSignature",
      "getUserByEmail",
      "createPreAuthorisedSession",
      "getPreAuthorisedSession",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("AuthService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("AuthService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const AUTH_SERVICE_NAME = "AuthService";
