import { HttpStatus, Inject, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { authenticator } from 'otplib';
import { <PERSON><PERSON>, IsNull, <PERSON><PERSON><PERSON>, Not, Repository } from 'typeorm';
import { StaffMember } from 'apps/staffing/src/entities/staff/staff-member.entity';
import utc from 'dayjs/plugin/utc';
import tz from 'dayjs/plugin/timezone';
import dayjs from 'dayjs';
dayjs.extend(utc);
dayjs.extend(tz);
import {
  UserConflictException,
  InvalidTokenException,
  NoPasswordOrTokenException,
  NoStaffFoundException,
  NoUsersFoundException,
  PasswordIncorrectException,
  UserNotFoundException,
} from '@tangopay/shared/errors';
import { User, UserAddress } from '../entities/user.entity';
import {
  AvailableBusiness,
  AvailableEnterprise,
  EmailRequest,
  ExistingUserResponse,
  GenericResponse,
  GetCurrentUserProfileResponse,
  LoginSessionResponse,
  UserResponse,
  UsersResponse,
} from '../proto/auth.pb';
import {
  CreateUserRequestDto,
  ExistingUserRequestDto,
  GetCurrentUserProfileDto,
  GetUserRequestDto,
  IdRequestDto,
  UpdatePasswordRequestDto,
  UpdateUserRequestDto,
} from '../dtos/auth.dto';
import { JwtService } from './jwt.service';
import { LoginSession } from '../entities/session.entity';
import { toUser } from '../utils/responses';

@Injectable()
export class AuthUserService {
  private readonly logger = new Logger(AuthUserService.name);
  @InjectRepository(User)
  private readonly repository: Repository<User>;

  @InjectRepository(UserAddress)
  private readonly addressRepository: Repository<UserAddress>;

  @InjectRepository(StaffMember)
  private readonly staffRepository: Repository<StaffMember>;

  @InjectRepository(LoginSession)
  private readonly loginSessionRepository: Repository<LoginSession>;

  @Inject(JwtService)
  private readonly jwtService: JwtService;

  constructor() {}

  public async getAllUsers(): Promise<UsersResponse> {
    const users = await this.repository
      .createQueryBuilder('user')
      .loadRelationCountAndMap('user.totalStaffMembers', 'user.staffMembers')
      .getMany();

    return {
      status: HttpStatus.OK,
      data: users
        .filter((user) => {
          if (dayjs().subtract(1, 'day').isBefore(user.createdAt)) return true;
          if (user.totalStaffMembers > 0) return true;
          return false;
        })
        .map((user) => {
          return toUser(user);
        }),
    };
  }

  async createUser({
    email,
    phoneNumber,
    password,
    firstName,
    lastName,
    publicOnboarding,
  }: CreateUserRequestDto): Promise<UserResponse> {
    let user: User = await this.repository.findOne({
      where: [{ email: ILike(email) }, { phoneNumber }],
    });

    if (user) throw new UserConflictException();

    user = new User();
    user.email = email;
    user.passwordHash = this.jwtService.encodePassword(password);
    user.firstName = firstName;
    user.lastName = lastName;
    user.phoneNumber = phoneNumber;
    user.totpSecret = authenticator.generateSecret();
    user.publicOnboarding = publicOnboarding ?? false;

    const result = await this.repository.save(user);

    return {
      status: HttpStatus.CREATED,
      error: null,
      data: toUser(result),
    };
  }

  public async getCurrentUserProfiles({
    userId,
  }: GetCurrentUserProfileDto): Promise<GetCurrentUserProfileResponse> {
    const user: User = await this.repository.findOne({
      where: { id: userId },
      relations: {
        addresses: true,
      },
    });

    if (!user) throw new UserNotFoundException(userId);

    const staffMembersForUser = await this.staffRepository.find({
      where: { user: { id: userId } },
      relations: {
        business: {
          enterpriseLinks: {
            enterprise: true,
          },
        },
        enterprise: true,
      },
    });

    const availableBusinesses = staffMembersForUser
      .filter((sm) => sm.business)
      .map((sm): AvailableBusiness => {
        const { business } = sm;
        const enterpriseIds = business.enterpriseLinks.map(
          (link) => link.enterprise.id,
        );
        const giftCardsEnterpriseId = business.enterpriseLinks.find(
          (link) => link.giftCards,
        )?.enterprise.id;
        const foodAndBeverageEnterpriseId = business.enterpriseLinks.find(
          (link) => link.foodAndBev,
        )?.enterprise.id;
        const reputationManagementEnterpriseId = business.enterpriseLinks.find(
          (link) => link.reputationManagement,
        )?.enterprise.id;
        return {
          id: business.id,
          name: business.name,
          enterpriseIds,
          giftCardsEnterpriseId,
          foodAndBeverageEnterpriseId,
          reputationManagementEnterpriseId,
          staffId: sm.id,
        };
      });
    const availableEnterprises = staffMembersForUser
      .filter((sm) => sm.enterprise)
      .map((sm): AvailableEnterprise => {
        return {
          id: sm.enterprise.id,
          name: sm.enterprise.name,
          staffId: sm.id,
        };
      });

    return {
      status: HttpStatus.OK,
      error: undefined,
      data: {
        user: toUser(user),
        availableBusinesses,
        availableEnterprises,
        isSuperAdmin: staffMembersForUser.some(
          (sm) => sm.enterprise?.superAdminEnterprise,
        ),
      },
    };
  }

  public async getUser({ userId }: GetUserRequestDto): Promise<UserResponse> {
    const user: User = await this.repository.findOne({
      where: {
        id: userId,
      },
      relations: {
        addresses: true,
      },
    });

    if (!user) throw new UserNotFoundException(userId);

    return {
      status: HttpStatus.OK,
      error: null,
      data: toUser(user),
    };
  }

  public async updateUser(
    payload: UpdateUserRequestDto,
  ): Promise<UserResponse> {
    const user = await this.repository.findOne({
      where: {
        id: payload.userId,
      },
    });
    if (!user) throw new UserNotFoundException(payload.userId);

    user.dateOfBirth = payload.dateOfBirth
      ? dayjs(payload.dateOfBirth).toISOString()
      : user.dateOfBirth;
    user.firstName = payload.firstName ?? user.firstName;
    user.lastName = payload.lastName ?? user.lastName;

    if (payload.phone) {
      const existingUser = await this.repository.findOne({
        where: {
          phoneNumber: payload.phone,
        },
      });
      if (existingUser && existingUser.id !== user.id) {
        throw new UserConflictException();
      }
      // Only update if the phone number is different
      if (!existingUser) {
        user.phoneNumber = payload.phone;
        user.phoneNumberVerified = false;
      }
    }

    if (payload.address) {
      const address = this.addressRepository.create({
        addressLine1: payload.address.line1,
        addressLine2: payload.address.line2,
        city: payload.address.city,
        state: payload.address.state,
        zipCode: payload.address.zip,
        country: payload.address.country,
        user,
      });
      await this.addressRepository.save(address);
    }

    await this.repository.save(user);

    return {
      status: HttpStatus.OK,
      data: toUser(user),
    };
  }

  public async updatePassword(
    payload: UpdatePasswordRequestDto,
  ): Promise<GenericResponse> {
    const user = await this.repository.findOneBy({
      id: payload.userId,
    });
    if (!user) throw new UserNotFoundException(payload.userId);
    if (!payload.token && !payload.previousPassword)
      throw new NoPasswordOrTokenException();
    if (payload.token) {
      // get the token
      const token = await this.jwtService.verify(payload.token);
      // check it applies for this user ID
      if (token.id !== user.id) throw new InvalidTokenException();
      // If token was issued before last user update, then its invalid (can't replay tokens)
      if (!token.issued || dayjs(token.issued).isBefore(user.updatedAt))
        throw new InvalidTokenException();
    }
    if (payload.previousPassword) {
      const isPasswordValid = this.jwtService.isPasswordValid(
        payload.previousPassword,
        user.passwordHash,
      );
      if (!isPasswordValid) throw new PasswordIncorrectException();
    }
    user.passwordHash = this.jwtService.encodePassword(payload.newPassword);
    await this.repository.save(user);

    return {
      status: HttpStatus.OK,
    };
  }

  async getLoginHistory(request: IdRequestDto): Promise<LoginSessionResponse> {
    const sessions = await this.loginSessionRepository.find({
      where: {
        user: { id: request.id },
      },
    });

    return {
      status: HttpStatus.OK,
      data: sessions.map((session) => {
        return {
          id: session.id,
          ipAddress: session.ipAddress,
          platform: session.platform,
          method: session.method,
          twoFactor: session.challengeCompleted,
          createdAt: session.createdAt.toISOString(),
          lastSeenAt: session.lastSeenAt?.toISOString(),
        };
      }),
    };
  }

  async existingUser(
    request: ExistingUserRequestDto,
  ): Promise<ExistingUserResponse> {
    if (request.email) {
      const user = await this.repository.findOne({
        where: { email: ILike(request.email.replace(/[%_]/g, '\\$&')) },
      });

      return {
        status: HttpStatus.OK,
        data: !!user,
      };
    }
    if (request.phoneNumber) {
      const user = await this.repository.findOne({
        where: { phoneNumber: request.phoneNumber },
      });

      return {
        status: HttpStatus.OK,
        data: !!user,
      };
    }
    return {
      status: HttpStatus.OK,
      data: false,
    };
  }

  async getUserByEmail(request: EmailRequest): Promise<UserResponse> {
    const user = await this.repository.findOne({
      where: { email: ILike(request.email.replace(/[%_]/g, '\\$&')) },
    });

    if (!user) throw new UserNotFoundException(request.email);

    return {
      status: HttpStatus.OK,
      data: toUser(user),
    };
  }

  async getActiveSessionsForUser(
    request: IdRequestDto,
  ): Promise<LoginSessionResponse> {
    const sessions = await this.loginSessionRepository.find({
      where: {
        user: {
          id: request.id,
        },
        loggedOutAt: IsNull(),
        fcmToken: Not(IsNull()),
      },
    });

    return {
      status: HttpStatus.OK,
      data: sessions.map((session) => {
        return {
          id: session.id,
          ipAddress: session.ipAddress,
          platform: session.platform,
          method: session.method,
          twoFactor: session.challengeCompleted,
          createdAt: session.createdAt.toISOString(),
          lastSeenAt: session.lastSeenAt?.toISOString(),
          fcmToken: session.fcmToken,
        };
      }),
    };
  }
}
