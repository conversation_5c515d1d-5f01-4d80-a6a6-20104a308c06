import { HttpStatus, Inject, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { authenticator, totp } from 'otplib';
import { ILike, IsNull, Repository } from 'typeorm';
import { JwtService } from './jwt.service';
import {
  ConfirmTOTPRequestDto,
  FCMTokenRequestDto,
  IdRequestDto,
  LoginRequestDto,
  PhoneLoginRequestDto,
  SignInWithEmailDTO,
  SignInWithPhoneRequestDTO,
  ValidateRequestDto,
  VerifyEmailDTO,
  VerifyPhoneNumberDTO,
} from '../dtos/auth.dto';
import { User } from '../entities/user.entity';
import {
  LoginResponse,
  ValidateResponse,
  GenericResponse,
  SignUrlResponse,
  SignUrlRequest,
  CheckUrlSignatureRequest,
} from '../proto/auth.pb';
import { TWILIO_CONFIG_TOKEN, TwilioClient } from '@tangopay/shared/twilio';
import { SendGridService } from '@tangopay/shared/sendgrid';
import utc from 'dayjs/plugin/utc';
import tz from 'dayjs/plugin/timezone';
import dayjs from 'dayjs';
dayjs.extend(utc);
dayjs.extend(tz);
import {
  CodeValidationException,
  InsufficientPermissionsException,
  InvalidTokenException,
  PasswordIncorrectException,
  UserNotFoundException,
} from '@tangopay/shared/errors';
import { ConfigService } from '@nestjs/config';
import { LoginSession } from '../entities/session.entity';
import { getTotpNumber } from '../utils/totp';
import { toChallenge, toToken } from '../utils/responses';
import { randomUUID } from 'crypto';
import { PreAuthService } from './preauth.service';

totp.options = { digits: 6, step: 60, window: 10 };

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  @InjectRepository(User)
  private readonly repository: Repository<User>;

  @InjectRepository(LoginSession)
  private readonly loginSessionRepository: Repository<LoginSession>;

  @Inject(JwtService)
  private readonly jwtService: JwtService;

  @Inject(PreAuthService)
  private readonly preAuthService: PreAuthService;

  constructor(
    private configService: ConfigService,
    @Inject(TWILIO_CONFIG_TOKEN)
    private readonly twilioClient: TwilioClient,
    private readonly sendGrid: SendGridService,
  ) {}

  public async verifyPhoneNumber({
    phoneNumber,
    code,
    platform,
    ip,
  }: VerifyPhoneNumberDTO): Promise<LoginResponse> {
    const user: User = await this.repository.findOne({
      where: { phoneNumber: phoneNumber },
    });

    if (!user) throw new UserNotFoundException(phoneNumber);

    if (!user.totpSecret) throw new CodeValidationException();

    const isValid = totp.verify({ token: code, secret: user.totpSecret });

    if (!isValid) throw new CodeValidationException();

    if (!user.phoneNumberVerified)
      this.repository.update(user.id, { phoneNumberVerified: true });

    const refreshToken = this.jwtService.generateRefreshToken(user);

    const loginSession = new LoginSession();
    loginSession.user = user;
    loginSession.ipAddress = ip || 'unknown';
    loginSession.platform = platform || 'unknown';
    loginSession.method = 'phone';
    loginSession.refreshToken = refreshToken;
    await this.loginSessionRepository.save(loginSession);

    const accessToken = this.jwtService.generateAccessToken(user, loginSession);

    return {
      status: HttpStatus.OK,
      error: null,
      data: toToken(accessToken, refreshToken, user.id),
    };
  }

  public async logInPhoneNumber(
    payload: SignInWithPhoneRequestDTO,
  ): Promise<GenericResponse> {
    const user: User = await this.repository.findOne({
      where: { phoneNumber: payload.phoneNumber },
      relations: {
        staffMembers: {
          payRates: {
            role: true,
          },
        },
      },
    });

    if (!user) throw new UserNotFoundException(payload.phoneNumber);

    const isAdmin = (user.staffMembers || []).some((sm) =>
      (sm.payRates || []).some((rate) =>
        rate?.role?.permissions?.includes('backend:admin'),
      ),
    );

    if (payload.platform === 'admin' && !isAdmin && !user.publicOnboarding)
      throw new InsufficientPermissionsException();

    if (!user.totpSecret) {
      const otpSecret = authenticator.generateSecret();
      user.totpSecret = otpSecret;
      await this.repository.save(user);
    }

    const token = totp.generate(user.totpSecret);
    await this.twilioClient.messages.create({
      body: `Your verification code is ${token}`,
      from: '+13345649140',
      to: payload.phoneNumber,
    });
    return {
      status: HttpStatus.OK,
      error: null,
    };
  }

  public async login(
    request: LoginRequestDto | PhoneLoginRequestDto,
  ): Promise<LoginResponse> {
    const { email, password, ip, platform } = request as LoginRequestDto;
    const { phoneNumber } = request as PhoneLoginRequestDto;
    const user: User = await this.repository.findOne({
      where: [
        { email: ILike(email ?? randomUUID()) },
        { phoneNumber: phoneNumber ?? randomUUID() },
      ],
      relations: {
        staffMembers: {
          enterprise: true,
          payRates: {
            role: true,
          },
        },
      },
    });

    if (!user) throw new UserNotFoundException(email);

    const isPasswordValid: boolean = this.jwtService.isPasswordValid(
      password,
      user.passwordHash,
    );

    if (!isPasswordValid) throw new PasswordIncorrectException();

    const refreshToken = this.jwtService.generateRefreshToken(user);

    const loginSession = new LoginSession();
    loginSession.user = user;
    loginSession.ipAddress = ip || 'unknown';
    loginSession.platform = platform || 'unknown';
    loginSession.method = 'password';
    loginSession.refreshToken = refreshToken;
    loginSession.challengeRequested = user.phoneNumberVerified;
    loginSession.fcmToken = 'fcmToken' in request ? request.fcmToken : null;
    await this.loginSessionRepository.save(loginSession);
    // Begin a TOTP challenge
    if (user.phoneNumberVerified && platform === 'admin') {
      const totpNumber = getTotpNumber(user, this.configService);
      await this.twilioClient.messages.create({
        body: `Your TangoHQ verification code is ${totp.generate(
          user.twoFactorAuthTotpSecret,
        )}`,
        from: '+16468466071',
        to: totpNumber,
      });
      return {
        status: HttpStatus.OK,
        error: null,
        data: toChallenge(loginSession.id, totpNumber.slice(-4), user.id),
      };
    }

    const isSuperAdmin = (user.staffMembers || []).some(
      (staff) => staff.enterprise?.superAdminEnterprise,
    );
    console.log(user.staffMembers);
    const isAdmin = (user.staffMembers || []).some(
      (sm) =>
        (sm.payRates || []).some((rate) =>
          rate?.role?.permissions?.includes('backend:admin'),
        ) || (sm.enterprise?.flags ?? []).includes('nexus'),
    );

    if (
      platform === 'admin' &&
      !isAdmin &&
      !isSuperAdmin &&
      !user.publicOnboarding
    )
      throw new InsufficientPermissionsException();

    const accessToken = this.jwtService.generateAccessToken(
      user,
      loginSession,
      isSuperAdmin,
    );

    return {
      status: HttpStatus.OK,
      error: null,
      data: toToken(accessToken, refreshToken, user.id),
    };
  }

  public async verifyEmail({
    email,
    code,
    platform,
    ip,
  }: VerifyEmailDTO): Promise<LoginResponse> {
    const user: User = await this.repository.findOne({
      where: { email: ILike(email) },
    });

    if (!user) throw new UserNotFoundException(email);

    if (!user.totpSecret) throw new CodeValidationException();

    const isValid = totp.verify({ token: code, secret: user.totpSecret });
    if (!isValid) throw new CodeValidationException();

    if (!user.emailVerified)
      this.repository.update(user.id, { emailVerified: true });

    const refreshToken = this.jwtService.generateRefreshToken(user);

    const loginSession = new LoginSession();
    loginSession.user = user;
    loginSession.ipAddress = ip || 'unknown';
    loginSession.platform = platform || 'unknown';
    loginSession.method = 'email';
    loginSession.refreshToken = refreshToken;
    await this.loginSessionRepository.save(loginSession);

    const accessToken = this.jwtService.generateAccessToken(user, loginSession);

    return {
      status: HttpStatus.OK,
      error: null,
      data: toToken(accessToken, refreshToken, user.id),
    };
  }

  public async logInEmail(
    payload: SignInWithEmailDTO,
  ): Promise<GenericResponse> {
    const user: User = await this.repository.findOne({
      where: { email: ILike(payload.email) },
      relations: {
        staffMembers: {
          payRates: {
            role: true,
          },
        },
      },
    });

    if (!user) throw new UserNotFoundException(payload.email);

    if (!user.totpSecret) {
      const otpSecret = authenticator.generateSecret();
      user.totpSecret = otpSecret;
      await this.repository.save(user);
    }

    const isAdmin = (user.staffMembers || []).some((sm) =>
      (sm.payRates || []).some((rate) =>
        rate?.role?.permissions?.includes('backend:admin'),
      ),
    );

    if (payload.platform === 'admin' && !isAdmin && !user.publicOnboarding)
      throw new InsufficientPermissionsException();

    const token = totp.generate(user.totpSecret);
    await this.sendGrid.send({
      to: payload.email,
      templateId: 'd-350a69bc746f4a32a9b0995b9b9dee7c',
      dynamicTemplateData: {
        code: token,
        firstName: user.firstName,
      },
      from: {
        email: '<EMAIL>',
        name: 'Tango HQ',
      },
      subject: 'Tango verification code',
    });
    return {
      status: HttpStatus.OK,
      error: null,
    };
  }

  public async sendPasswordResetLink(
    payload: SignInWithEmailDTO,
  ): Promise<GenericResponse> {
    const user = await this.repository.findOne({
      where: {
        email: payload.email,
      },
    });
    if (!user) throw new UserNotFoundException(payload.email);
    const token = await this.jwtService.generateToken(user, undefined, '24h');
    await this.sendGrid.send({
      to: user.email,
      templateId: 'd-9d635aa194a34a178ca839aa7392067d',
      dynamicTemplateData: {
        fullName: `${user.firstName} ${user.lastName}`,
        resetUrl: this.composeResetUrl(token),
      },
      from: {
        email: '<EMAIL>',
        name: 'Tango HQ',
      },
      subject: `Reset your Tango password`,
    });

    return {
      status: HttpStatus.OK,
    };
  }

  composeResetUrl(token: string) {
    return `${
      this.configService.get<string>('ADMIN_DOMAIN') ??
      'https://admin.tangohq.com'
    }/reset-password/${token}`;
  }

  async confirmTOTP(request: ConfirmTOTPRequestDto): Promise<LoginResponse> {
    const session = await this.loginSessionRepository.findOne({
      where: { id: request.challengeId },
      relations: {
        user: true,
      },
    });

    const totpCorrect = totp.check(
      request.code,
      session.user.twoFactorAuthTotpSecret,
    );

    if (!totpCorrect) throw new CodeValidationException();

    const accessToken = this.jwtService.generateAccessToken(
      session.user,
      session,
      true,
    );

    await this.loginSessionRepository.update(session.id, {
      challengeCompleted: true,
    });

    return {
      status: HttpStatus.OK,
      error: null,
      data: toToken(accessToken, session.refreshToken, session.user.id),
    };
  }

  public async validate({
    token,
  }: ValidateRequestDto): Promise<ValidateResponse> {
    const decoded = await this.jwtService.verify(token);
    if (!decoded) throw new InvalidTokenException();

    if (decoded.preauthorisedSessionId)
      return this.preAuthService.validatePreAuthorisedSession(
        decoded.preauthorisedSessionId,
      );

    const auth: User = await this.jwtService.validateUser(decoded);

    if (!auth) throw new UserNotFoundException('for token');

    // TODO: Activate this if we're confident that login session will be fast to load
    // if (decoded.loginSession) {
    //   const loginSession = await this.loginSessionRepository.findOne({
    //     where: {
    //       id: decoded.loginSession,
    //       loggedOutAt: IsNull(),
    //     },
    //   });

    //   if (!loginSession) throw new InvalidTokenException();
    // }

    // Record that the session was seen
    if (decoded.loginSession)
      this.loginSessionRepository.update(decoded.id, {
        lastSeenAt: new Date(),
      });

    const allBusinessIds = new Set<string>();
    const allEnterpriseIds = new Set<string>();
    const allStaffIds = new Set<string>();
    auth.staffMembers?.forEach((sm) => {
      allStaffIds.add(sm.id);
      if (sm.business) allBusinessIds.add(sm.business.id);
      if (sm.enterprise) allEnterpriseIds.add(sm.enterprise.id);
    });

    // ids that the user has access to in a specific context
    const allFNBBusinessIds = new Set<string>();
    const allFNBEnterpriseIds = new Set<string>();
    const allGCBusinessIds = new Set<string>();
    const allGCEnterpriseIds = new Set<string>();
    const allRMBusinessIds = new Set<string>();
    const allRMEnterpriseIds = new Set<string>();

    auth.staffMembers?.forEach((sm) => {
      if (sm.business) {
        sm.business.enterpriseLinks?.forEach((link) => {
          const enterprise = link.enterprise;
          if (!enterprise) return;
          if (allEnterpriseIds.has(enterprise.id)) return;

          if (link.foodAndBev) allFNBEnterpriseIds.add(enterprise.id);
          if (link.reputationManagement) allRMEnterpriseIds.add(enterprise.id);
          if (link.giftCards) allGCEnterpriseIds.add(enterprise.id);
        });
      }

      if (sm.enterprise) {
        sm.enterprise.businessLinks?.forEach((link) => {
          const business = link.business;
          if (!business) return;
          if (allBusinessIds.has(business.id)) return;

          if (link.foodAndBev) allFNBBusinessIds.add(business.id);
          if (link.reputationManagement) allRMBusinessIds.add(business.id);
          if (link.giftCards) allGCBusinessIds.add(business.id);
        });
      }
    });

    return {
      status: HttpStatus.OK,
      error: null,
      data: {
        userId: decoded.id,
        businessIds: [...allBusinessIds],
        enterpriseIds: [...allEnterpriseIds],
        staffIds: [...allStaffIds],
        foodAndBevBusinessIds: [...allFNBBusinessIds],
        foodAndBevEnterpriseIds: [...allFNBEnterpriseIds],
        reputationBusinessIds: [...allRMBusinessIds],
        reputationEnterpriseIds: [...allRMEnterpriseIds],
        giftCardBusinessIds: [...allGCBusinessIds],
        giftCardEnterpriseIds: [...allGCEnterpriseIds],
        routes: [],
        isSuperUser: auth.staffMembers.some(
          (sm) => sm.enterprise?.superAdminEnterprise,
        ),
        parameters: [],
        sessionId: decoded.loginSession,
      },
    };
  }

  public async signUrl(payload: SignUrlRequest): Promise<SignUrlResponse> {
    const { token } = await this.jwtService.signUrl(payload.url);

    return {
      status: HttpStatus.OK,
      error: null,
      data: token,
    };
  }

  public async verifyUrlSignature(
    payload: CheckUrlSignatureRequest,
  ): Promise<GenericResponse> {
    const { url, salt } = await this.jwtService.verifyUrlSignature(
      payload.signature,
    );
    if (url !== payload.url) throw new InvalidTokenException();

    return {
      status: HttpStatus.OK,
      error: null,
    };
  }

  public async attachFCMToken(
    request: FCMTokenRequestDto,
  ): Promise<GenericResponse> {
    const loginSession = await this.loginSessionRepository.findOne({
      where: { id: request.sessionId },
    });

    if (!loginSession) throw new InvalidTokenException();

    await this.loginSessionRepository.update(loginSession.id, {
      fcmToken: request.fcmToken,
    });

    return {
      status: HttpStatus.OK,
      error: null,
    };
  }

  public async logout(request: IdRequestDto): Promise<GenericResponse> {
    const loginSession = await this.loginSessionRepository.findOne({
      where: { id: request.id },
    });

    if (!loginSession) throw new InvalidTokenException();

    await this.loginSessionRepository.update(loginSession.id, {
      loggedOutAt: new Date(),
    });

    return {
      status: HttpStatus.OK,
      error: null,
    };
  }
}
