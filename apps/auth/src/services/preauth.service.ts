import { HttpStatus, Inject, Injectable } from '@nestjs/common';
import { PreAuthorisedSession } from '../entities/preauthed-session.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreatePreAuthorisedSessionRequestDto } from '../dtos/preauth.dto';
import {
  PreAuthorisedSessionResponse,
  ValidateResponse,
} from '../proto/auth.pb';
import { BusinessClient } from '@tangopay/shared/clients/businessClient.client';
import {
  AtLeastOneBusinessOrEnterpriseRequiredException,
  BusinessNotFoundException,
  EnterpriseNotFoundException,
  PreAuthorisedSessionNotFoundException,
} from '@tangopay/shared/errors';
import dayjs from 'dayjs';
import { toPreAuthorisedSessionResponse } from '../utils/responses';
import { JwtService } from './jwt.service';
import { Logger } from '@tangopay/shared/logger/logger';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class PreAuthService {
  private readonly logger = new Logger(PreAuthService.name);

  @Inject(ConfigService)
  private readonly configService: ConfigService;

  @Inject(BusinessClient)
  private readonly businessClient: BusinessClient;

  @Inject(JwtService)
  private readonly jwtService: JwtService;

  constructor(
    @InjectRepository(PreAuthorisedSession)
    private readonly preAuthSessionRepository: Repository<PreAuthorisedSession>,
  ) {}

  public async createPreAuthorisedSession(
    payload: CreatePreAuthorisedSessionRequestDto,
  ): Promise<PreAuthorisedSessionResponse> {
    // if (payload.businessIds.length + payload.enterpriseIds.length === 0) {
    //   throw new AtLeastOneBusinessOrEnterpriseRequiredException();
    // }
    const businesses = await Promise.all(
      payload.businessIds.map(async (id) => {
        const business = await this.businessClient.getBusinessById(id);
        if (!business) throw new BusinessNotFoundException(id);
        return business;
      }),
    );

    const enterprises = await Promise.all(
      payload.enterpriseIds.map(async (id) => {
        const enterprise = await this.businessClient.getEnterpriseById(id);
        if (!enterprise) throw new EnterpriseNotFoundException(id);
        return enterprise;
      }),
    );

    const session = this.preAuthSessionRepository.create({
      ...payload,
      expiresAt: dayjs(payload.expiresAt).toDate(),
      businessIds: businesses.map((business) => business.id),
      enterpriseIds: enterprises.map((enterprise) => enterprise.id),
    });

    const savedSession = await this.preAuthSessionRepository.save(session);

    const adminUrl = `${this.configService.get('ADMIN_DOMAIN')}/preauth/${
      savedSession.id
    }`;

    return toPreAuthorisedSessionResponse(
      savedSession,
      this.jwtService.generatePreAuthorisedSessionToken(savedSession),
      adminUrl,
    );
  }

  public async getPreAuthorisedSession(
    sessionId: string,
  ): Promise<PreAuthorisedSessionResponse> {
    const session = await this.preAuthSessionRepository.findOne({
      where: { id: sessionId },
    });

    if (!session) throw new PreAuthorisedSessionNotFoundException(sessionId);

    const adminUrl = `${this.configService.get('ADMIN_DOMAIN')}/preauth/${
      session.id
    }`;

    return toPreAuthorisedSessionResponse(
      session,
      this.jwtService.generatePreAuthorisedSessionToken(session),
      adminUrl,
    );
  }

  public async validatePreAuthorisedSession(
    sessionId: string,
  ): Promise<ValidateResponse> {
    const session = await this.preAuthSessionRepository.findOne({
      where: { id: sessionId },
    });
    if (!session) {
      this.logger.defcon1(
        `A pre-authorised token was presented with a non-existent session token -- JWT secret compromised, cycle now!`,
      );
      throw new PreAuthorisedSessionNotFoundException(sessionId);
    }

    return {
      status: HttpStatus.OK,
      error: null,
      data: {
        businessIds: session.businessIds,
        enterpriseIds: session.enterpriseIds,
        sessionId: session.id,
        staffIds: [],
        foodAndBevBusinessIds: [...session.businessIds],
        foodAndBevEnterpriseIds: [...session.enterpriseIds],
        reputationBusinessIds: [...session.businessIds],
        reputationEnterpriseIds: [...session.enterpriseIds],
        giftCardBusinessIds: [...session.businessIds],
        giftCardEnterpriseIds: [...session.enterpriseIds],
        routes: session.routes,
        isSuperUser: false,
        parameters: session.parameters,
      },
    };
  }
}
