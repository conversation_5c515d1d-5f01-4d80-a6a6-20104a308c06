import { INestMicroservice, ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { AuthModule } from './auth.module';
import { HttpExceptionFilter } from '@tangopay/shared/filters/http-exception.filter';
import { protobufPackage } from './proto/auth.pb';
import { RpcExceptionFilter } from '@tangopay/shared/filters/rpc-exception.filter';
import { protobufPackage as healthProtobufPackage } from '@tangopay/shared/health/proto/health.pb';
import * as Sentry from '@sentry/node';

async function bootstrap() {
  const app: INestMicroservice = await NestFactory.createMicroservice<MicroserviceOptions>(AuthModule, {
    transport: Transport.GRPC,
    options: {
      url: `${process.env.MICROSERVICE_TRANSPORTER_URL_HOST ?? '0.0.0.0'}:50051`,
      package: [protobufPackage, healthProtobufPackage],
      protoPath: ['tango-proto/proto/auth.proto', 'tango-proto/proto/health.proto'],
      loader: {
        defaults: true,
      },
    },
  });
  Sentry.init({
    //dsn - get this value from your project in sentry
    dsn: process.env.SENTRY_DSN,
    // Configures the sample rate for error events, in the range of 0.0 to 1.0.
    //The default is 1.0 which means that 100% of error events are sent. If set to 0.1 only 10% of error events will be sent.
    sampleRate: 1,
    //Specify whether this SDK should send events to Sentry.
    enabled: true,
    //Specify environment you're working in (ie staging, production, local, etc)
    environment: process.env.CLUSTER_ENVIRONMENT,
  });

  app.useGlobalFilters(new RpcExceptionFilter(), new HttpExceptionFilter());
  app.useGlobalPipes(new ValidationPipe({ whitelist: true, transform: true }));

  await app.listen();
}

bootstrap();
