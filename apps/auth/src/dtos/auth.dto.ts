import { FormatPhoneNumberWithE164 } from '@tangopay/shared';
import {
  IsBoolean,
  IsEmail,
  IsHexadecimal,
  IsISO8601,
  IsIn,
  IsNotEmpty,
  IsOptional,
  IsPhoneNumber,
  IsString,
  IsUUID,
  Length,
  Matches,
  MinLength,
  ValidateNested,
} from 'class-validator';
import {
  LoginRequest,
  CreateUserRequest,
  ValidateRequest,
  GetUserRequest,
  SignInWithPhoneRequest,
  VerifyPhoneRequest,
  GetCurrentUserProfileRequest,
  SignInWithEmailRequest,
  VerifyEmailRequest,
  GetAllUsersRequest,
  UpdateUserRequest,
  UpdatePasswordRequest,
  PhoneLoginRequest,
  IdRequest,
  ConfirmTOTPRequest,
  ChangeEmailRequest,
  ChangePhoneNumberRequest,
  UserAddress,
  ExistingUserRequest,
  FCMTokenRequest,
} from '../proto/auth.pb';
import { Type } from 'class-transformer';
import { Platform, PLATFORMS } from '../entities/session.entity';

export class PhoneLoginRequestDto implements PhoneLoginRequest {
  @IsNotEmpty()
  @IsString()
  @IsPhoneNumber('US')
  @FormatPhoneNumberWithE164()
  public readonly phoneNumber: string;

  @IsNotEmpty()
  @IsString()
  public readonly password: string;

  @IsOptional()
  @IsIn(['admin', 'swift', 'qsrSpeed', 'fsrSpeed', 'manager', 'unknown'])
  public readonly platform?:
    | 'admin'
    | 'swift'
    | 'qsrSpeed'
    | 'fsrSpeed'
    | 'manager'
    | 'unknown';

  @IsOptional()
  @IsString()
  public readonly ip?: string;
}

export class LoginRequestDto implements LoginRequest {
  @IsEmail()
  public readonly email: string;

  @IsString()
  public readonly password: string;

  @IsOptional()
  @IsIn(PLATFORMS)
  public readonly platform?: Platform;

  @IsOptional()
  @IsString()
  public readonly ip?: string;

  @IsOptional()
  @IsString()
  public readonly fcmToken?: string;
}

export class CreateUserRequestDto implements CreateUserRequest {
  @IsEmail()
  public readonly email: string;

  @IsString()
  @MinLength(32)
  @IsHexadecimal()
  public readonly password: string;

  @IsOptional()
  @IsString()
  @IsPhoneNumber('US')
  @FormatPhoneNumberWithE164()
  public readonly phoneNumber?: string;

  @IsNotEmpty()
  @IsString()
  firstName: string;

  @IsNotEmpty()
  @IsString()
  lastName: string;

  @IsOptional()
  @IsBoolean()
  public readonly publicOnboarding?: boolean;
}

export class SignInWithPhoneRequestDTO implements SignInWithPhoneRequest {
  @IsNotEmpty()
  @IsString()
  @IsPhoneNumber('US')
  @FormatPhoneNumberWithE164()
  public readonly phoneNumber: string;

  @IsOptional()
  @IsIn(['admin', 'swift', 'qsrSpeed', 'fsrSpeed', 'manager', 'unknown'])
  public readonly platform?:
    | 'admin'
    | 'swift'
    | 'qsrSpeed'
    | 'fsrSpeed'
    | 'manager'
    | 'unknown';
}

export class VerifyPhoneNumberDTO implements VerifyPhoneRequest {
  @IsNotEmpty()
  @IsString()
  @IsPhoneNumber('US')
  @FormatPhoneNumberWithE164()
  public readonly phoneNumber: string;

  @IsNotEmpty()
  @IsString()
  @Length(6)
  public readonly code: string;

  @IsOptional()
  @IsIn(['admin', 'swift', 'qsrSpeed', 'fsrSpeed', 'manager', 'unknown'])
  public readonly platform?:
    | 'admin'
    | 'swift'
    | 'qsrSpeed'
    | 'fsrSpeed'
    | 'manager'
    | 'unknown';

  @IsOptional()
  @IsString()
  public readonly ip?: string;
}

export class SignInWithEmailDTO implements SignInWithEmailRequest {
  @IsEmail()
  public readonly email: string;

  @IsOptional()
  @IsIn(['admin', 'swift', 'qsrSpeed', 'fsrSpeed', 'manager', 'unknown'])
  public readonly platform?:
    | 'admin'
    | 'swift'
    | 'qsrSpeed'
    | 'fsrSpeed'
    | 'manager'
    | 'unknown';
}

export class VerifyEmailDTO implements VerifyEmailRequest {
  @IsEmail()
  public readonly email: string;

  @IsNotEmpty()
  @IsString()
  @Length(6)
  public readonly code: string;

  @IsOptional()
  @IsIn(['admin', 'swift', 'qsrSpeed', 'fsrSpeed', 'manager', 'unknown'])
  public readonly platform?:
    | 'admin'
    | 'swift'
    | 'qsrSpeed'
    | 'fsrSpeed'
    | 'manager'
    | 'unknown';

  @IsOptional()
  @IsString()
  public readonly ip?: string;
}

export class GetAllUsersRequestDto implements GetAllUsersRequest {
  @IsUUID()
  public readonly businessId: string;
}

export class GetUserRequestDto implements GetUserRequest {
  @IsUUID()
  public readonly userId: string;
}

export class GetCurrentUserProfileDto implements GetCurrentUserProfileRequest {
  @IsString()
  public readonly userId: string;
}

export class ValidateRequestDto implements ValidateRequest {
  @IsString()
  public readonly token: string;
}

export class UserAddressDto implements UserAddress {
  @IsString()
  line1: string;
  @IsOptional()
  @IsString()
  line2?: string;
  @IsString()
  city: string;
  @IsString()
  zip: string;
  @IsString()
  state: string;
  @IsIn(['US', 'CA'])
  country: string;
}

export class UpdateUserRequestDto implements UpdateUserRequest {
  @IsUUID()
  userId: string;

  @IsString()
  @IsOptional()
  firstName?: string;

  @IsString()
  @IsOptional()
  lastName?: string;

  @IsString()
  @IsOptional()
  email?: string;

  @IsString()
  @IsPhoneNumber('US')
  @FormatPhoneNumberWithE164()
  @IsOptional()
  phone?: string;

  @IsOptional()
  @IsISO8601()
  dateOfBirth?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => UserAddressDto)
  address?: UserAddressDto;
}

export class UpdatePasswordRequestDto implements UpdatePasswordRequest {
  @IsUUID()
  userId: string;

  @IsString()
  @MinLength(32)
  @IsHexadecimal()
  newPassword: string;

  @IsOptional()
  @IsString()
  previousPassword?: string;

  @IsOptional()
  @IsString()
  token?: string;
}

export class IdRequestDto implements IdRequest {
  @IsUUID()
  id: string;
}

export class ConfirmTOTPRequestDto implements ConfirmTOTPRequest {
  @IsUUID()
  challengeId: string;
  @Matches(/^[0-9]{6}$/)
  code: string;
}

export class ChangeEmailRequestDto implements ChangeEmailRequest {
  @IsUUID()
  userId: string;

  @IsEmail()
  newEmail: string;
}

export class ChangePhoneNumberRequestDto implements ChangePhoneNumberRequest {
  @IsUUID()
  userId: string;

  @IsString()
  @IsPhoneNumber('US')
  @FormatPhoneNumberWithE164()
  newPhoneNumber: string;
}

export class ExistingUserRequestDto implements ExistingUserRequest {
  @IsOptional()
  @IsEmail()
  email?: string;

  @IsOptional()
  @IsString()
  @IsPhoneNumber('US')
  @FormatPhoneNumberWithE164()
  phoneNumber?: string;
}

export class FCMTokenRequestDto implements FCMTokenRequest {
  @IsUUID()
  sessionId: string;
  @IsString()
  fcmToken: string;
}
