import {
  BaseEntity,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  Relation,
  UpdateDateColumn,
} from 'typeorm';
import { User } from './user.entity';

export const PLATFORMS = [
  'admin',
  'swift',
  'qsrSpeed',
  'fsrSpeed',
  'manager',
  'unknown',
  'onboarding',
  'nexus',
] as const;

export type Platform = (typeof PLATFORMS)[number];

@Entity('LoginSessions')
export class LoginSession extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  public id: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id', referencedColumnName: 'id' })
  public user: Relation<User>;

  @Column({ type: 'varchar', name: 'refresh_token' })
  public refreshToken: string;

  @Column({ type: 'varchar', name: 'ip_address' })
  public ipAddress: string;

  @Column({ type: 'varchar', name: 'method' })
  public method: 'phone' | 'email' | 'password';

  @Column({ type: 'varchar', name: 'platform' })
  public platform: Platform;

  @Column({ type: 'varchar', name: 'fcm_token', nullable: true })
  public fcmToken: string | null;

  @Column({ type: 'bool', name: 'challenge_requested', default: () => 'false' })
  public challengeRequested: boolean;

  @Column({ type: 'bool', name: 'challenge_completed', default: () => 'false' })
  public challengeCompleted: boolean;

  @CreateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
    name: 'created_at',
  })
  public createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
    onUpdate: 'CURRENT_TIMESTAMP(6)',
    name: 'updated_at',
  })
  public updatedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
    name: 'last_seen_at',
    default: null,
  })
  public lastSeenAt?: Date | null;

  @Column({ type: 'timestamp', nullable: true, name: 'logged_out_at' })
  public loggedOutAt?: Date | null;

  @DeleteDateColumn({ type: 'timestamp', nullable: true, name: 'deleted_at' })
  public deletedAt?: Date | null;
}
