import { Controller, Inject, UseFilters } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import {
  LoginRequestDto,
  CreateUserRequestDto,
  ValidateRequestDto,
  GetUserRequestDto,
  SignInWithPhoneRequestDTO,
  VerifyPhoneNumberDTO,
  GetCurrentUserProfileDto,
  SignInWithEmailDTO,
  VerifyEmailDTO,
  UpdateUserRequestDto,
  UpdatePasswordRequestDto,
  PhoneLoginRequestDto,
  IdRequestDto,
  ConfirmTOTPRequestDto,
  ChangePhoneNumberRequestDto,
  ChangeEmailRequestDto,
  ExistingUserRequestDto,
  FCMTokenRequestDto,
} from './dtos/auth.dto';
import {
  AUTH_SERVICE_NAME,
  CheckUrlSignatureRequest,
  EmailRequest,
  GenericResponse,
  GetCurrentUserProfileResponse,
  LoginResponse,
  LoginSessionResponse,
  PreAuthorisedSessionResponse,
  SignUrlRequest,
  SignUrlResponse,
  UserResponse,
  UsersResponse,
  ValidateResponse,
  VerificationTokenRequest,
} from './proto/auth.pb';
import { AuthService } from './services/auth.service';
import { LegacyRpcExceptionFilter } from '@tangopay/shared/filters/legacy-rpc-exception.filter';
import { OldHttpExceptionFilter } from '@tangopay/shared/filters/old-http-exception.filter';
import { AuthVerifyService } from './services/verify.service';
import { AuthUserService } from './services/user.service';
import { PreAuthService } from './services/preauth.service';
import { CreatePreAuthorisedSessionRequestDto } from './dtos/preauth.dto';

@Controller('auth')
export class AuthController {
  @Inject(AuthService)
  private readonly service: AuthService;

  @Inject(PreAuthService)
  private readonly preAuthService: PreAuthService;

  @Inject(AuthUserService)
  private readonly userService: AuthUserService;

  @Inject(AuthVerifyService)
  private readonly verifyService: AuthVerifyService;

  @GrpcMethod(AUTH_SERVICE_NAME, 'ExistingUser')
  private existingUser(
    payload: ExistingUserRequestDto,
  ): Promise<GenericResponse> {
    return this.userService.existingUser(payload);
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'GetAllUsers')
  private async getAllUsers(): Promise<UsersResponse> {
    return await this.userService.getAllUsers();
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'CreateUser')
  private createUser(payload: CreateUserRequestDto): Promise<UserResponse> {
    return this.userService.createUser(payload);
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'Login')
  private login(payload: LoginRequestDto): Promise<LoginResponse> {
    return this.service.login(payload);
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'PhoneLogin')
  private phoneLogin(payload: PhoneLoginRequestDto): Promise<LoginResponse> {
    return this.service.login(payload);
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'SignInWithPhone')
  private signInWithPhone(
    payload: SignInWithPhoneRequestDTO,
  ): Promise<GenericResponse> {
    return this.service.logInPhoneNumber(payload);
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'VerifyPhoneNumber')
  private verifyPhoneNumber(
    payload: VerifyPhoneNumberDTO,
  ): Promise<LoginResponse> {
    return this.service.verifyPhoneNumber(payload);
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'SignInWithEmail')
  private signInWithEmail(
    payload: SignInWithEmailDTO,
  ): Promise<GenericResponse> {
    return this.service.logInEmail(payload);
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'VerifyEmail')
  private verifyEmail(payload: VerifyEmailDTO): Promise<LoginResponse> {
    return this.service.verifyEmail(payload);
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'GetUser')
  private getUser(payload: GetUserRequestDto): Promise<UserResponse> {
    return this.userService.getUser(payload);
  }

  // This one uses the old format for errors
  @GrpcMethod(AUTH_SERVICE_NAME, 'GetCurrentUserProfile')
  @UseFilters(LegacyRpcExceptionFilter, OldHttpExceptionFilter)
  private getCurrentUserProfile(
    payload: GetCurrentUserProfileDto,
  ): Promise<GetCurrentUserProfileResponse> {
    return this.userService.getCurrentUserProfiles(payload);
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'Validate')
  private validate(payload: ValidateRequestDto): Promise<ValidateResponse> {
    return this.service.validate(payload);
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'UpdateUser')
  private updateUser(payload: UpdateUserRequestDto): Promise<UserResponse> {
    return this.userService.updateUser(payload);
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'UpdatePassword')
  private updatePassword(
    payload: UpdatePasswordRequestDto,
  ): Promise<GenericResponse> {
    return this.userService.updatePassword(payload);
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'SendResetPasswordLink')
  private sendResetPasswordLink(
    payload: SignInWithEmailDTO,
  ): Promise<GenericResponse> {
    return this.service.sendPasswordResetLink(payload);
  }

  /**
   * VERIFY EMAIL OR PHONE
   */
  @GrpcMethod(AUTH_SERVICE_NAME, 'VerifyEmailOrPhone')
  private verifyEmailOrPhone(
    payload: VerificationTokenRequest,
  ): Promise<GenericResponse> {
    return this.verifyService.verifyEmailOrPhone(payload);
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'SendEmailVerification')
  private sendEmailVerification(
    payload: IdRequestDto,
  ): Promise<GenericResponse> {
    return this.verifyService.sendEmailVerification(payload);
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'SendPhoneNumberVerification')
  private sendPhoneVerification(
    payload: IdRequestDto,
  ): Promise<GenericResponse> {
    return this.verifyService.sendPhoneVerification(payload);
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'ConfirmTOTP')
  private confirmTOTP(payload: ConfirmTOTPRequestDto): Promise<LoginResponse> {
    return this.service.confirmTOTP(payload);
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'ChangePhoneNumber')
  private changePhoneNumber(
    payload: ChangePhoneNumberRequestDto,
  ): Promise<GenericResponse> {
    return this.verifyService.changePhoneNumber(payload);
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'ChangeEmail')
  private changeEmail(
    payload: ChangeEmailRequestDto,
  ): Promise<GenericResponse> {
    return this.verifyService.changeEmail(payload);
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'GetLoginHistory')
  private getLoginHistory(payload: IdRequestDto): Promise<GenericResponse> {
    return this.userService.getLoginHistory(payload);
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'CheckUrlSignature')
  private checkUrlSignature(
    payload: CheckUrlSignatureRequest,
  ): Promise<GenericResponse> {
    return this.service.verifyUrlSignature(payload);
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'SignUrl')
  private signUrl(payload: SignUrlRequest): Promise<SignUrlResponse> {
    return this.service.signUrl(payload);
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'GetUserByEmail')
  private getUserByEmail(payload: EmailRequest): Promise<UserResponse> {
    return this.userService.getUserByEmail(payload);
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'GetPreAuthorisedSession')
  private getPreAuthorisedSession(
    payload: IdRequestDto,
  ): Promise<PreAuthorisedSessionResponse> {
    return this.preAuthService.getPreAuthorisedSession(payload.id);
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'CreatePreAuthorisedSession')
  private createPreAuthorisedSession(
    payload: CreatePreAuthorisedSessionRequestDto,
  ): Promise<PreAuthorisedSessionResponse> {
    return this.preAuthService.createPreAuthorisedSession(payload);
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'Logout')
  private logout(payload: IdRequestDto): Promise<GenericResponse> {
    return this.service.logout(payload);
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'GetActiveSessionsForUser')
  private getActiveSessionsForUser(
    payload: IdRequestDto,
  ): Promise<LoginSessionResponse> {
    return this.userService.getActiveSessionsForUser(payload);
  }

  @GrpcMethod(AUTH_SERVICE_NAME, 'AttachFCMToken')
  private attachFCMToken(
    payload: FCMTokenRequestDto,
  ): Promise<GenericResponse> {
    return this.service.attachFCMToken(payload);
  }
}
