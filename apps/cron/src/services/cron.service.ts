import { Inject, Injectable } from '@nestjs/common';
import { Cron, CronExpression, CronOptions } from '@nestjs/schedule';
import { BusinessClient } from '@tangopay/shared/clients/businessClient.client';
import { OrderingClient } from '@tangopay/shared/clients/orderingClient.client';
import { ReportingClient } from '@tangopay/shared/clients/reportingClient.client';

import utc from 'dayjs/plugin/utc';
import tz from 'dayjs/plugin/timezone';
import dayjs from 'dayjs';
import { RealtimeClient } from '@tangopay/shared/clients/realtimeClient.client';
import { Logger } from '@tangopay/shared/logger/logger';
import { GoogleBookingsClient } from '@tangopay/shared/clients/googleBookingsClient.client';
import { BookingClient } from '@tangopay/shared/clients/bookingClient.client';
import { InjectRepository } from '@nestjs/typeorm';
import { Business } from 'apps/business/src/entities/business/business.entity';
import { Repository } from 'typeorm';
import { BankingClient } from '@tangopay/shared/clients/bankingClient.client';
import { KDSClient } from '@tangopay/shared/clients/kdsClient.client';
import { AIClient } from '@tangopay/shared/clients/aiClient.client';
import { SchedulingClient } from '@tangopay/shared/clients/schedulingClient.client';
dayjs.extend(utc);
dayjs.extend(tz);

const config: CronOptions = {
  timeZone: 'America/Toronto',
};

@Injectable()
export class CronService {
  private readonly logger = new Logger(CronService.name);

  constructor(
    @Inject(AIClient)
    private aiClient: AIClient,
    @Inject(BusinessClient)
    private businessClient: BusinessClient,
    @Inject(BookingClient)
    private bookingClient: BookingClient,
    @Inject(ReportingClient)
    private reportingClient: ReportingClient,
    @Inject(OrderingClient)
    private orderingClient: OrderingClient,
    @Inject(RealtimeClient)
    private realtimeClient: RealtimeClient,
    @Inject(GoogleBookingsClient)
    private googleBookingsClient: GoogleBookingsClient,
    @Inject(BankingClient)
    private bankingClient: BankingClient,
    @Inject(KDSClient)
    private kdsClient: KDSClient,
    @Inject(SchedulingClient)
    private schedulingClient: SchedulingClient,
    @InjectRepository(Business)
    private readOnlyBusinessRepo: Repository<Business>,
  ) {}

  @Cron(CronExpression.EVERY_HOUR)
  public async takeBalanceSnapshot() {
    await this.bankingClient.takeBalanceSnapshot(this.logger);
  }

  @Cron(CronExpression.EVERY_DAY_AT_8AM)
  public async syncFeeds() {
    await this.googleBookingsClient.triggerSync(new Date().toISOString(), this.logger);
  }
  @Cron(CronExpression.EVERY_DAY_AT_6AM)
  public async cleanAndBuildStatsForBusiness() {
    const businesses = await this.readOnlyBusinessRepo.find();
    // run in series to avoid heavy load
    // 6AM is a low volume time, since most businesses are in Toronto
    for (const business of businesses) {
      try {
        const tz = business.timezone;
        const endOfADay = dayjs().tz(tz).hour(4).minute(0).second(0).millisecond(0);
        const endOfYesterday = endOfADay.isBefore(new Date()) ? endOfADay : dayjs().subtract(1, 'day').tz(tz).hour(4);
        const startOfYesterday = endOfYesterday.subtract(1, 'day').tz(tz).hour(4);
        await this.reportingClient.cleanAndBuildStatsForBusiness(business.id, startOfYesterday.toDate(), endOfYesterday.toDate(), this.logger);
      } catch (e) {
        this.logger.error(e, `Error cleaning and building stats for ${business.name}`);
      }
    }
  }

  @Cron(CronExpression.EVERY_30_SECONDS)
  public async sendStoreHealthUpdate() {
    // Get any stores that have changed in the last 30 seconds
    const updates = await this.businessClient.getStoreHealthUpdates(30);
    // send the updates to realtime
    const sendNotificationTasks = updates.map((update) => this.realtimeClient.sendStoreHealth(update));
    // await for those to be sent
    await Promise.all(sendNotificationTasks);
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  public async checkBusinessStability() {
    // Get the health status of the previous 10 and previous 5 mins
    const [previous, current] = await Promise.all([this.businessClient.getStoreHealthUpdates(600), this.businessClient.getStoreHealthUpdates(300)]);
    // extract the business IDs of those businesses we healthchecked in the past 10 mins
    const previouslyUpdatedBusinesses = previous.map((prev) => prev.businessId);
    // extract the business IDs of those businesses we healthchecked in the past 5 mins
    const currentlyUpdatedBusinesses = current.map((curr) => curr.businessId);
    // Get the difference between the businesses updated in the past 10 and the past 5
    const businessesUpdatedBeforeButNotNow = previouslyUpdatedBusinesses.filter((prev) => !currentlyUpdatedBusinesses.includes(prev));
    // If a business we previously updated, is now not being updated in this 5 minute window, something is wrong at
    // that business
    if (businessesUpdatedBeforeButNotNow.length > 0) {
      // Create a warn log
      this.logger.defcon1('Businesses that previously reported as healthy are no longer reporting as healthy', { businessIds: businessesUpdatedBeforeButNotNow.join() });
    }
  }

  // Note that this task runs serially and can take a few minutes to complete
  // 7:00 AM, before payouts. (6:00 Madison)
  // (capturing a payment takes roughly 3 seconds)
  @Cron(CronExpression.EVERY_DAY_AT_7AM, config)
  public async closeTabs(): Promise<void> {
    const yesterday5AM = dayjs().tz(config.timeZone).subtract(1, 'day').hour(5).minute(0).second(0);
    const startTime = yesterday5AM.toISOString();
    // 5am to 7am. Overlap is safe, risking a dead zone during changes is not.
    const endTime = yesterday5AM.add(1, 'day').hour(7).toISOString();
    const allBusinesses = await this.readOnlyBusinessRepo.find({
      where: {
        settings: {
          posSettings: [{ businessTipsOnScreen: false }, { usePreauth: true }],
        },
      },
    });

    for (const business of allBusinesses) {
      try {
        await this.orderingClient.closeTabsForBusiness(business.id, startTime, endTime);
      } catch (e) {
        this.logger.error(e, `Error closing tabs for ${business.name}`);
      }
    }
  }

  @Cron(CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT, config)
  public async updateAverageCookTime() {
    const businesses = await this.businessClient.getAllBusinesses();
    await Promise.all(businesses.map(async (business) => this.kdsClient.calculateAverageCookTime(business.id, this.logger)));
  }

  // runs at 7:45am to ensure that the business day (4AM) has ended
  // but before the payouts are sent
  @Cron('45 07 * * *', config)
  public async createPayouts(): Promise<void> {
    this.businessClient.triggerPayoutCalculations(undefined, undefined, this.logger);
  }

  // run frequently so that payouts arrive as soon as the funds are available
  @Cron(CronExpression.EVERY_HOUR, config)
  public async sendPayouts(): Promise<void> {
    this.businessClient.sendPayouts(undefined, this.logger);
  }

  @Cron(CronExpression.EVERY_DAY_AT_4AM, config)
  async sendDailyScheduledReports() {
    this.reportingClient.triggerDailyScheduledReports(this.logger);
  }

  @Cron(CronExpression.EVERY_DAY_AT_4AM, config)
  async sendWeeklyScheduledReports() {
    this.reportingClient.triggerWeeklyScheduledReports(this.logger);
  }
  @Cron(CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT, config)
  async sendMonthlyScheduledReports() {
    this.reportingClient.triggerMonthlyScheduledReports(this.logger);
  }

  @Cron(CronExpression.EVERY_10_MINUTES, config)
  async sendReminderAndConfirmationMessages() {
    this.bookingClient.sendReminderAndConfirmationMessages();
  }

  @Cron(CronExpression.EVERY_10_MINUTES, config)
  async sendReportingNotifications() {
    this.reportingClient.sendNotifications();
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async chargeCardTransactionReconciliation() {
    // Dumps clumps on Oatfi
    await this.bankingClient.dumpClumpsOnOatfi();
    // Resets the balance of all charge accounts for the day
    await this.bankingClient.resetAllChargeAccountBalances();
    // Begins Oatfi repayments for due charge accounts
    await this.bankingClient.beginOatfiRepayments();
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async updateSurveilledMenus() {
    await this.aiClient.updateSurveilledMenus();
  }

  @Cron(CronExpression.EVERY_DAY_AT_NOON, config)
  async dispatchEmails() {
    this.reportingClient.dispatchEmails();
  }

  // Dispatch emails every Monday at 8:00 AM
  @Cron('0 8 * * 1', { timeZone: 'America/Toronto' })
  async dispatchAugmentedSchedulingEmails() {
    await this.schedulingClient.dispatchAugmentedSchedulingEmail();
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async createNewJobsForSchedules() {
    this.reportingClient.createNewJobsForSchedules(this.logger);
  }

  @Cron(CronExpression.EVERY_WEEK)
  async refreshReviews() {
    const businesses = await this.businessClient.getAllBusinesses();
    await Promise.all(businesses.map((business) => this.reportingClient.refreshReviews(business.id, this.logger)));
  }
}
