import { Modu<PERSON> } from '@nestjs/common';
import { HealthController } from '@tangopay/shared/health/grpcHealth.controller';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { QueryController } from './controllers/query.controller';
import { QueryService } from './services/query.service';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService) => {
        return {
          type: 'postgres',
          host: config.get<string>('POSTGRES_HOST_READ'),
          port: config.get<number>('POSTGRES_PORT_READ'),
          username: config.get<string>('POSTGRES_USERNAME_READ'),
          password: config.get<string>('POSTGRES_PASSWORD_READ'),
          database: config.get<string>('POSTGRES_DATABASE_READ'),
          entities: [],
          synchronize: false,
          migrationsTransactionMode: 'each',
          useUTC: true,
        };
      },
      inject: [ConfigService],
    }),
  ],
  controllers: [HealthController, QueryController],
  providers: [QueryService],
})
export class SandboxModule {}
