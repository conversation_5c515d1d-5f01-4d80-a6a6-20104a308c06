/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";

export const protobufPackage = "realtime";

export interface PushNotificationMessage {
  title: string;
  body: string;
  fcmToken: string;
  /** 'insight' */
  type?:
    | string
    | undefined;
  /** uuid of the insight */
  id?: string | undefined;
}

export interface Error {
  type: string;
  message: string;
}

export interface GenericResponse {
  status: number;
  error?: Error | undefined;
}

export const REALTIME_PACKAGE_NAME = "realtime";

export interface RealtimeNexusServiceClient {
  sendPushNotification(request: PushNotificationMessage, metadata?: Metadata): Observable<GenericResponse>;
}

export interface RealtimeNexusServiceController {
  sendPushNotification(
    request: PushNotificationMessage,
    metadata?: Metadata,
  ): Promise<GenericResponse> | Observable<GenericResponse> | GenericResponse;
}

export function RealtimeNexusServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["sendPushNotification"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("RealtimeNexusService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("RealtimeNexusService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const REALTIME_NEXUS_SERVICE_NAME = "RealtimeNexusService";
