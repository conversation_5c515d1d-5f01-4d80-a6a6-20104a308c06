import { Module } from '@nestjs/common';
import DineInController from './controllers/dinein.controller';
import KDSController from './controllers/kds.controller';
import PrintingController from './controllers/printing.controller';
import DineInSocket from './gateways/dinein.gateway';
import KDSSocket from './gateways/kds.gateway';
import PrintingSocket from './gateways/printing.gateway';
import { DineInRealtimeService } from './services/dinein.service';
import { KDSRealtimeService } from './services/kds.service';
import { SharedModule } from '@tangopay/shared';
import OrderingController from './controllers/ordering.controller';
import OrderingSocket from './gateways/ordering.gateway';
import ReservationsController from './controllers/reservations.controller';
import ReservationsSocket from './gateways/reservations.gateway';
import MenuSocket from './gateways/menu.gateway';
import MenuController from './controllers/menu.controller';
import TrackingSocket from './gateways/counts.gateway';
import TrackingController from './controllers/tracking.controller';
import { HealthController } from '@tangopay/shared/health/grpcHealth.controller';
import SchedulingSocket from './gateways/scheduling.gateway';
import SchedulingController from './controllers/scheduling.controller';
import PreviewPrintController from './controllers/print.controller';
import PreviewPrintSocket from './gateways/print.gateway';
import StoreHealthController from './controllers/store-health.controller';
import StaffingSocket from './gateways/staffing.gateway';
import StaffingController from './controllers/staffing.controller';
import BankingSocket from './gateways/banking.gateway';
import BankingController from './controllers/banking.controller';
import BusinessJoinController from './controllers/join.controller';
import BusinessJoinSocket from './gateways/join.gateway';
import NexusController from './controllers/nexus.controller';
import { NexusNotificationService } from './services/nexus.service';
import { ConfigModule } from '@nestjs/config';

@Module({
  imports: [ConfigModule.forRoot({ isGlobal: true }), SharedModule],
  controllers: [
    PrintingController,
    DineInController,
    KDSController,
    OrderingController,
    ReservationsController,
    MenuController,
    TrackingController,
    SchedulingController,
    StaffingController,
    PreviewPrintController,
    HealthController,
    BankingController,
    BusinessJoinController,
    NexusController,
    StoreHealthController,
  ],
  providers: [
    DineInRealtimeService,
    DineInSocket,
    KDSSocket,
    PrintingSocket,
    PreviewPrintSocket,
    KDSRealtimeService,
    OrderingSocket,
    StaffingSocket,
    ReservationsSocket,
    MenuSocket,
    TrackingSocket,
    SchedulingSocket,
    BankingSocket,
    BusinessJoinSocket,
    NexusNotificationService,
  ],
})
export class RealtimeModule {}
