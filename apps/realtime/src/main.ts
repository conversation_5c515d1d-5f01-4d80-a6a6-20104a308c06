import { INestMicroservice } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { protobufPackage } from './proto/proto/realtime/dinein.pb';
import { RealtimeModule } from './realtime.module';
import { RedisIoAdapter } from './redis.adapter';
import { protobufPackage as healthProtobufPackage } from '@tangopay/shared/health/proto/health.pb';
import * as Sentry from '@sentry/node';

async function bootstrap() {
  const app: INestMicroservice = await NestFactory.createMicroservice<MicroserviceOptions>(RealtimeModule, {
    transport: Transport.GRPC,
    options: {
      url: `${process.env.MICROSERVICE_TRANSPORTER_URL_HOST ?? '0.0.0.0'}:50071`,
      package: [protobufPackage, healthProtobufPackage],
      maxSendMessageLength: 1024 * 1024 * 100,
      maxReceiveMessageLength: 1024 * 1024 * 100,
      protoPath: [
        'tango-proto/proto/realtime/dinein.proto',
        'tango-proto/proto/realtime/kds.proto',
        'tango-proto/proto/realtime/printing.proto',
        'tango-proto/proto/realtime/print.proto',
        'tango-proto/proto/realtime/ordering.proto',
        'tango-proto/proto/realtime/reservation.proto',
        'tango-proto/proto/realtime/menu.proto',
        'tango-proto/proto/realtime/tracking.proto',
        'tango-proto/proto/realtime/scheduling.proto',
        'tango-proto/proto/realtime/staffing.proto',
        'tango-proto/proto/realtime/banking.proto',
        'tango-proto/proto/realtime/store-health.proto',
        'tango-proto/proto/realtime/join.proto',
        'tango-proto/proto/realtime/nexus.proto',
        'tango-proto/proto/health.proto',
      ],
      loader: {
        defaults: true,
      },
    },
  });
  Sentry.init({
    //dsn - get this value from your project in sentry
    dsn: process.env.SENTRY_DSN,
    // Configures the sample rate for error events, in the range of 0.0 to 1.0.
    //The default is 1.0 which means that 100% of error events are sent. If set to 0.1 only 10% of error events will be sent.
    sampleRate: 1,
    //Specify whether this SDK should send events to Sentry.
    enabled: true,
    //Specify environment you're working in (ie staging, production, local, etc)
    environment: process.env.CLUSTER_ENVIRONMENT,
  });
  if (process.env.NODE_ENV === 'production') {
    const redisIoAdapter = new RedisIoAdapter(app);
    await redisIoAdapter.connectToRedis();

    app.useWebSocketAdapter(redisIoAdapter);
  }
  await app.listen();
}
bootstrap();
