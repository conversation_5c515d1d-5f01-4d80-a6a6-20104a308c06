import { Controller } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { GenericResponse, PushNotificationMessage, REALTIME_NEXUS_SERVICE_NAME } from '../proto/proto/realtime/nexus.pb';
import { NexusNotificationService } from '../services/nexus.service';

@Controller()
export default class NexusController {
  constructor(private readonly nexusService: NexusNotificationService) {}

  @GrpcMethod(REALTIME_NEXUS_SERVICE_NAME, 'SendPushNotification')
  async sendPushNotification(request: PushNotificationMessage): Promise<GenericResponse> {
    return this.nexusService.sendPushNotification(request);
  }
}
