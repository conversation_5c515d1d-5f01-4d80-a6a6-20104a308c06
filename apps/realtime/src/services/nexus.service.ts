import { HttpStatus, Inject, Injectable } from '@nestjs/common';
import { GenericResponse, PushNotificationMessage } from '../proto/proto/realtime/nexus.pb';
import { App, initializeApp } from 'firebase-admin/app';
import { getMessaging } from 'firebase-admin/messaging';
import { ConfigService } from '@nestjs/config';
import { credential } from 'firebase-admin';
import { randomUUID } from 'crypto';

// TODO: this is really just a passthrough class that doesn't await things
@Injectable()
export class NexusNotificationService {
  private readonly app: App;

  constructor(configService: ConfigService) {
    const credentials = JSON.parse(Buffer.from(`${configService.get('FIREBASE_SERVICE_CREDENTIALS')}`, 'base64').toString('utf8'));
    this.app = initializeApp({
      credential: credential.cert(credentials),
    });
  }

  async sendPushNotification(request: PushNotificationMessage): Promise<GenericResponse> {
    const result = await getMessaging(this.app).send({
      token: request.fcmToken,
      notification: {
        title: request.title,
        body: request.body,
      },
      android:
        request.type && request.id
          ? {
              data: {
                type: request.type,
                id: request.id,
              },
            }
          : undefined,
      apns:
        request.type && request.id
          ? {
              payload: {
                aps: {},
                type: request.type,
                id: request.id,
              },
            }
          : undefined,
    });
    console.log(result);
    return {
      status: HttpStatus.OK,
    };
  }
}
