import { Body, Controller, Inject, Post, UseInterceptors } from '@nestjs/common';
import { SentryInterceptor } from '../sentry/sentry.interceptor';
import { AvailableClients } from '@tangopay/shared/grpc-clients';
import { ClientGrpc } from '@nestjs/microservices';
import { PushNotificationMessage, REALTIME_NEXUS_SERVICE_NAME, RealtimeNexusServiceClient } from 'apps/realtime/src/proto/proto/realtime/nexus.pb';

@UseInterceptors(SentryInterceptor)
@Controller('v2/realtime/nexus')
export class NexusGatewayController {
  constructor(
    @Inject(AvailableClients.REALTIME_PACKAGE.packageName)
    private client: ClientGrpc,
  ) {}

  private service: RealtimeNexusServiceClient;

  onModuleInit() {
    this.service = this.client.getService<RealtimeNexusServiceClient>(REALTIME_NEXUS_SERVICE_NAME);
  }

  @Post('send-push-notification')
  async sendPushNotification(@Body() body: PushNotificationMessage) {
    return this.service.sendPushNotification(body);
  }
}
