import { Authorize } from './authorize';

export const businessIdFromParams = Authorize((req) => ({
  business: {
    businessIds: [req.params.businessId],
  },
}));
export const enterpriseIdFromParams = Authorize((req) => ({
  business: {
    enterpriseIds: [req.params.enterpriseId],
  },
}));
export const businessIdFromBody = Authorize((req) => ({
  business: {
    businessIds: [req.body.businessId],
  },
}));
export const tabIdFromParams = Authorize((req) => ({
  ordering: {
    tabIds: [req.params.tabId],
  },
}));
export const tabIdFromBody = Authorize((req) => ({
  ordering: {
    tabIds: [req.body.tabId],
  },
}));
export const paymentIdFromBody = Authorize((req) => ({
  ordering: {
    paymentIds: [req.body.paymentId],
  },
}));
export const paymentIdFromParams = Authorize((req) => ({
  ordering: {
    paymentIds: [req.params.paymentId],
  },
}));
export const orderIdFromParams = Authorize((req) => ({
  ordering: {
    orderIds: [req.params.orderId],
  },
}));

export const orderedProductIdFromParams = Authorize((req) => ({
  ordering: {
    productIds: [req.params.orderedProductId],
  },
}));

export const accountIdFromParams = Authorize((req) => ({
  banking: {
    accountIds: [req.params.accountId],
  },
}));

export const cardholderIdFromParams = Authorize((req) => ({
  banking: {
    cardholderIds: [req.params.cardholderId],
  },
}));
export const controlGroupIdFromParams = Authorize((req) => ({
  banking: {
    controlGroupIds: [req.params.controlGroupId],
  },
}));
export const cardIdFromParams = Authorize((req) => ({
  banking: {
    cardIds: [req.params.cardId],
  },
}));

export const penaltyTemplateIdFromParams = Authorize((req) => ({
  banking: {
    penaltyTemplateIds: [req.params.penaltyTemplateId],
  },
}));

export const demandorDemandIdFromParams = Authorize((req) => ({
  banking: {
    demandorDemandIds: [req.params.demandId],
  },
}));

export const demandeeDemandIdFromParams = Authorize((req) => ({
  banking: {
    demandeeDemandIds: [req.params.demandId],
  },
}));

export const linkedAccountIdFromParams = Authorize((req) => ({
  banking: {
    linkedAccountIds: [req.params.linkedAccountId],
  },
}));

export const forexPaymentIdFromParams = Authorize((req) => ({
  banking: {
    forexPaymentIds: [req.params.forexPaymentId],
  },
}));

export const transactionV2IdFromParams = Authorize((req) => ({
  banking: {
    transactionV2Ids: [req.params.transactionId],
  },
}));

export const superAdmin = Authorize((req) => ({
  user: {
    superAdmin: true,
  },
}));

export const reviewableIdFromParams = Authorize((req) => ({
  reporting: {
    reviewableIds: [req.params.reviewableId],
  },
}));

export const reviewableIdFromBody = Authorize((req) => ({
  reporting: {
    reviewableIds: [req.body.reviewableId],
  },
}));
