import { PreAuthorisationParameters } from 'apps/auth/src/proto/auth.pb';
import { BankingPermissionsRequest } from 'apps/banking/src/proto/banking/authorization.pb';
import { MenuPermissionsRequest } from 'apps/menu/src/proto/proto/menu-management.pb';
import { OrderingPermissionsRequest } from 'apps/ordering/src/proto/proto/ordering/ordering.pb';
import { ReportingPermissionsRequest } from 'apps/reporting/src/proto/reporting/authorization.pb';
import { Request } from 'express';

export type User = {
  id?: string;
  isSuperUser: boolean;
  staffMemberIds: string[];
  businessIds: string[];
  foodAndBevBusinessIds: string[];
  giftCardBusinessIds: string[];
  reputationBusinessIds: string[];
  enterpriseIds: string[];
  foodAndBevEnterpriseIds: string[];
  giftCardEnterpriseIds: string[];
  reputationEnterpriseIds: string[];
  routes: string[];
  parameters: PreAuthorisationParameters[];
};

export type UserPermissionsRequest = {
  superAdmin?: boolean;
};

export type PermissionsManifest = {
  ordering?: Partial<Omit<OrderingPermissionsRequest, 'businessIds' | 'enterpriseIds'>>;
  menu?: Partial<Omit<MenuPermissionsRequest, 'businessIds' | 'enterpriseIds'>>;
  business?: {
    businessIds?: string[];
    enterpriseIds?: string[];
  };
  user?: UserPermissionsRequest;
  banking?: Partial<Omit<BankingPermissionsRequest, 'authorizedBusinessIds' | 'authorizedEnterpriseIds' | 'authorizedStaffIds'>>;
  reporting?: Partial<Omit<ReportingPermissionsRequest, 'businessIds' | 'enterpriseIds'>>;
};
export type PermissionExtractor = (request: Request) => PermissionsManifest;
