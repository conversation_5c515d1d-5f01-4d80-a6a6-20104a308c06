import { CanActivate, ExecutionContext, HttpStatus, Inject, Logger, mixin } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import TangoException, { UnauthorizedBusinessException, UnauthorizedEnterpriseException } from '@tangopay/shared/errors';
import { AvailableClients } from '@tangopay/shared/grpc-clients';
import { ORDERING_SERVICE_NAME, OrderingPermissionsRequest, OrderingServiceClient } from 'apps/ordering/src/proto/proto/ordering/ordering.pb';
import { Request } from 'express';
import { firstValueFrom } from 'rxjs';
import { PermissionExtractor, User, UserPermissionsRequest } from './types';
import { MENU_MANAGEMENT_SERVICE_NAME, MenuManagementServiceClient, MenuPermissionsRequest } from 'apps/menu/src/proto/proto/menu-management.pb';
import { uniq } from 'lodash';
import { ConfigService } from '@nestjs/config';
import { BANKING_AUTHORIZATION_SERVICE_NAME, BankingAuthorizationServiceClient, BankingPermissionsRequest } from 'apps/banking/src/proto/banking/authorization.pb';
import {
  REPORTING_AUTHORIZATION_SERVICE_NAME,
  ReportingAuthorizationServiceClient,
  ReportingPermissionsRequest,
} from 'apps/reporting/src/proto/reporting/authorization.pb';

export const Authorize = (extractor: PermissionExtractor) => {
  class AuthorizeMixin implements CanActivate {
    logger = new Logger(AuthorizeMixin.name);
    orderingService: OrderingServiceClient;
    menuService: MenuManagementServiceClient;
    bankingService: BankingAuthorizationServiceClient;
    reportingService: ReportingAuthorizationServiceClient;

    @Inject(AvailableClients.ORDERING_PACKAGE.packageName)
    orderingClient: ClientGrpc;
    @Inject(AvailableClients.MENU_PACKAGE.packageName)
    menuClient: ClientGrpc;
    @Inject(AvailableClients.BANKING_PACKAGE.packageName)
    bankingClient: ClientGrpc;
    @Inject(AvailableClients.REPORTING_PACKAGE.packageName)
    reportingClient: ClientGrpc;
    @Inject(ConfigService)
    configService: ConfigService;

    enabled: boolean = false;
    strict: boolean = false;
    onModuleInit() {
      this.orderingService = this.orderingClient.getService<OrderingServiceClient>(ORDERING_SERVICE_NAME);
      this.menuService = this.menuClient.getService<MenuManagementServiceClient>(MENU_MANAGEMENT_SERVICE_NAME);
      this.bankingService = this.bankingClient.getService<BankingAuthorizationServiceClient>(BANKING_AUTHORIZATION_SERVICE_NAME);
      this.reportingService = this.reportingClient.getService<ReportingAuthorizationServiceClient>(REPORTING_AUTHORIZATION_SERVICE_NAME);
      const mode = this.configService.get('RBAC_MODE');
      if (mode === 'enabled') {
        this.enabled = true;
        this.strict = true;
      } else if (mode === 'warning') {
        this.enabled = true;
        this.strict = false;
      } else {
        this.enabled = false;
        this.strict = false;
      }
    }

    async canActivate(context: ExecutionContext): Promise<boolean> {
      // skip check
      if (!this.enabled) return true;
      const request = context.switchToHttp().getRequest() as Request;
      const user = request.user as User;
      if (user.isSuperUser) return true;
      if (user.routes.length > 0) {
        const route = request.route.path;
        // check if the route is in the pre-authorised routes
        if (!user.routes.some((r) => route.includes(r))) {
          throw new TangoException('Unauthorized', 'UNAUTHORIZED', HttpStatus.UNAUTHORIZED);
        }
      }
      const manifest = extractor(request);
      const promises: Promise<unknown>[] = [];
      if (manifest.user) {
        promises.push(this.checkUser(manifest.user, user));
      }
      if (manifest.ordering) {
        promises.push(
          this.checkOrdering({
            // ordering is in the food and bev domain
            businessIds: [...(user.businessIds || []), ...(user.foodAndBevBusinessIds || [])],
            enterpriseIds: [...(user.enterpriseIds || []), ...(user.foodAndBevEnterpriseIds || [])],

            tabIds: uniq(manifest.ordering.tabIds || []),
            paymentIds: uniq(manifest.ordering.paymentIds || []),
            orderIds: uniq(manifest.ordering.orderIds || []),
            productIds: uniq(manifest.ordering.productIds || []),
            preAuthIds: uniq(manifest.ordering.preAuthIds || []),
            voidedProductIds: uniq(manifest.ordering.voidedProductIds || []),
          }),
        );
      }
      if (manifest.menu) {
        promises.push(
          this.checkMenu({
            // menu management is in the food and bev domain
            businessIds: [...(user.businessIds || []), ...(user.foodAndBevBusinessIds || [])],
            enterpriseIds: [...(user.enterpriseIds || []), ...(user.foodAndBevEnterpriseIds || [])],

            publishedMenuIds: uniq(manifest.menu.publishedMenuIds || []),
            menuIds: uniq(manifest.menu.menuIds || []),
            categoryIds: uniq(manifest.menu.categoryIds || []),
            productIds: uniq(manifest.menu.productIds || []),
            modifierIds: uniq(manifest.menu.modifierIds || []),
            modifierOptionIds: uniq(manifest.menu.modifierOptionIds || []),
            discountIds: uniq(manifest.menu.discountIds || []),
            priceLevelIds: uniq(manifest.menu.priceLevelIds || []),
            priceVariantIds: uniq(manifest.menu.priceVariantIds || []),
          }),
        );
      }
      if (manifest.business) {
        promises.push(this.checkBusiness(user.businessIds || [], manifest.business.businessIds || [], user.enterpriseIds || [], manifest.business.enterpriseIds || []));
      }
      if (manifest.banking) {
        promises.push(
          this.checkBanking({
            authorizedBusinessIds: user.businessIds,
            authorizedEnterpriseIds: user.enterpriseIds,
            authorizedStaffIds: user.staffMemberIds,
            accountIds: manifest.banking.accountIds,
            beneficiaryIds: manifest.banking.beneficiaryIds,
            cardholderIds: manifest.banking.cardholderIds,
            cardIds: manifest.banking.cardIds,
            controlGroupIds: manifest.banking.controlGroupIds,
            globalAccountIds: manifest.banking.globalAccountIds,
            transactionIds: manifest.banking.transactionIds,
            penaltyTemplateIds: manifest.banking.penaltyTemplateIds,
            demandorDemandIds: manifest.banking.demandorDemandIds,
            demandeeDemandIds: manifest.banking.demandeeDemandIds,
            linkedAccountIds: manifest.banking.linkedAccountIds,
            forexPaymentIds: manifest.banking.forexPaymentIds,
            transactionV2Ids: manifest.banking.transactionV2Ids,
          }),
        );
      }
      if (manifest.reporting) {
        promises.push(
          this.checkReporting({
            businessIds: user.businessIds,
            enterpriseIds: user.enterpriseIds,
            reviewableIds: manifest.reporting.reviewableIds,
          }),
        );
      }
      try {
        await Promise.all(promises);
      } catch (e) {
        if (this.strict) {
          throw e;
        } else {
          this.logger.warn(e instanceof TangoException ? e.rawMessage : e);
        }
      }
      return true;
    }

    async checkUser(req: UserPermissionsRequest, user: User) {
      if (req.superAdmin && !user.isSuperUser) throw new TangoException('User is not a super admin', 'UNAUTHORIZED', HttpStatus.UNAUTHORIZED);
      return;
    }

    async checkOrdering(req: OrderingPermissionsRequest) {
      // no need to validate, no permissions required
      if (
        req.paymentIds.length === 0 &&
        req.tabIds.length === 0 &&
        req.orderIds.length === 0 &&
        req.productIds.length === 0 &&
        req.voidedProductIds.length === 0 &&
        req.preAuthIds.length === 0
      )
        return;
      try {
        const result = await firstValueFrom(this.orderingService.authorize(req));
        if (result.status === HttpStatus.OK) return;
        if (result.error) throw new TangoException(result.error.message, result.error.type, result.status);
      } catch (e) {
        if (e instanceof TangoException) throw e;
        // log the error, then throw below
        this.logger.error(e);
      }
      // log the error, throw if the call failed or had a non-200 status
      throw new TangoException('Failed to authorize with ordering service', 'UNAUTHORIZED', HttpStatus.FORBIDDEN);
    }

    async checkMenu(req: MenuPermissionsRequest) {
      // no need to validate, no permissions required
      if (
        req.menuIds.length === 0 &&
        req.publishedMenuIds.length === 0 &&
        req.categoryIds.length === 0 &&
        req.productIds.length === 0 &&
        req.modifierIds.length === 0 &&
        req.modifierOptionIds.length === 0 &&
        req.priceLevelIds.length === 0 &&
        req.priceVariantIds.length === 0 &&
        req.discountIds.length === 0
      )
        return;
      try {
        const result = await firstValueFrom(this.menuService.authorize(req));
        if (result.status === HttpStatus.OK) return;
        if (result.error) throw new TangoException(result.error.message, result.error.type, result.status);
      } catch (e) {
        if (e instanceof TangoException) throw e;
        // log the error, then throw below
        this.logger.error(e);
      }
      // log the error, throw if the call failed or had a non-200 status
      throw new TangoException('Failed to authorize with menu service', 'UNAUTHORIZED', HttpStatus.FORBIDDEN);
    }

    async checkBusiness(businessIdsHeld: string[], businessIdsNeeded: string[], enterpriseIdsHeld: string[], enterpriseIdsNeeded: string[]) {
      businessIdsNeeded.forEach((id) => {
        if (!businessIdsHeld.includes(id)) throw new UnauthorizedBusinessException(id);
      });
      enterpriseIdsNeeded.forEach((id) => {
        if (!enterpriseIdsHeld.includes(id)) throw new UnauthorizedEnterpriseException(id);
      });
    }

    async checkBanking(req: BankingPermissionsRequest) {
      // no need to validate, no permissions required
      if (
        req.accountIds?.length === 0 &&
        req.beneficiaryIds?.length === 0 &&
        req.cardholderIds?.length === 0 &&
        req.cardIds?.length === 0 &&
        req.controlGroupIds?.length === 0 &&
        req.globalAccountIds?.length === 0 &&
        req.transactionIds?.length === 0
      )
        return;
      try {
        const result = await firstValueFrom(this.bankingService.authorize(req));
        if (result.status === HttpStatus.OK) return;
        if (result.error) throw new TangoException(result.error.message, result.error.type, result.status);
      } catch (e) {
        if (e instanceof TangoException) throw e;
        // log the error, then throw below
        this.logger.error(e);
      }
      // log the error, throw if the call failed or had a non-200 status
      throw new TangoException('Failed to authorize with banking service', 'UNAUTHORIZED', HttpStatus.FORBIDDEN);
    }

    async checkReporting(req: ReportingPermissionsRequest) {
      // no need to validate, no permissions required
      if (req.reviewableIds?.length === 0) return;
      try {
        const result = await firstValueFrom(this.reportingService.authorize(req));
        if (result.status === HttpStatus.OK) return;
        if (result.error) throw new TangoException(result.error.message, result.error.type, result.status);
      } catch (e) {
        if (e instanceof TangoException) throw e;
        // log the error, then throw below
        this.logger.error(e);
      }
      // log the error, throw if the call failed or had a non-200 status
      throw new TangoException('Failed to authorize with reporting service', 'UNAUTHORIZED', HttpStatus.FORBIDDEN);
    }
  }
  return mixin(AuthorizeMixin);
};
