import { Body, Controller, Delete, Get, Inject, Logger, Param, Post, Put, Query, UseGuards, UseInterceptors } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { AvailableClients } from '@tangopay/shared/grpc-clients';
import {
  DailyLaborReportRequest,
  DailySalesReportRequest,
  ProductsAndPaymentsCsvReportRequest,
  REPORTING_SERVICE_NAME,
  ReportingServiceClient,
  SalesMixCsvReportRequest,
  TimeCardSummaryReportRequest,
  WeeklySalarySummaryReportRequest,
} from 'apps/reporting/src/proto/reporting/reporting.pb';
import { SentryInterceptor } from '../sentry/sentry.interceptor';
import { REPORT_DATA_SERVICE_NAME, ReportDataServiceClient } from 'apps/reporting/src/proto/reporting/data.pb';
import {
  CreateReportingNotificationRequest,
  CustomReportRequest,
  REPORT_GENERATION_SERVICE_NAME,
  ReportGenerationServiceClient,
} from 'apps/reporting/src/proto/reporting/generate.pb';
import { stringify } from 'qs';
import { REPORT_DETAILED_DATA_SERVICE_NAME, ReportDetailedDataServiceClient } from 'apps/reporting/src/proto/reporting/detailed-data.pb';
import { CreateTipOutRequest, TIP_OUT_SERVICE_NAME, TipOutServiceClient, UpdateTipOutRequest } from 'apps/reporting/src/proto/reporting/tipout.pb';
import { ANALYSIS_SERVICE_NAME, AnalysisServiceClient } from 'apps/reporting/src/proto/reporting/analysis.pb';
import { DialogInsightRequest, REPORT_INSIGHTS_SERVICE_NAME, ReportInsightsServiceClient } from 'apps/reporting/src/proto/reporting/insights.pb';
import { superAdmin } from '../middleware/basicGuards';
import { DispatchEmailsRequest, REPORT_MAILING_SERVICE_NAME, ReportMailingServiceClient } from 'apps/reporting/src/proto/reporting/mailing.pb';
import { COPILOT_SERVICE_NAME, CopilotServiceClient } from 'apps/reporting/src/proto/reporting/copilot.pb';
import { QueryCopilotRequestDto } from 'apps/reporting/src/dtos/copilot.dto';

@UseInterceptors(SentryInterceptor)
@Controller('v2/reporting')
export class ReportingGatewayController {
  constructor(
    @Inject(AvailableClients.REPORTING_PACKAGE.packageName)
    private client: ClientGrpc,
  ) {}

  private service: ReportingServiceClient;
  private mailingService: ReportMailingServiceClient;
  private analysisService: AnalysisServiceClient;
  private dataService: ReportDataServiceClient;
  private detailedDataService: ReportDetailedDataServiceClient;
  private generateService: ReportGenerationServiceClient;
  private insightsService: ReportInsightsServiceClient;
  private tipOutService: TipOutServiceClient;
  private copilotService: CopilotServiceClient;
  private readonly logger = new Logger(ReportingGatewayController.name);
  onModuleInit() {
    this.service = this.client.getService<ReportingServiceClient>(REPORTING_SERVICE_NAME);
    this.mailingService = this.client.getService<ReportMailingServiceClient>(REPORT_MAILING_SERVICE_NAME);
    this.analysisService = this.client.getService<AnalysisServiceClient>(ANALYSIS_SERVICE_NAME);
    this.dataService = this.client.getService<ReportDataServiceClient>(REPORT_DATA_SERVICE_NAME);
    this.detailedDataService = this.client.getService<ReportDetailedDataServiceClient>(REPORT_DETAILED_DATA_SERVICE_NAME);
    this.generateService = this.client.getService<ReportGenerationServiceClient>(REPORT_GENERATION_SERVICE_NAME);
    this.tipOutService = this.client.getService<TipOutServiceClient>(TIP_OUT_SERVICE_NAME);
    this.insightsService = this.client.getService<ReportInsightsServiceClient>(REPORT_INSIGHTS_SERVICE_NAME);
    this.copilotService = this.client.getService<CopilotServiceClient>(COPILOT_SERVICE_NAME);
  }

  @UseGuards(superAdmin)
  @Post('trigger-emails')
  triggerEmails(@Body() payload: DispatchEmailsRequest) {
    return this.mailingService.dispatchEmails(payload);
  }

  @Post('daily-sales-report')
  getDailySalesReport(@Body() reportRequest: DailySalesReportRequest) {
    return this.service.dailySalesReport(reportRequest);
  }

  @Post('time-card-summary-report')
  getTimeCardSummaryReport(@Body() reportRequest: TimeCardSummaryReportRequest) {
    return this.service.timeCardSummaryReport(reportRequest);
  }

  @Post('weekly-salary-summary-report')
  getWeeklySalarySummaryReport(@Body() reportRequest: WeeklySalarySummaryReportRequest) {
    return this.service.weeklySalarySummaryReport(reportRequest);
  }

  @Post('daily-labor-report')
  getDailyLaborReport(@Body() reportRequest: DailyLaborReportRequest) {
    return this.service.dailyLaborReport(reportRequest);
  }

  @Post('sales-mix-csv-report')
  getSalesMixCSVReport(@Body() reportRequest: SalesMixCsvReportRequest) {
    return this.service.salesMixCsvReport(reportRequest);
  }

  @Post('products-mix')
  getProductsMix(@Body() reportRequest: ProductsAndPaymentsCsvReportRequest) {
    return this.service.productsAndPaymentsCsvReport(reportRequest);
  }

  @Get('stats/business/:businessId/:startDate/:endDate')
  getStatsForBusiness(@Param('businessId') businessId: string, @Param('startDate') startDate: string, @Param('endDate') endDate: string, @Query() query: any) {
    return this.dataService.getStatsForBusiness({
      id: businessId,
      startDate,
      endDate,
      query: stringify(query),
    });
  }

  @Get('stats/staff/:staffId/:startDate/:endDate')
  getStatsForStaffMember(@Param('startDate') startDate: string, @Param('endDate') endDate: string, @Param('staffId') staffId: string, @Query() query: any) {
    return this.dataService.getStatsForStaffMember({
      id: staffId,
      startDate,
      endDate,
      query: stringify(query),
    });
  }

  @Get('stats/third-party/:businessId/:startDate/:endDate')
  getStatsForThirdParty(@Param('businessId') businessId: string, @Param('startDate') startDate: string, @Param('endDate') endDate: string) {
    return this.dataService.getStatsForBusiness({
      id: businessId,
      startDate,
      endDate,
      query: 'thirdParty=true',
    });
  }

  @Get('report/sales-report/:businessId/:startDate/:endDate')
  getSalesReport(@Param('businessId') businessId: string, @Param('startDate') startDate: string, @Param('endDate') endDate: string, @Query() query: unknown) {
    return this.generateService.salesReport({
      id: businessId,
      startDate,
      endDate,
      query: stringify(query),
    });
  }

  @Get('report/product-sales-report/:businessId/:startDate/:endDate')
  getProductSalesReport(@Param('businessId') businessId: string, @Param('startDate') startDate: string, @Param('endDate') endDate: string, @Query() query: unknown) {
    return this.generateService.productSalesReport({
      id: businessId,
      startDate,
      endDate,
      query: stringify(query),
    });
  }

  @Get('report/labor-report/:businessId/:startDate/:endDate')
  getLaborReport(@Param('businessId') businessId: string, @Param('startDate') startDate: string, @Param('endDate') endDate: string, @Query() query: unknown) {
    return this.generateService.laborCostReport({
      id: businessId,
      startDate,
      endDate,
      query: stringify(query),
    });
  }

  @Get('report/daily-sales-report/:businessId/:startDate/:endDate')
  getDailySalesReportV2(@Param('businessId') businessId: string, @Param('startDate') startDate: string, @Param('endDate') endDate: string, @Query() query: unknown) {
    return this.generateService.dailySalesReport({
      id: businessId,
      startDate,
      endDate,
      query: stringify(query),
    });
  }

  @Get('report/gross-margin-report/:businessId/:startDate/:endDate')
  getGrossMarginReport(@Param('businessId') businessId: string, @Param('startDate') startDate: string, @Param('endDate') endDate: string, @Query() query: unknown) {
    return this.generateService.grossMarginReport({
      id: businessId,
      startDate,
      endDate,
      query: stringify(query),
    });
  }

  @Get('report/labour-margin-report/:businessId/:startDate/:endDate/:cadence')
  getLabourMarginReport(
    @Param('businessId') businessId: string,
    @Param('startDate') startDate: string,
    @Param('endDate') endDate: string,
    @Param('cadence') cadence: string,
    @Query() query: unknown,
  ) {
    return this.generateService.labourMarginReport({
      id: businessId,
      startDate,
      endDate,
      cadence,
      query: stringify(query),
    });
  }

  @Get('report/food-cost-report/:businessId/:startDate/:endDate')
  getFoodCostReport(@Param('businessId') businessId: string, @Param('startDate') startDate: string, @Param('endDate') endDate: string, @Query() query: unknown) {
    return this.generateService.foodCostReport({
      id: businessId,
      startDate,
      endDate,
      query: stringify(query),
    });
  }
  @Get('report/customer-visits/:businessId/:startDate/:endDate')
  getCustomerVisitsReport(@Param('businessId') businessId: string, @Param('startDate') startDate: string, @Param('endDate') endDate: string, @Query() query: unknown) {
    return this.generateService.customerVisitsReport({
      id: businessId,
      startDate,
      endDate,
      query: stringify(query),
    });
  }

  @Get('report/enterprise-labour-margin-report/:enterpriseId/:startDate/:endDate/:cadence')
  getEnterpriseLabourMarginReport(
    @Param('enterpriseId') enterpriseId: string,
    @Param('startDate') startDate: string,
    @Param('endDate') endDate: string,
    @Param('cadence') cadence: string,
    @Query() query: unknown,
  ) {
    return this.generateService.enterpriseLabourMarginReport({
      id: enterpriseId,
      startDate,
      endDate,
      cadence,
      query: stringify(query),
    });
  }

  @Get('report/sales-mix-report/:businessId/:startDate/:endDate')
  getSalesMixReportV2(@Param('businessId') businessId: string, @Param('startDate') startDate: string, @Param('endDate') endDate: string, @Query() query: unknown) {
    return this.generateService.salesMixReport({
      id: businessId,
      startDate,
      endDate,
      query: stringify(query),
    });
  }

  @Get('report/pay-summary-report/:businessId/:startDate/:endDate')
  getPaySummaryReport(@Param('businessId') businessId: string, @Param('startDate') startDate: string, @Param('endDate') endDate: string, @Query() query: unknown) {
    return this.generateService.paySummaryReport({
      id: businessId,
      startDate,
      endDate,
      query: stringify(query),
    });
  }

  @Post('report/custom/:businessId/:startDate/:endDate')
  getCustomReport(@Param('businessId') businessId: string, @Param('startDate') startDate: string, @Param('endDate') endDate: string, @Body() body: CustomReportRequest) {
    return this.generateService.customReport({
      ...body,
      businessId,
      startDate,
      endDate,
    });
  }

  @Get('detailed-stats/staff/:staffId/:startDate/:endDate')
  getDetailedStatsForStaffMember(@Param('staffId') staffId: string, @Param('startDate') startDate: string, @Param('endDate') endDate: string) {
    return this.detailedDataService.getDetailedStatsForStaffMember({
      id: staffId,
      startDate,
      endDate,
    });
  }

  @Get('detailed-stats/business/:businessId/:startDate/:endDate')
  getDetailedStatsForBusiness(
    @Param('businessId') businessId: string,
    @Param('startDate') startDate: string,
    @Param('endDate') endDate: string,
    @Query() query: unknown,
  ) {
    return this.detailedDataService.getDetailedStatsForBusiness({
      id: businessId,
      startDate,
      endDate,
      query: stringify(query),
    });
  }

  @Get('detailed-stats/average/business/:businessId/:startDate/:endDate')
  getAverageStatsForBusiness(@Param('businessId') businessId: string, @Param('startDate') startDate: string, @Param('endDate') endDate: string, @Query() query: unknown) {
    return this.detailedDataService.getAverageStatsForBusiness({
      id: businessId,
      startDate,
      endDate,
      query: stringify(query),
    });
  }

  @Get('detailed-stats/incrementalised-average/business/:businessId/:startDate/:endDate')
  getIncrementalisedAverageStatsForBusiness(
    @Param('businessId') businessId: string,
    @Param('startDate') startDate: string,
    @Param('endDate') endDate: string,
    @Query() query: unknown,
  ) {
    return this.detailedDataService.getIncrementalisedAverageStatsForBusiness({
      id: businessId,
      startDate,
      endDate,
      query: stringify(query),
    });
  }

  @Get('detailed-stats/enterprise/:enterpriseId/:startDate/:endDate')
  getDetailedStatsForEnterprise(@Param('enterpriseId') enterpriseId: string, @Param('startDate') startDate: string, @Param('endDate') endDate: string) {
    return this.detailedDataService.getDetailedStatsForEnterprise({
      id: enterpriseId,
      startDate,
      endDate,
    });
  }

  @Delete('detailed-stats/bust-cache/:businessId/:startDate/:endDate')
  bustCacheForBusiness(@Param('businessId') businessId: string, @Param('startDate') startDate: string, @Param('endDate') endDate: string) {
    return this.detailedDataService.bustCacheForBusiness({
      id: businessId,
      startDate,
      endDate,
    });
  }

  @Get('notification/:userId')
  getNotificationForUser(@Param('userId') userId: string) {
    return this.generateService.getReportingNotificationForUser({
      id: userId,
    });
  }

  @Post('create-notification')
  createNotification(@Body() payload: CreateReportingNotificationRequest) {
    return this.generateService.createReportingNotification(payload);
  }

  @Delete('delete-notification/:id')
  deleteNotification(@Param('id') id: string) {
    return this.generateService.cancelReportingNotification({
      id,
    });
  }

  @Get('tipouts/business/:businessId')
  getTipOuts(@Param('businessId') id: string) {
    return this.tipOutService.getTipOuts({
      id,
    });
  }

  @Post('tipouts')
  createTipOut(@Body() payload: CreateTipOutRequest) {
    return this.tipOutService.createTipOut(payload);
  }

  @Put('tipouts/:id')
  updateTipOut(@Param('id') id: string, @Body() payload: UpdateTipOutRequest) {
    return this.tipOutService.updateTipOut({
      id,
      ...payload,
    });
  }

  @Delete('tipouts/:id')
  deleteTipOut(@Param('id') id: string) {
    return this.tipOutService.deleteTipOut({
      id,
    });
  }

  @Get('product-performance/:businessId/:start/:end')
  getProductPerformance(@Param('businessId') businessId: string, @Param('start') start: string, @Param('end') end: string) {
    return this.analysisService.analyzeProductPerformance({
      id: businessId,
      startDate: start,
      endDate: end,
    });
  }

  @Get('store-performance/:businessId')
  getStorePerformance(@Param('businessId') businessId: string, @Query() query: any) {
    return this.analysisService.getStorePerformance({
      id: businessId,
      query: stringify(query),
    });
  }

  @Get('staff-performance/:businessId/:startDate/:endDate')
  getStaffPerformance(@Param('businessId') businessId: string, @Param('startDate') startDate: string, @Param('endDate') endDate: string) {
    return this.analysisService.analyzeStaffPerformance({
      id: businessId,
      startDate,
      endDate,
    });
  }

  @Put('insights/dialogue')
  createDialogueInsight(@Body() payload: DialogInsightRequest) {
    return this.insightsService.requestDialogInsight({
      ...payload,
      dialogId: undefined,
    });
  }

  @Post('insights/dialogue/:dialogId')
  continueDialogueInsight(@Param('dialogId') dialogId: string, @Body() payload: DialogInsightRequest) {
    return this.insightsService.requestDialogInsight({
      ...payload,
      dialogId,
    });
  }

  @Get('insights/dialogue/business/:businessId')
  getDialoguesForBusiness(@Param('businessId') businessId: string) {
    return this.insightsService.getDialogues({
      id: businessId,
    });
  }

  @Get('insights/dialogue/all')
  @UseGuards(superAdmin)
  getAllDialogues() {
    return this.insightsService.getAllDialogues({
      query: '',
    });
  }

  @Get('insights/dialogue/:dialogId')
  getDialogueInsight(@Param('dialogId') dialogId: string) {
    return this.insightsService.getDialogue({
      id: dialogId,
    });
  }

  @Post('copilot')
  queryCopilot(@Body() payload: QueryCopilotRequestDto) {
    return this.copilotService.queryCopilot(payload);
  }
}
