import { Body, Controller, Delete, Get, Inject, Logger, Param, Post, Put, UseGuards, UseInterceptors } from '@nestjs/common';
import { SentryInterceptor } from '../sentry/sentry.interceptor';
import { AvailableClients } from '@tangopay/shared/grpc-clients';
import { ClientGrpc } from '@nestjs/microservices';
import { CreateReviewableRequest, REVIEWS_SERVICE_NAME, ReviewsServiceClient, UpdateReviewableRequest } from 'apps/reporting/src/proto/reporting/reviews.pb';
import { businessIdFromBody, businessIdFromParams, reviewableIdFromBody, reviewableIdFromParams, superAdmin } from '../middleware/basicGuards';

@UseInterceptors(SentryInterceptor)
@Controller('v2/reviews')
export class ReviewsGatewayController {
  constructor(
    @Inject(AvailableClients.REPORTING_PACKAGE.packageName)
    private client: ClientGrpc,
  ) {}

  private service: ReviewsServiceClient;
  private readonly logger = new Logger(ReviewsGatewayController.name);

  onModuleInit() {
    this.service = this.client.getService<ReviewsServiceClient>(REVIEWS_SERVICE_NAME);
  }

  @Get('reviewables/business/:businessId')
  @UseGuards(businessIdFromParams)
  getReviewables(@Param('businessId') businessId: string) {
    return this.service.getReviewables({
      id: businessId,
    });
  }

  @Get('reviewable/:reviewableId')
  @UseGuards(reviewableIdFromParams)
  getReviewable(@Param('reviewableId') reviewableId: string) {
    return this.service.getReviewable({
      id: reviewableId,
    });
  }

  @Post('reviewables')
  @UseGuards(businessIdFromBody)
  createReviewable(@Body() body: CreateReviewableRequest) {
    return this.service.createReviewable(body);
  }

  @Put('reviewables')
  @UseGuards(reviewableIdFromBody)
  updateReviewable(@Body() body: UpdateReviewableRequest) {
    return this.service.updateReviewable(body);
  }

  @Delete('reviewables/:reviewableId')
  @UseGuards(reviewableIdFromParams)
  deleteReviewable(@Param('reviewableId') reviewableId: string) {
    return this.service.deleteReviewable({
      id: reviewableId,
    });
  }

  @Post('reviewables/refresh/:businessId')
  @UseGuards(superAdmin)
  refreshReviews(@Param('businessId') businessId: string) {
    return this.service.refreshReviews({
      id: businessId,
    });
  }

  @Post('insights/generate/:businessId')
  @UseGuards(superAdmin)
  generateReviewInsights(@Param('businessId') businessId: string) {
    return this.service.generateReviewInsights({
      id: businessId,
    });
  }
}
