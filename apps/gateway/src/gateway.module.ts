import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { AuthGatewayController } from './auth/auth.gateway';
import { AvailableClients, registerClientModule } from '@tangopay/shared/grpc-clients';
import { AuthenticationMiddleware, PreauthAuthenticationMiddleware } from './middleware/authenticate';
import { CloudLoggingMiddleware } from './middleware/cloudLogging';
import { AuthPublicGatewayController } from './auth/auth.public.gateway';
import { BusinessGatewayController } from './business/business.gateway';
import { CashDrawersGatewayController } from './business/cash-drawers.gateway';
import { EnterpriseGatewayController } from './business/enterprise.gateway';
import { InternetReadersGatewayController, InternetReadersPublicGatewayController } from './business/internet-readers.gateway';
import { PrintersGatewayController } from './business/printers.gateway';
import { PublicBusinessGatewayController, PublicEnterpriseGatewayController } from './business/public.gateway';
import { ServiceAreasGatewayController } from './business/service-areas.gateway';
import { SettingsGatewayController } from './business/settings.gateway';
import { TablesGatewayController } from './business/tables.gateway';
import { TaxGatewayController } from './business/taxes.gateway';
import { GiftCardsGatewayController } from './giftcards/giftcards.gateway';
import { PublicGiftCardsGatewayController } from './giftcards/public.gateway';
import { KDSGatewayController, KDSV3GatewayController } from './kds/kds.gateway';
import { MenuGatewayController, PublicMenuGatewayController } from './menu/menu.gateway';
import { MenuCategoryGatewayController } from './menu/categories.gateway';
import { MenuProductGatewayController } from './menu/products.gateway';
import { MenuModifierGatewayController } from './menu/modifiers.gateway';
import { MenuModifierOptionGatewayController } from './menu/modifier-options.gateway';
import { MenuDiscountGatewayController } from './menu/discounts.gateway';
import { OrderingCashEventsGatewayController } from './ordering/cash-events.gateway';
import { OrderingGatewayController } from './ordering/orders.gateway';
import { OrderingPaymentsGatewayController } from './ordering/payments.gateway';
import { OrderingReportingGatewayController } from './ordering/reporting.gateway';
import { OrderingProductsGatewayController } from './ordering/products.gateway';
import { OrderingTabsGatewayController } from './ordering/tabs.gateway';
import { PublicOrderingGatewayController } from './ordering/public.gateway';
import { PrintingGatewayController } from './printing/printing.gateway';
import { PublicQRCodesGatewayController, QRCodesGatewayController } from './qrcodes/qrcodes.gateway';
import { ReportingGatewayController } from './reporting/reporting.gateway';
import { GatewayHealthController } from './health/gateway';
import { InventoryInfoGatewayController } from './inventory/inventoryInfo.gateway';
import { MenuUtilsGatewayController } from './menu/utils.gateway';
import { InventoryItemsGatewayController } from './inventory/inventoryItems.gateway';
import { ReputationGatewayController, ReputationPublicGatewayController } from './reputation/reputation.gateway';
import { InventoryTrackingGatewayController } from './inventory/inventoryTracking.gateway';
import { InventoryVendorsGatewayController } from './inventory/inventoryVendors.gateway';
import { UnityGatewayController } from './reporting/unity.gateway';
import { MenuNewDiscountGatewayController } from './menu/v2-discounts.gateway';
import { MenuDiscountPromocodesGatewayController } from './menu/promocodes.gateway';
import { HelpGuideGatewayController } from './business/help.gateway';
import { PayoutGatewayController, PublicPayoutGatewayController } from './business/payouts.gateway';
import { ConfigModule } from '@nestjs/config';
import { StaffingGatewayController, StaffingLegacyGatewayController } from './staffing/staffing.gateway';
import { SchedulingGatewayController } from './scheduling/scheduling.gateway';
import { DailyLogsGatewayController } from './scheduling/dailylogs.gateway';
import { PayrollGatewayController } from './staffing/payroll.gateway';
import { ContractsGatewayController } from './business/contract.gateway';
import { MenuPriceLevelGatewayController } from './menu/price-levels.gateway';
import { StaffingPublicGatewayController } from './staffing/staffing.public.gateway';
import { PublicPublishedMenusGatewayController, PublishedMenusGatewayController } from './menu/published-menus.gateway';
import { PriceVariantGatewayController } from './menu/price-variants.gateway';
import { BusinessStripeGatewayController } from './business/stripe.gateway';
import { TipoutsGatewayController } from './staffing/tipouts.gateway';
import { AIGatewayController } from './ai/ai.gateway';
import { BasicDiscountGatewayController } from './menu/basic-discounts.gateway';
import { OrderingV2OrderedProductsGatewayController } from './ordering/v2/ordered-products.gateway';
import { OrderingV2TabsGatewayController } from './ordering/v2/tabs.gateway';
import { OrderingV2OrdersGatewayController } from './ordering/v2/orders.gateway';
import { PreviewPrintGatewayController, PreviewPrintPublicGatewayController } from './printing/print.gateway';
import { PayrollCheckGatewayController } from './staffing/payroll.check.gateway';
import { ThrottlerModule } from '@nestjs/throttler';
import { PublicOrderingV2GatewayController } from './ordering/v2/public.gateway';
import { JwtModule } from '@nestjs/jwt';
import { ReservationsGatewayController } from './business/reservations.gateway';
import { PreviewPaymentsGatewayController } from './ordering/v2/payments.gateway';
import { StripeHookGatewayController } from './ordering/v2/hooks.gateway';
import { WalkInGatewayController } from './business/walk-in.gateway';
import { WaitlistGatewayController } from './business/waitlist.gateway';
import { ServiceFeesGatewayController } from './business/service-fees.gateway';
import { AvailabilityGatewayController } from './menu/availability.gateway';
import { StoreHealthGatewayController } from './business/store-health.gateway';
import { ProcessingRatesGatewayController } from './business/processing-rates.gateway';
import { CustomersGatewayController } from './business/customers.gateway';
import { TableDutyGatewayController } from './business/table-duty.gateway';
import { GoogleBookingAuthenticationMiddleware, GoogleBookingSandboxAuthenticationMiddleware } from './middleware/google-booking-authenticate';
import { GoogleBookingsGatewayController, GoogleBookingsSandboxGatewayController } from './business/google-bookings.gateway';
import { BankingGatewayController } from './banking/banking.gateway';
import { PublicBankingGatewayController } from './banking/banking.public.gateway';
import { BudgetGatewayController } from './reporting/budget.gateway';
import { CompetitorsGatewayController } from './business/competitors.gateway';
import { BusinessJoinPublicGatewayController } from './business/join.public.gateway';
import { BusinessJoinGatewayController } from './business/join.gateway';
import { AIPublicGatewayController } from './ai/ai.public.gateway';
import { ReportingIngestGatewayController } from './reporting/ingest.gateway';
import { ReportingPreauthGatewayController } from './reporting/reporting-preauth.gateway';
import { NestjsFormDataModule } from 'nestjs-form-data';
import { SchedulingAIGatewayController } from './scheduling/scheduling-ai.gateway';
import { ReportingHarvestGatewayController } from './reporting/harvest.gateway';
import { ReportingForecastingGatewayController } from './reporting/forecasting.gateway';
import { ReportingKPIsGatewayController } from './reporting/kpis.gateway';
import { ReportingDigestGatewayController } from './reporting/digest.gateway';
import { ReportingRemoteHarvestGatewayController, ReportingRemoteHarvestPublicGatewayController } from './reporting/remote-harvest.gateway';
import { InsightsInboxGatewayController } from './reporting/insights.gateway';
import { ReviewsGatewayController } from './reporting/reviews.gateway';
import { NexusGatewayController } from './realtime/nexus.gateway';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    registerClientModule([
      AvailableClients.BUSINESS_PACKAGE,
      AvailableClients.BANKING_PACKAGE,
      AvailableClients.GOOGLE_BOOKINGS_PACKAGE,
      AvailableClients.ORDERING_PACKAGE,
      AvailableClients.REALTIME_PACKAGE,
      AvailableClients.KDS_PACKAGE,
      AvailableClients.KDS_V3_PACKAGE,
      AvailableClients.MENU_PACKAGE,
      AvailableClients.AUTH_PACKAGE,
      AvailableClients.INVENTORY_PACKAGE,
      AvailableClients.PRINTING_PACKAGE,
      AvailableClients.GIFT_CARDS_PACKAGE,
      AvailableClients.QR_CODES_PACKAGE,
      AvailableClients.REPORTING_PACKAGE,
      AvailableClients.REPUTATION_PACKAGE,
      AvailableClients.STAFFING_PACKAGE,
      AvailableClients.SCHEDULING_PACKAGE,
      AvailableClients.AI_PACKAGE,
      AvailableClients.BOOKING_PACKAGE,
      AvailableClients.PRINT_PACKAGE,
    ]),
    ThrottlerModule.forRoot([
      {
        ttl: 60000,
        limit: 30,
      },
    ]),
    JwtModule.register({
      secret: 'dev',
      signOptions: { expiresIn: '365d' },
    }),
    NestjsFormDataModule,
  ],
  controllers: [
    GatewayHealthController,
    GoogleBookingsGatewayController,
    GoogleBookingsSandboxGatewayController,
    AIGatewayController,
    AIPublicGatewayController,
    AuthGatewayController,
    AuthPublicGatewayController,
    BasicDiscountGatewayController,
    BusinessGatewayController,
    CompetitorsGatewayController,
    CashDrawersGatewayController,
    ContractsGatewayController,
    EnterpriseGatewayController,
    CustomersGatewayController,
    InternetReadersGatewayController,
    TableDutyGatewayController,
    ReservationsGatewayController,
    WalkInGatewayController,
    WaitlistGatewayController,
    InventoryInfoGatewayController,
    InventoryItemsGatewayController,
    InventoryTrackingGatewayController,
    InventoryVendorsGatewayController,
    PrintersGatewayController,
    PublicBusinessGatewayController,
    PayoutGatewayController,
    PublicPayoutGatewayController,
    PublicEnterpriseGatewayController,
    ServiceAreasGatewayController,
    ServiceFeesGatewayController,
    SettingsGatewayController,
    TablesGatewayController,
    TaxGatewayController,
    GiftCardsGatewayController,
    PublicGiftCardsGatewayController,
    KDSGatewayController,
    KDSV3GatewayController,
    MenuGatewayController,
    PublicMenuGatewayController,
    MenuCategoryGatewayController,
    MenuProductGatewayController,
    MenuModifierGatewayController,
    MenuModifierOptionGatewayController,
    MenuDiscountGatewayController,
    OrderingGatewayController,
    OrderingPaymentsGatewayController,
    PreviewPaymentsGatewayController,
    OrderingReportingGatewayController,
    OrderingProductsGatewayController,
    OrderingCashEventsGatewayController,
    OrderingTabsGatewayController,
    OrderingV2TabsGatewayController,
    OrderingV2OrdersGatewayController,
    OrderingV2OrderedProductsGatewayController,
    PublicOrderingV2GatewayController,
    PublicOrderingGatewayController,
    PrintingGatewayController,
    QRCodesGatewayController,
    PublicQRCodesGatewayController,
    ReviewsGatewayController,
    ReportingGatewayController,
    ReportingPreauthGatewayController,
    UnityGatewayController,
    MenuUtilsGatewayController,
    ReputationGatewayController,
    ReputationPublicGatewayController,
    MenuNewDiscountGatewayController,
    MenuDiscountPromocodesGatewayController,
    HelpGuideGatewayController,
    InsightsInboxGatewayController,
    StaffingGatewayController,
    StaffingLegacyGatewayController,
    StaffingPublicGatewayController,
    SchedulingGatewayController,
    SchedulingAIGatewayController,
    DailyLogsGatewayController,
    PayrollGatewayController,
    MenuPriceLevelGatewayController,
    PublishedMenusGatewayController,
    PublicPublishedMenusGatewayController,
    PriceVariantGatewayController,
    BusinessStripeGatewayController,
    TipoutsGatewayController,
    PreviewPrintPublicGatewayController,
    PreviewPrintGatewayController,
    PayrollCheckGatewayController,
    StripeHookGatewayController,
    AvailabilityGatewayController,
    InternetReadersPublicGatewayController,
    StoreHealthGatewayController,
    ProcessingRatesGatewayController,
    BankingGatewayController,
    PublicBankingGatewayController,
    BudgetGatewayController,
    BusinessJoinPublicGatewayController,
    BusinessJoinGatewayController,
    ReportingIngestGatewayController,
    ReportingHarvestGatewayController,
    ReportingForecastingGatewayController,
    ReportingKPIsGatewayController,
    ReportingDigestGatewayController,
    ReportingRemoteHarvestPublicGatewayController,
    ReportingRemoteHarvestGatewayController,
    NexusGatewayController,
  ],
})
export class GatewayModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(CloudLoggingMiddleware).forRoutes('*');
    consumer
      .apply(AuthenticationMiddleware)
      .exclude('/v2/public/(.*)', '/v2/google-bookings/(.*)', '/v2/google-bookings-sandbox/(.*)', '/v2/preauth/(.*)')
      .forRoutes('*');
    consumer.apply(PreauthAuthenticationMiddleware).forRoutes('/v2/preauth/*');
    consumer.apply(GoogleBookingAuthenticationMiddleware).forRoutes('/v2/google-bookings/*');
    consumer.apply(GoogleBookingSandboxAuthenticationMiddleware).forRoutes('/v2/google-bookings-sandbox/*');
  }
}
