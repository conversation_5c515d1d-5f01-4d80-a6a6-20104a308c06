import { Body, Controller, Get, HttpStatus, Inject, Logger, Param, Post, Put, UseGuards, UseInterceptors } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { AvailableClients } from '@tangopay/shared/grpc-clients';
import {
  AUTH_SERVICE_NAME,
  AuthServiceClient,
  ChangeEmailRequest,
  ChangePhoneNumberRequest,
  CreatePreAuthorisedSessionRequest,
  CreateUserRequest,
  FCMTokenRequest,
  UpdateUserRequest,
} from 'apps/auth/src/proto/auth.pb';
import { User } from '../decorators/User.decorator';
import { SentryInterceptor } from '../sentry/sentry.interceptor';
import { superAdmin } from '../middleware/basicGuards';

@UseInterceptors(SentryInterceptor)
@Controller('v2/auth')
export class AuthGatewayController {
  constructor(
    @Inject(AvailableClients.AUTH_PACKAGE.packageName)
    private client: ClientGrpc,
  ) {}

  private service: AuthServiceClient;
  private readonly logger = new Logger(AuthGatewayController.name);
  onModuleInit() {
    this.service = this.client.getService<AuthServiceClient>(AUTH_SERVICE_NAME);
  }

  /**
   * Authenticated endpoints
   */
  @Get('get-all-users')
  getAllUsers() {
    return this.service.getAllUsers({ businessId: '' });
  }

  @Get('get-current-user')
  async getCurrentUser(
    @User()
    user: {
      id: string;
    },
  ) {
    return this.service.getUser({
      userId: user.id,
    });
  }

  @Get('get-user/:userId')
  async getUser(@Param('userId') userId: string) {
    return this.service.getUser({
      userId,
    });
  }

  @Get('get-current-user-profile')
  async getCurrentUserProfile(
    @User()
    user: {
      id: string;
    },
  ) {
    return this.service.getCurrentUserProfile({
      userId: user.id,
    });
  }

  @Post('create-user')
  createUser(@Body() createRequest: CreateUserRequest) {
    return this.service.createUser(createRequest);
  }

  @Put('update-user')
  updateUser(
    @Body() updateRequest: UpdateUserRequest,
    @User()
    user: {
      id: string;
    },
  ) {
    return this.service.updateUser({
      ...updateRequest,
      userId: user.id,
    });
  }

  // TODO: Implement when `superAdmin` guard is available
  @Put('update-other-user')
  @UseGuards(superAdmin)
  updateOtherUser(@Body() updateRequest: UpdateUserRequest) {
    return this.service.updateUser({
      ...updateRequest,
    });
  }

  @Post('send-email-verification/:userId')
  sendEmailVerification(@Param('userId') userId: string) {
    return this.service.sendEmailVerification({
      id: userId,
    });
  }

  @Post('send-phone-verification/:userId')
  sendPhoneVerification(@Param('userId') userId: string) {
    return this.service.sendPhoneNumberVerification({
      id: userId,
    });
  }

  @Post('change-email')
  changeEmail(@Body() changeEmailRequest: ChangeEmailRequest, @User() user: { id: string }) {
    return this.service.changeEmail({
      ...changeEmailRequest,
      userId: user.id,
    });
  }

  @Post('change-phone-number')
  changePhoneNumber(@Body() changePhoneNumberRequest: ChangePhoneNumberRequest, @User() user: { id: string }) {
    return this.service.changePhoneNumber({
      ...changePhoneNumberRequest,
      userId: user.id,
    });
  }

  @Get('login-history')
  getLoginHistory(@User() user: { id: string }) {
    return this.service.getLoginHistory({
      id: user.id,
    });
  }

  @Post('pre-authorised-session')
  @UseGuards(superAdmin)
  createPreAuthorisedSession(@Body() createPreAuthorisedSessionRequest: CreatePreAuthorisedSessionRequest) {
    return this.service.createPreAuthorisedSession(createPreAuthorisedSessionRequest);
  }

  @Put('update-fcm-token')
  updateFCMToken(
    @Body() updateRequest: FCMTokenRequest,
    @User()
    user: {
      id: string;
      sessionId: string;
    },
  ) {
    return this.service.attachFcmToken({
      sessionId: user.sessionId,
      fcmToken: updateRequest.fcmToken,
    });
  }

  @Post('logout')
  logout(@User() user: { id: string; sessionId: string }) {
    return this.service.logout({ id: user.sessionId });
  }
}
