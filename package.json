{"name": "tango-cloud", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write .", "start": "nest start", "start:db": "docker-compose down && docker-compose build postgres redis && docker-compose up postgres redis", "start:redis": "docker-compose down && docker-compose build redis && docker-compose up redis", "start:local": "./make-cloud-fast && node ./local.mjs", "start:linux": "./linux-cloud-fast && node ./local.mjs", "start:dev": "nest start --watch", "dev:reporting": "npm run start:dev reporting", "start:debug": "nest start --debug --watch", "start:prod": "node dist/apps/tango-cloud/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest --detectOpenHandles --coverage --collectCoverageFrom=\"./apps/**/*.{js,ts}\" --coveragePathIgnorePatterns=\"^.+\\.(pb|dto|module|entity|controller|gateway|interface|mock)\\.ts$\"", "ci:test": "jest --detectOpenHandles --coveragePathIgnorePatterns=\"^.+\\.(pb|dto|module|entity|controller|gateway|interface|mock)\\.ts$\"", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./apps/tango-cloud/test/jest-e2e.json", "dock": "docker-compose up", "dock:clean": "docker system prune --all --force", "redock": "docker-compose down && docker-compose build && docker-compose up", "redock:clean": "docker-compose down && docker system prune --all --force && docker-compose build && docker-compose up", "redock:linuxclean": "docker-compose down && sudo chmod +rwx ./postgres-data && docker system prune --all --force --volumes && docker-compose build && docker-compose up", "build:ai": "nest build ai", "build:auth": "nest build auth", "build:banking": "nest build banking", "build:booking": "nest build booking", "build:business": "nest build business", "build:cron": "nest build cron", "build:gateway": "nest build gateway", "build:giftcards": "nest build giftcards", "build:inventory": "nest build inventory", "build:kds": "nest build kds", "build:menu": "nest build menu", "build:ordering": "nest build ordering", "build:printing": "nest build printing", "build:qrcodes": "nest build qrcodes", "build:realtime": "nest build realtime", "build:reporting": "nest build reporting", "build:reputation": "nest build reputation", "build:roles": "nest build roles", "build:sandbox": "nest build sandbox", "build:scheduling": "nest build scheduling", "build:staffing": "nest build staffing", "build:uploader": "nest build uploader", "clean": "rimraf lib/", "proto:ai": "protoc  --proto_path=./tango-proto/ --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./tango-proto/proto --ts_proto_out=apps/ai/src/ tango-proto/proto/ai/*.proto --ts_proto_opt=nestJs=true --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=addGrpcMetadata=true", "proto:auth": "protoc --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./tango-proto/proto --ts_proto_out=apps/auth/src/proto/ tango-proto/proto/auth.proto --ts_proto_opt=nestJs=true --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=addGrpcMetadata=true", "proto:banking": "protoc --proto_path=./tango-proto/ --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./tango-proto/proto --ts_proto_out=apps/banking/src/ tango-proto/proto/banking/*.proto --ts_proto_opt=nestJs=true --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=addGrpcMetadata=true", "proto:booking": "protoc --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./tango-proto/proto --ts_proto_out=apps/booking/src/proto/ tango-proto/proto/booking/*.proto --ts_proto_opt=nestJs=true --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=snakeToCamel=false --ts_proto_opt=addGrpcMetadata=true", "proto:business": "protoc --proto_path=./tango-proto/ --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./tango-proto/proto --ts_proto_out=apps/business/src/proto/ tango-proto/proto/business/*.proto  --ts_proto_opt=snakeToCamel=false --ts_proto_opt=nestJs=true --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=addGrpcMetadata=true", "proto:giftcards": "protoc --proto_path=./tango-proto/ --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./tango-proto/proto --ts_proto_out=apps/gift-cards/src/proto/ tango-proto/proto/gift-cards/*.proto --ts_proto_opt=nestJs=true --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=addGrpcMetadata=true", "proto:health": "protoc --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./tango-proto/proto --ts_proto_out=libs/shared/src/health/proto/ tango-proto/proto/health.proto --ts_proto_opt=nestJs=true --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=addGrpcMetadata=true", "proto:inventory": "protoc --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./tango-proto/proto --ts_proto_out=apps/inventory/src/proto tango-proto/proto/inventory/*.proto --ts_proto_opt=nestJs=true --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=addGrpcMetadata=true", "proto:kds": "protoc --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./tango-proto/proto --ts_proto_out=apps/kds/src/proto/ tango-proto/proto/kds.proto --ts_proto_opt=nestJs=true --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=addGrpcMetadata=true", "proto:kdsv3": "protoc --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./tango-proto/proto --ts_proto_out=apps/kds/src/proto/ tango-proto/proto/kds/kds.proto --ts_proto_opt=nestJs=true --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=addGrpcMetadata=true", "proto:menu": "protoc --proto_path=./tango-proto/ --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./tango-proto/proto --ts_proto_out=apps/menu/src/proto/ tango-proto/proto/menu-management.proto --ts_proto_opt=nestJs=true --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=addGrpcMetadata=true", "proto:ordering": "protoc --proto_path=./tango-proto/ --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./tango-proto/proto --ts_proto_out=apps/ordering/src/proto/ tango-proto/proto/ordering/*.proto --ts_proto_opt=nestJs=true --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=addGrpcMetadata=true", "proto:people": "protoc --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./tango-proto/proto --ts_proto_out=apps/staffing/src/proto/ tango-proto/proto/people/*.proto --ts_proto_opt=nestJs=true --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=addGrpcMetadata=true", "proto:print": "protoc --proto_path=./tango-proto/ --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./tango-proto/proto --ts_proto_out=apps/printing/src/proto/ tango-proto/proto/print/*.proto --ts_proto_opt=nestJs=true --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=addGrpcMetadata=true", "proto:printing": "protoc --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./tango-proto/proto --ts_proto_out=apps/printing/src/proto/ tango-proto/proto/printing/*.proto --ts_proto_opt=nestJs=true --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=addGrpcMetadata=true", "proto:qrcodes": "protoc --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./tango-proto/proto --ts_proto_out=apps/qrcodes/src/proto/ tango-proto/proto/qr-codes.proto --ts_proto_opt=nestJs=true --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=addGrpcMetadata=true", "proto:realtime": "protoc --proto_path=./tango-proto/ --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./tango-proto/proto --ts_proto_out=apps/realtime/src/proto/ tango-proto/proto/realtime/*.proto --ts_proto_opt=nestJs=true --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=addGrpcMetadata=true", "proto:reporting": "protoc --proto_path=./tango-proto/ --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./tango-proto/proto --ts_proto_out=apps/reporting/src/ tango-proto/proto/reporting/*.proto --ts_proto_opt=nestJs=true --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=addGrpcMetadata=true", "proto:reputation": "protoc --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./tango-proto/proto --ts_proto_out=apps/reputation/src/proto/ tango-proto/proto/reputation-management.proto --ts_proto_opt=nestJs=true --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=addGrpcMetadata=true", "proto:sandbox": "protoc --proto_path=./tango-proto/ --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./tango-proto/proto --ts_proto_out=apps/sandbox/src/ tango-proto/proto/sandbox/*.proto --ts_proto_opt=nestJs=true --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=addGrpcMetadata=true", "proto:scheduling": "protoc  --proto_path=./tango-proto/ --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./tango-proto/proto --ts_proto_out=apps/scheduling/src/ tango-proto/proto/scheduling/*.proto --ts_proto_opt=nestJs=true --ts_proto_opt=fileSuffix=.pb --ts_proto_opt=addGrpcMetadata=true", "proto:update:local": "yarn add-no-save file:./tango-proto", "proto:get": "yarn proto:auth && yarn proto:business && yarn proto:booking && yarn proto:ordering && yarn proto:menu && yarn proto:scheduling && yarn proto:qrcodes && yarn proto:inventory && yarn proto:realtime && yarn proto:giftcards && yarn proto:kds && yarn proto:kdsv3 && yarn proto:printing && yarn proto:reporting && yarn proto:reputation && yarn proto:people && yarn proto:ai && yarn proto:print && yarn proto:banking && yarn proto:health && yarn proto:sandbox", "gen-docs": "compodoc -p tsconfig.doc.json --serve", "install:all": "yarn && yarn --cwd flock", "flock:start": "node flock/build/index.js", "flock:build": "yarn --cwd flock parcel build", "flock:watch": "yarn --cwd flock parcel watch", "flock:clean": "cd flock && rm -rf build .parcel-cache", "flock:veryClean": "cd flock && rm -rf build .parcel-cache node_modules", "flock:build:clean": "yarn flock:clean && yarn flock:build", "flock:build:veryClean": "yarn flock:veryClean && yarn --cwd flock && yarn flock:build", "flock:prepare": "husky install", "publish:proto:alpha": "cd tango-proto && npm publish --tag alpha", "stripe:forward-hooks": "stripe listen --forward-to localhost:50040/v2/public/payments-preview/hooks/stripe/ca-test/terminal-events --events terminal.reader.action_failed,charge.succeeded,charge.failed,charge.refunded,payment_intent.amount_capturable_updated", "stripe:forward-hooks-us": "stripe listen --forward-to localhost:50040/v2/public/payments-preview/hooks/stripe/us-test/terminal-events --events terminal.reader.action_failed,charge.succeeded,charge.failed,charge.refunded,payment_intent.amount_capturable_updated", "stripe:reauthenticate": "stripe login"}, "dependencies": {"@anthropic-ai/sdk": "^0.52.0", "@faker-js/faker": "^8.0.2", "@google-cloud/documentai": "^8.8.0", "@google-cloud/logging": "^11.0.0", "@google-cloud/pubsub": "^3.7.3", "@google-cloud/storage": "^6.9.5", "@googlemaps/google-maps-services-js": "^3.3.34", "@grpc/grpc-js": "^1.9.4", "@grpc/proto-loader": "^0.7.4", "@larlon/verifier-and-challenge": "^2.1.0", "@liaoliaots/nestjs-redis": "^9.0.5", "@mergeapi/merge-node-client": "^1.0.9", "@nestjs/axios": "^2.0.0", "@nestjs/cli": "^9.0.0", "@nestjs/common": "^9.0.0", "@nestjs/config": "^2.2.0", "@nestjs/core": "9.2.1", "@nestjs/jwt": "^10.0.1", "@nestjs/mapped-types": "*", "@nestjs/microservices": "9.2.1", "@nestjs/passport": "^9.0.0", "@nestjs/platform-express": "^9.0.0", "@nestjs/platform-socket.io": "9.1.6", "@nestjs/schedule": "^2.2.1", "@nestjs/schematics": "^9.0.0", "@nestjs/serve-static": "^3.0.1", "@nestjs/terminus": "^9.2.1", "@nestjs/throttler": "^5.0.1", "@nestjs/typeorm": "^9.0.1", "@nestjs/websockets": "9.1.6", "@ntegral/nestjs-sentry": "^4.0.0", "@react-pdf/renderer": "^3.1.9", "@sendgrid/inbound-mail-parser": "^8.0.0", "@sendgrid/mail": "^7.7.0", "@sentry/node": "^7.57.0", "@socket.io/redis-adapter": "^8.1.0", "@swc/helpers": "^0.4.14", "@tangopay/tango-unity": "^1.1.10", "@types/parse-data-url": "^3.0.2", "@types/qrcode": "^1.5.0", "@types/redis": "^4.0.11", "axios": "^1.3.5", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "color": "^4.2.3", "compression": "^1.7.4", "dayjs": "^1.11.9", "dotenv": "^16.3.1", "excel4node": "^1.8.2", "fast-xml-parser": "^4.5.1", "firebase-admin": "^13.4.0", "haversine": "^1.1.1", "ioredis": "^5.3.2", "js-base64": "^3.7.7", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.1", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "mailparser": "^3.7.2", "natural": "^6.8.0", "nestjs-form-data": "^1.9.92", "node-rsa": "^1.1.1", "node-x12": "^1.7.1", "numeral": "^2.0.6", "nylas": "^7.7.2", "openai": "^5.0.1", "otplib": "^12.0.1", "painless-pdf": "^1.4.1", "papaparse": "^5.4.1", "parse-data-url": "^6.0.0", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "path": "^0.12.7", "pg": "^8.8.0", "plaid": "^26.0.0", "puppeteer": "^23.9.0", "qrcode": "^1.5.1", "qs": "^6.11.2", "react": "^18.2.0", "react-dom": "^18.2.0", "redis": "^4.6.5", "reflect-metadata": "^0.1.13", "rxjs": "^7.2.0", "serpapi": "^2.1.0", "sharp": "^0.33.5", "showdown": "^2.1.0", "socket.io": "^4.6.1", "ssh2-sftp-client": "^9.0.0", "stream-chat": "^8.11.0", "stripe": "^11.15.0", "tiktoken": "^1.0.21", "ts-loader": "^9.2.3", "twilio": "^3.50.0", "typeorm": "^0.3.20", "x-crawl": "^10.0.2", "zod": "^3.23.8"}, "devDependencies": {"@compodoc/compodoc": "^1.1.22", "@golevelup/ts-jest": "^0.3.4", "@nestjs/testing": "9.1.6", "@swc/cli": "^0.1.63", "@swc/core": "^1.3.99", "@types/cron": "^2.0.1", "@types/express": "^4.17.13", "@types/jest": "29.2.4", "@types/lodash": "^4.14.191", "@types/mailparser": "^3.4.5", "@types/multer": "^1.4.7", "@types/node": "^18.11.18", "@types/node-kmeans": "^1.1.2", "@types/passport-jwt": "^3.0.8", "@types/react": "^18.0.37", "@types/react-dom": "^18.0.11", "@types/ssh2-sftp-client": "^9.0.4", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "concurrently": "^8.2.1", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "husky": "^8.0.3", "jest": "29.3.1", "mockdate": "^3.0.5", "pactum": "^3.6.6", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.3.3", "swc-loader": "^0.2.3", "ts-jest": "29.0.3", "ts-node": "^10.0.0", "ts-proto": "^1.137.0", "tsconfig-paths": "4.1.1", "typescript": "^4.7.4", "yarn-add-no-save": "^1.0.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "moduleNameMapper": {"^apps/(.*)$": "<rootDir>/apps/$1", "^@tangopay/shared(|/.*)$": "<rootDir>/libs/shared/src/$1"}, "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/apps/", "<rootDir>/libs/"]}}