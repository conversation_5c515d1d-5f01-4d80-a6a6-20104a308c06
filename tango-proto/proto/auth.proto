syntax = 'proto3';

package auth;

option go_package = "./";

service AuthService {
  //// Retrieval
  rpc GetAllUsers(GetAllUsersRequest) returns (UsersResponse) {}
  rpc GetUser(GetUserRequest) returns (UserResponse) {}
  rpc GetCurrentUserProfile(GetCurrentUserProfileRequest)
      returns (GetCurrentUserProfileResponse) {}
  // Get the login session history
  rpc GetLoginHistory(IdRequest) returns (LoginSessionResponse) {}

  //// CRUD
  rpc CreateUser(CreateUserRequest) returns (UserResponse) {}
  rpc UpdateUser(UpdateUserRequest) returns (UserResponse) {}
  rpc DeleteUser(DeleteUserRequest) returns (GenericResponse) {}

  //// Login
  rpc Login(LoginRequest) returns (LoginResponse) {}
  rpc PhoneLogin(PhoneLoginRequest) returns (LoginResponse) {}
  rpc SignInWithPhone(SignInWithPhoneRequest) returns (GenericResponse) {}
  rpc SignInWithEmail(SignInWithEmailRequest) returns (GenericResponse) {}
  rpc AttachFCMToken(FCMTokenRequest) returns (GenericResponse) {}
  rpc GetActiveSessionsForUser(IdRequest) returns (LoginSessionResponse) {}
  rpc Logout(IdRequest) returns (GenericResponse) {}

  //// Stage 2 of login + 2FA
  rpc VerifyPhoneNumber(VerifyPhoneRequest) returns (LoginResponse) {}
  rpc VerifyEmail(VerifyEmailRequest) returns (LoginResponse) {}
  // Confirm the TOTP code
  rpc ConfirmTOTP(ConfirmTOTPRequest) returns (LoginResponse) {}

  //// Update/Change Password
  rpc UpdatePassword(UpdatePasswordRequest) returns (GenericResponse) {}
  rpc SendResetPasswordLink(SignInWithEmailRequest) returns (GenericResponse) {}

  //// Verify + Change Email/Phone
  // Send email verification
  rpc SendEmailVerification(IdRequest) returns (GenericResponse) {}
  // Send phone verification
  rpc SendPhoneNumberVerification(IdRequest) returns (GenericResponse) {}
  // Send email change verification
  rpc ChangeEmail(ChangeEmailRequest) returns (GenericResponse) {}
  // Send phone change verification
  rpc ChangePhoneNumber(ChangePhoneNumberRequest) returns (GenericResponse) {}
  // Verify phone or email
  rpc VerifyEmailOrPhone(VerificationTokenRequest)
      returns (VerificationResponse) {}

  //// Internal/Hidden methods
  rpc Validate(ValidateRequest) returns (ValidateResponse) {}
  rpc RefreshToken(RefreshTokenRequest) returns (RefreshTokenResponse) {}

  rpc ExistingUser(ExistingUserRequest) returns (ExistingUserResponse) {}

  rpc SignUrl(SignUrlRequest) returns (SignUrlResponse) {}
  rpc CheckUrlSignature(CheckUrlSignatureRequest) returns (GenericResponse) {}
  rpc GetUserByEmail(EmailRequest) returns (UserResponse) {}

  rpc CreatePreAuthorisedSession(CreatePreAuthorisedSessionRequest)
      returns (PreAuthorisedSessionResponse) {}

  rpc GetPreAuthorisedSession(IdRequest)
      returns (PreAuthorisedSessionResponse) {}
}

message PreAuthorisationParameters {
  string key = 1;
  string value = 2;
}

message CreatePreAuthorisedSessionRequest {
  // type = 'scheduling'
  string sessionType = 1;
  // what routes the session has access to, if empty, all routes are allowed
  repeated string routes = 2;
  // what business ids the session has access to, if empty, no business ids are
  // allowed
  repeated string businessIds = 3;
  // what enterprise ids the session has access to, if empty, no enterprise ids
  // are allowed
  repeated string enterpriseIds = 4;
  // what email the session was sent to
  string sentTo = 5;
  // parameters for the session
  repeated PreAuthorisationParameters parameters = 6;
  // when the session expires
  string expiresAt = 7;
}

message PreAuthorisedSession {
  string id = 1;
  string sessionType = 2;
  repeated string routes = 3;
  repeated string businessIds = 4;
  repeated string enterpriseIds = 5;
  string sentTo = 6;
  string token = 7;
  repeated PreAuthorisationParameters parameters = 8;
  string adminUrl = 9;
  string createdAt = 10;
  string expiresAt = 11;
}

message PreAuthorisedSessionResponse {
  int32 status = 1;
  optional Error error = 2;
  PreAuthorisedSession data = 3;
}

message SignUrlRequest { string url = 1; }

message SignUrlResponse {
  int32 status = 1;
  optional Error error = 2;
  string data = 3;
}

message CheckUrlSignatureRequest {
  string url = 1;
  string signature = 2;
}

message EmailRequest { string email = 1; }

message ExistingUserRequest {
  optional string email = 1;
  optional string phoneNumber = 2;
}

message ExistingUserResponse {
  int32 status = 1;
  optional Error error = 2;
  bool data = 3;
}

message LoginSession {
  string id = 1;
  string ipAddress = 2;
  string method = 3;
  string platform = 4;
  bool twoFactor = 5;
  string createdAt = 6;
  string lastSeenAt = 7;
  optional string fcmToken = 8;
}

message LoginSessionResponse {
  int32 status = 1;
  optional Error error = 2;
  repeated LoginSession data = 3;
}

message ChangeEmailRequest {
  string userId = 1;
  string newEmail = 2;
}

message ChangePhoneNumberRequest {
  string userId = 1;
  string newPhoneNumber = 2;
}

message ConfirmTOTPRequest {
  string challengeId = 1;
  string code = 2;
}

message ElevateTokenRequest {
  string userId = 1;
  string accessToken = 2;
}

message SecureRequest {
  string userId = 1;
  string passwordHash = 2;
}

message Get2FATOTPSecretResponse {
  int32 status = 1;
  optional Error error = 2;
  optional string data = 3;
}

message VerificationTokenRequest { string token = 1; }

message URL { string url = 1; }

message VerificationResponse {
  int32 status = 1;
  optional Error error = 2;
  URL data = 3;
}

message IdRequest { string id = 1; }

message Error {
  string type = 1;
  string message = 2;
}

message AuthTokens {
  string accessToken = 1;
  string refreshToken = 2;
  string userId = 3;
  string totpChallenge = 4;
  string userPhoneLast4 = 5;
}

message UserAddress {
  string line1 = 1;
  optional string line2 = 2;
  string city = 3;
  string zip = 4;
  string state = 5;
  string country = 6;
}

message EmergencyContact {
  string emergencyContactName = 1;
  string emergencyContactRelation = 2;
  string emergencyContactPhoneNumber = 3;
}

message User {
  string firstName = 1;
  string lastName = 2;
  string email = 3;
  string dateOfBirth = 4;
  UserAddress address = 6;
  EmergencyContact emergencyContact = 7;
  string id = 8;
  optional string phoneNumber = 9;
  bool phoneNumberVerified = 10;
  bool emailVerified = 11;
  bool publicOnboarding = 12;
}

message GetCurrentUserProfileRequest { string userId = 1; }

message AvailableBusiness {
  string id = 1;
  string name = 2;
  optional string foodAndBeverageEnterpriseId = 3;
  optional string reputationManagementEnterpriseId = 4;
  optional string giftCardsEnterpriseId = 5;
  repeated string enterpriseIds = 6;
  optional string staffId = 7;
}

message AvailableEnterprise {
  string id = 1;
  string name = 2;
  optional string staffId = 3;
}

message AvailableUserProfilesData {
  User user = 1;
  repeated AvailableBusiness availableBusinesses = 2;
  repeated AvailableEnterprise availableEnterprises = 3;
  optional bool isSuperAdmin = 4;
}

message GetCurrentUserProfileResponse {
  int32 status = 1;
  repeated string error = 2;
  optional AvailableUserProfilesData data = 3;
}

// RefreshTokenRequest
message RefreshTokenRequest { string refreshToken = 1; }

// RefreshTokenResponse
message RefreshTokenResponse {
  int32 status = 1;
  optional Error error = 2;
  optional AuthTokens data = 3;
}

// VerifyPhoneRequest
message VerifyPhoneRequest {
  string phoneNumber = 1;
  string code = 2;
  optional string platform = 3;
  optional string ip = 4;
}

// VerifyPhoneRequest
message VerifyEmailRequest {
  string email = 1;
  string code = 2;
  optional string platform = 3;
  optional string ip = 4;
}

// SignInWithPhoneRequest
message SignInWithPhoneRequest {
  string phoneNumber = 1;
  optional string platform = 2;
}

// SignInWithEmailRequest
message SignInWithEmailRequest {
  string email = 1;
  optional string platform = 2;
}

// CreateUser
message CreateUserRequest {
  string email = 1;
  string password = 2;
  optional string phoneNumber = 3;
  string firstName = 4;
  string lastName = 5;
  optional bool publicOnboarding = 6;
}

// GetUser
message GetUserRequest { string userId = 1; }

message UserResponse {
  int32 status = 1;
  optional Error error = 2;
  optional User data = 3;
}

// EditUser
message UpdateUserRequest {
  string userId = 1;
  optional string firstName = 2;
  optional string lastName = 3;
  optional string email = 4;
  optional string phone = 5;
  optional string dateOfBirth = 6;
  optional UserAddress address = 7;
}

// DeleteUser
message DeleteUserRequest { string userId = 1; }

// Get All Users
message GetAllUsersRequest { string businessId = 1; }

message UsersResponse {
  int32 status = 1;
  optional Error error = 2;
  repeated User data = 3;
}

// Login
message LoginRequest {
  string email = 1;
  string password = 2;
  optional string platform = 3;
  optional string ip = 4;
  optional string fcmToken = 5;
}

message PhoneLoginRequest {
  string phoneNumber = 1;
  string password = 2;
  optional string platform = 3;
  optional string ip = 4;
}

message LoginResponse {
  int32 status = 1;
  optional Error error = 2;
  optional AuthTokens data = 3;
}

// Validate
message ValidateRequest { string token = 1; }

message ValidateResponse {
  message Data {
    optional string userId = 1;
    // businesses the token acts on behalf of
    repeated string businessIds = 2;
    // enterprises the token acts on behalf of
    repeated string enterpriseIds = 3;
    // businesses the token acts on behalf of in the food and bev context
    repeated string foodAndBevBusinessIds = 4;
    // enterprises the token acts on behalf of in the food and bev context
    repeated string foodAndBevEnterpriseIds = 5;
    // businesses the token acts on behalf of in the reputation context
    repeated string reputationBusinessIds = 6;
    // enterprises the token acts on behalf of in the reputation context
    repeated string reputationEnterpriseIds = 7;
    // businesses the token acts on behalf of in the gift card context
    repeated string giftCardBusinessIds = 8;
    // enterprises the token acts on behalf of in the gift card context
    repeated string giftCardEnterpriseIds = 9;
    bool isSuperUser = 10;
    // staff members the token acts on behalf of
    repeated string staffIds = 11;
    // the routes the token has access to
    repeated string routes = 12;
    // Parameters associated with a pre-authorised session
    repeated PreAuthorisationParameters parameters = 13;
    // the session id of the login session
    string sessionId = 14;
  }

  int32 status = 1;
  optional Error error = 2;
  optional Data data = 3;
}

message UpdatePasswordRequest {
  string userId = 1;
  string newPassword = 2;
  optional string previousPassword = 3;
  optional string token = 4;
}

message GenericResponse {
  int32 status = 1;
  optional Error error = 2;
}

message EmptyRequest {}

message ActiveSession {
  string id = 1;
  string userId = 2;
  string loggedInAt = 3;
  string fcmToken = 4;
}

message FCMTokenRequest {
  string sessionId = 1;
  string fcmToken = 2;
}