syntax = "proto3";

package reporting;

option go_package = "./";

import "proto/reporting/shared.proto";

service ReviewsService {
  rpc GetReviewables(IdRequest) returns (ReviewablesResponse) {}
  rpc GetReviewable(IdRequest) returns (ReviewableResponse) {}
  rpc CreateReviewable(CreateReviewableRequest) returns (ReviewableResponse) {}
  rpc UpdateReviewable(UpdateReviewableRequest) returns (ReviewableResponse) {}
  rpc DeleteReviewable(IdRequest) returns (GenericResponse) {}
  rpc RefreshReviews(IdRequest) returns (GenericResponse) {}
  rpc GenerateReviewInsights(IdRequest) returns (GenericResponse) {}
}

// Messages

message Reviewable {
  // ID of the reviewable
  string id = 1;
  // Google Places ID of the reviewable
  string googlePlacesId = 2;
  // Description of the reviewable
  string description = 3;
  // Is the reviewable the business itself
  bool isSelf = 4;
  // The reviews for the reviewable
  repeated Review reviews = 5;
  // The review snapshots for the reviewable
  repeated ReviewableSnapshot snapshots = 6;
}

message Review {
  // ID of the review
  string id = 1;
  // Score of the review
  float score = 2;
  // Snippet of the review
  string snippet = 3;
  // Reviewed at
  string reviewedAt = 4;
  // Responded at
  string respondedAt = 5;
  // Responded snippet
  string respondedSnippet = 6;
}

message ReviewableSnapshot {
  // ID of the reviewable snapshot
  string id = 1;
  // Title of the reviewable at the time of the snapshot
  string title = 2;
  // Address of the reviewable at the time of the snapshot
  string address = 3;
  // Rating of the reviewable at the time of the snapshot
  float rating = 4;
  // Review count of the reviewable at the time of the snapshot
  int32 reviewCount = 5;
  // Type of the reviewable at the time of the snapshot
  string type = 6;
  // Review topics associated with the reviewable at the time of the snapshot
  repeated ReviewTopic reviewTopics = 7;
}

message ReviewTopic {
  // ID of the review topic
  string id = 1;
  // Keyword of the review topic
  string keyword = 2;
  // Mentions of the review topic
  int32 mentions = 3;
}

// Requests

message CreateReviewableRequest {
  string businessId = 1;
  string googlePlacesId = 2;
  optional string description = 3;
  optional bool isSelf = 4;
}

message UpdateReviewableRequest {
  string reviewableId = 1;
  optional string description = 2;
  optional bool isSelf = 3;
}

// Responses

message ReviewablesResponse {
  int32 status = 1;
  optional Error error = 2;
  repeated Reviewable data = 3;
}

message ReviewableResponse {
  int32 status = 1;
  optional Error error = 2;
  Reviewable data = 3;
}