syntax = "proto3";

package reporting;

option go_package = "./";

import "proto/reporting/shared.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

service CopilotService {
    rpc QueryCopilot(QueryCopilotRequest) returns (QueryCopilotResponse) {}
}

// Requests

message QueryCopilotRequest {
    string query = 1;
    string businessId = 2;
    string sessionId = 3;
    optional string metadata = 4;
}

// Responses

message QueryCopilotResponse {
    int32 status = 1;
    Error error = 2;
    CopilotAnswer response = 3;
}

message CopilotAnswer {
    string message = 1;
    // 'text | 'sales' | 'product' | 'schedule' | 'labor' | 'staff' | 'sales-by-staff'
    string UI = 2;
    google.protobuf.Struct data = 3;
    // 'regular' | 'comparison' | 'tabular' | 'follow-up'
    string UIType = 4;
    int32 totalTokens = 5;
    repeated string followUpQuestions = 6;
}
