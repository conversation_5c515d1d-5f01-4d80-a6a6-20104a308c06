syntax = "proto3";

package reporting;

option go_package = "./";

import "proto/reporting/shared.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

service CopilotService {
    rpc QueryCopilot(QueryCopilotRequest) returns (QueryCopilotResponse) {}
}

// Requests

message TimeContext {
    // 'relative' | 'absolute' | 'none'
    string type = 1;
    string start_date = 2;
    string end_date = 3;
}

message Information {
    string type = 1;
    string name = 2;
}

message SchemaInfo {
    repeated string ctes = 1;
    TimeContext time_context = 2;
    // 'sales' | 'staff' | 'labor' | 'inventory' | 'menu' | 'cross_domain'
    string query_type = 3;
    repeated Information options = 4;
    bool has_missing_information = 5;
    repeated Information missing_information = 6;
    repeated Information items_to_query = 7;
}

message Context {
    SchemaInfo schemaInfo = 1;
    google.protobuf.Struct decomposition = 2;
    string SQLQuery = 3;
    google.protobuf.Struct validation = 4;
}

message HistoricalChat {
    string query = 1;
    string businessId = 2;
    CopilotAnswer answer = 3;
    google.protobuf.Timestamp createdAt = 4;
    Context context = 5;	
}

message QueryCopilotRequest {
    string query = 1;
    string businessId = 2;
    repeated HistoricalChat historicalChat = 3;
    optional string metadata = 4;
}

// Responses

message QueryCopilotResponse {
    int32 status = 1;
    Error error = 2;
    CopilotAnswerWithHistoricalChat response = 3;
}

message CopilotAnswer {
    string message = 1;
    // 'text | 'sales' | 'product' | 'schedule' | 'labor' | 'staff' | 'sales-by-staff'
    string UI = 2;
    google.protobuf.Struct data = 3;
    // 'regular' | 'comparison' | 'tabular' | 'follow-up'
    string UIType = 4;
    int32 totalTokens = 5;
    repeated string followUpQuestions = 6;
}

message CopilotAnswerWithHistoricalChat {
    CopilotAnswer answer = 1;
    repeated HistoricalChat historicalChat = 2;
}
