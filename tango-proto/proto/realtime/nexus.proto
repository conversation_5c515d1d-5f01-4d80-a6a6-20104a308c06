syntax = 'proto3';

package realtime;

option go_package = "./";

service RealtimeNexusService {
  rpc SendPushNotification(PushNotificationMessage) returns (GenericResponse) {}
}

message PushNotificationMessage {
  string title = 1;
  string body = 2;
  string fcmToken = 3;
  // 'insight'
  optional string type = 4;
  // uuid of the insight
  optional string id = 5;
}

message Error {
  string type = 1;
  string message = 2;
}

message GenericResponse {
  int32 status = 1;
  optional Error error = 2;
}